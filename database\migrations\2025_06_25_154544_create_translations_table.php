<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('translations', function (Blueprint $table) {
            $table->id();
            $table->string('translatable_type'); // Model class name
            $table->unsignedBigInteger('translatable_id'); // Model ID
            $table->foreignId('language_id')->constrained('languages')->onDelete('cascade');
            $table->string('field_name'); // Field being translated
            $table->text('field_value'); // Translated content
            $table->timestamps();

            // Indexes
            $table->index(['translatable_type', 'translatable_id']);
            $table->index(['language_id', 'field_name']);
            $table->unique(['translatable_type', 'translatable_id', 'language_id', 'field_name'], 'translations_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('translations');
    }
};
