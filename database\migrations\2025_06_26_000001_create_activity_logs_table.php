<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('activity_logs', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            
            // User Information
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->string('user_email')->nullable(); // Store email even if user is deleted
            $table->string('user_name')->nullable(); // Store name for reference
            
            // Activity Details
            $table->string('activity_type'); // password_reset_request, password_reset_success, login_attempt, etc.
            $table->string('activity_description');
            $table->string('status'); // success, failed, pending, blocked
            $table->text('failure_reason')->nullable(); // Why it failed (rate limited, invalid email, etc.)
            
            // Request Information
            $table->string('ip_address', 45);
            $table->text('user_agent');
            $table->string('device_type')->nullable(); // mobile, desktop, tablet
            $table->string('browser')->nullable(); // Chrome, Firefox, Safari, etc.
            $table->string('platform')->nullable(); // Windows, macOS, Linux, iOS, Android
            
            // Location Information (if available)
            $table->string('country')->nullable();
            $table->string('region')->nullable();
            $table->string('city')->nullable();
            $table->decimal('latitude', 10, 8)->nullable();
            $table->decimal('longitude', 11, 8)->nullable();
            
            // Request Context
            $table->string('url');
            $table->string('method', 10); // GET, POST, PUT, DELETE
            $table->json('request_data')->nullable(); // Sanitized request data
            $table->json('response_data')->nullable(); // Response information
            $table->string('session_id')->nullable();
            $table->string('request_id')->nullable(); // For tracking related requests
            
            // Security Information
            $table->boolean('is_suspicious')->default(false);
            $table->text('security_notes')->nullable();
            $table->integer('risk_score')->default(0); // 0-100 risk assessment
            
            // Timestamps
            $table->timestamp('occurred_at');
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['user_id', 'activity_type', 'occurred_at']);
            $table->index(['user_email', 'activity_type', 'occurred_at']);
            $table->index(['ip_address', 'occurred_at']);
            $table->index(['activity_type', 'status', 'occurred_at']);
            $table->index(['is_suspicious', 'occurred_at']);
            $table->index(['occurred_at']);
            $table->index('uuid');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('activity_logs');
    }
};
