@extends('layouts.dashboard')

@section('title', 'Product Details - Admin Dashboard')
@section('page_title', 'Product Details')

@section('content')
<div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">{{ $product->name }}</h1>
            <p class="text-gray-600">Product details and management</p>
        </div>
        <div class="flex items-center space-x-3">
            <a href="{{ route('admin.products.index') }}" 
               class="inline-flex items-center px-4 py-2 bg-neutral-600 text-white rounded-lg hover:bg-neutral-700 transition-colors duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                </svg>
                Back to Products
            </a>
            <a href="{{ route('admin.products.edit', $product) }}" 
               class="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                </svg>
                Edit Product
            </a>
        </div>
    </div>

    <!-- Product Information -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Details -->
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100 overflow-hidden">
                <div class="px-6 py-4 border-b border-neutral-200">
                    <h3 class="text-lg font-semibold text-gray-900">Basic Information</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Product Name</label>
                            <div class="text-sm text-gray-900">{{ $product->name }}</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">SKU</label>
                            <div class="text-sm text-gray-900 font-mono bg-gray-50 px-2 py-1 rounded">{{ $product->sku }}</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Slug</label>
                            <div class="text-sm text-gray-900 font-mono bg-gray-50 px-2 py-1 rounded">{{ $product->slug }}</div>
                        </div>
                        @if($product->barcode)
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Barcode</label>
                                <div class="text-sm text-gray-900 font-mono">{{ $product->barcode }}</div>
                            </div>
                        @endif
                        @if($product->brand)
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Brand</label>
                                <div class="text-sm text-gray-900">{{ $product->brand }}</div>
                            </div>
                        @endif
                        @if($product->model_number)
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Model Number</label>
                                <div class="text-sm text-gray-900">{{ $product->model_number }}</div>
                            </div>
                        @endif
                    </div>
                    
                    @if($product->short_description)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Short Description</label>
                            <div class="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg prose prose-sm max-w-none">
                                {!! $product->short_description !!}
                            </div>
                        </div>
                    @endif

                    @if($product->description)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Full Description</label>
                            <div class="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg max-h-32 overflow-y-auto prose prose-sm max-w-none">
                                {!! $product->description !!}
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Pricing & Inventory -->
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100 overflow-hidden">
                <div class="px-6 py-4 border-b border-neutral-200">
                    <h3 class="text-lg font-semibold text-gray-900">Pricing & Inventory</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Price</label>
                            <div class="text-lg font-semibold text-gray-900">R{{ number_format($product->price, 2) }}</div>
                        </div>
                        @if($product->compare_price)
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Compare Price</label>
                                <div class="text-sm text-gray-500 line-through">R{{ number_format($product->compare_price, 2) }}</div>
                            </div>
                        @endif
                        @if($product->cost_price)
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Cost Price</label>
                                <div class="text-sm text-gray-900">R{{ number_format($product->cost_price, 2) }}</div>
                            </div>
                        @endif
                    </div>
                    
                    @if($product->track_inventory)
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t border-neutral-200">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Inventory Quantity</label>
                                <div class="text-sm text-gray-900">
                                    {{ $product->inventory_quantity }} units
                                    @if($product->inventory_quantity <= $product->low_stock_threshold)
                                        <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            Low Stock
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Low Stock Threshold</label>
                                <div class="text-sm text-gray-900">{{ $product->low_stock_threshold }} units</div>
                            </div>
                        </div>
                    @else
                        <div class="pt-4 border-t border-neutral-200">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                Inventory Not Tracked
                            </span>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Categories -->
            @if($product->categories->count() > 0)
                <div class="bg-white rounded-xl shadow-soft border border-neutral-100 overflow-hidden">
                    <div class="px-6 py-4 border-b border-neutral-200">
                        <h3 class="text-lg font-semibold text-gray-900">Categories</h3>
                    </div>
                    <div class="p-6">
                        <div class="flex flex-wrap gap-2">
                            @foreach($product->categories as $category)
                                <a href="{{ route('admin.categories.show', $category) }}" 
                                   class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-100 text-primary-800 hover:bg-primary-200 transition-colors duration-200">
                                    {{ $category->name }}
                                </a>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif

            <!-- SEO Information -->
            @if($product->meta_title || $product->meta_description || $product->meta_keywords)
                <div class="bg-white rounded-xl shadow-soft border border-neutral-100 overflow-hidden">
                    <div class="px-6 py-4 border-b border-neutral-200">
                        <h3 class="text-lg font-semibold text-gray-900">SEO Information</h3>
                    </div>
                    <div class="p-6 space-y-4">
                        @if($product->meta_title)
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Meta Title</label>
                                <div class="text-sm text-gray-900">{{ $product->meta_title }}</div>
                            </div>
                        @endif
                        @if($product->meta_description)
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Meta Description</label>
                                <div class="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{{ $product->meta_description }}</div>
                            </div>
                        @endif
                        @if($product->meta_keywords)
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Meta Keywords</label>
                                <div class="text-sm text-gray-900">{{ $product->meta_keywords }}</div>
                            </div>
                        @endif
                    </div>
                </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Product Images -->
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100 overflow-hidden">
                <div class="px-6 py-4 border-b border-neutral-200">
                    <h3 class="text-lg font-semibold text-gray-900">Product Images</h3>
                </div>
                <div class="p-6">
                    @if($product->featured_image)
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Featured Image</label>
                            <img src="{{ $product->featured_image_url }}"
                                 alt="{{ $product->name }}"
                                 class="w-full h-48 object-cover rounded-lg">
                        </div>
                    @endif
                    
                    @if($product->gallery && count($product->gallery) > 0)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Gallery Images</label>
                            <div class="grid grid-cols-2 gap-2">
                                @foreach($product->gallery as $image)
                                    <img src="{{ asset('storage/' . $image) }}" 
                                         alt="{{ $product->name }}" 
                                         class="w-full h-24 object-cover rounded-lg">
                                @endforeach
                            </div>
                        </div>
                    @endif
                    
                    @if(!$product->featured_image && (!$product->gallery || count($product->gallery) === 0))
                        <div class="w-full h-48 bg-gradient-to-br from-primary-100 to-primary-200 rounded-lg flex items-center justify-center">
                            <div class="text-center">
                                <svg class="w-12 h-12 text-primary-600 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>
                                </svg>
                                <p class="text-sm text-primary-600">No images uploaded</p>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Status & Stats -->
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100 overflow-hidden">
                <div class="px-6 py-4 border-b border-neutral-200">
                    <h3 class="text-lg font-semibold text-gray-900">Status & Statistics</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">Status</span>
                        @if($product->is_active)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Active
                            </span>
                        @else
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Inactive
                            </span>
                        @endif
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">Featured</span>
                        @if($product->is_featured)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                <svg class="w-3 h-3 mr-1 fill-current" viewBox="0 0 24 24">
                                    <path d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"/>
                                </svg>
                                Featured
                            </span>
                        @else
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                Not Featured
                            </span>
                        @endif
                    </div>
                    
                    @if($product->weight)
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-700">Weight</span>
                            <span class="text-sm text-gray-900">{{ $product->weight }}g</span>
                        </div>
                    @endif
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">Categories</span>
                        <span class="text-sm font-semibold text-gray-900">{{ $product->categories->count() }}</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">Created</span>
                        <span class="text-sm text-gray-900">{{ $product->created_at->format('M j, Y') }}</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">Last Updated</span>
                        <span class="text-sm text-gray-900">{{ $product->updated_at->diffForHumans() }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
