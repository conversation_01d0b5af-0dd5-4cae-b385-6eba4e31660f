<?php

namespace Tests\Unit;

use App\Models\Job;
use App\Models\JobApplication;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class JobApplicationTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test job application creation with required fields.
     */
    public function test_job_application_can_be_created_with_required_fields(): void
    {
        $job = Job::create([
            'title' => 'Test Job',
            'description' => 'Test description',
            'requirements' => 'Test requirements',
            'responsibilities' => 'Test responsibilities',
            'location' => 'Test Location',
            'employment_type' => 'full-time',
            'experience_level' => 'entry',
            'department' => 'Test',
            'salary_period' => 'monthly',
        ]);

        $application = JobApplication::create([
            'job_id' => $job->id,
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'phone' => '123456789',
            'country' => 'South Africa',
            'cover_letter' => 'Test cover letter',
            'experience_summary' => 'Test experience',
            'highest_qualification' => 'Bachelor Degree',
        ]);

        $this->assertDatabaseHas('job_applications', [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
        ]);

        $this->assertNotNull($application->uuid);
        $this->assertNotNull($application->reference_number);
    }

    /**
     * Test job application reference number generation.
     */
    public function test_job_application_reference_number_is_generated_automatically(): void
    {
        $job = Job::create([
            'title' => 'Test Job',
            'description' => 'Test description',
            'requirements' => 'Test requirements',
            'responsibilities' => 'Test responsibilities',
            'location' => 'Test Location',
            'employment_type' => 'full-time',
            'experience_level' => 'entry',
            'department' => 'Test',
            'salary_period' => 'monthly',
        ]);

        $application = JobApplication::create([
            'job_id' => $job->id,
            'first_name' => 'Bob',
            'last_name' => 'Johnson',
            'email' => '<EMAIL>',
            'phone' => '555123456',
            'country' => 'South Africa',
            'cover_letter' => 'Test cover letter',
            'experience_summary' => 'Test experience',
            'highest_qualification' => 'Diploma',
        ]);

        $this->assertNotNull($application->reference_number);
        $this->assertStringStartsWith('JOB-', $application->reference_number);
        $this->assertEquals(12, strlen($application->reference_number)); // JOB- + 8 characters
    }

    /**
     * Test full name attribute.
     */
    public function test_full_name_attribute(): void
    {
        $job = Job::create([
            'title' => 'Test Job',
            'description' => 'Test description',
            'requirements' => 'Test requirements',
            'responsibilities' => 'Test responsibilities',
            'location' => 'Test Location',
            'employment_type' => 'full-time',
            'experience_level' => 'entry',
            'department' => 'Test',
            'salary_period' => 'monthly',
        ]);

        $application = JobApplication::create([
            'job_id' => $job->id,
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'phone' => '123456789',
            'country' => 'South Africa',
            'cover_letter' => 'Test cover letter',
            'experience_summary' => 'Test experience',
            'highest_qualification' => 'Bachelor Degree',
        ]);

        $this->assertEquals('John Doe', $application->full_name);
    }

    /**
     * Test status options.
     */
    public function test_status_options(): void
    {
        $options = JobApplication::getStatusOptions();

        $this->assertIsArray($options);
        $this->assertArrayHasKey('pending', $options);
        $this->assertArrayHasKey('reviewing', $options);
        $this->assertArrayHasKey('shortlisted', $options);
        $this->assertArrayHasKey('interviewed', $options);
        $this->assertArrayHasKey('offered', $options);
        $this->assertArrayHasKey('hired', $options);
        $this->assertArrayHasKey('rejected', $options);
    }

    /**
     * Test default status is pending.
     */
    public function test_default_status_is_pending(): void
    {
        $job = Job::create([
            'title' => 'Test Job',
            'description' => 'Test description',
            'requirements' => 'Test requirements',
            'responsibilities' => 'Test responsibilities',
            'location' => 'Test Location',
            'employment_type' => 'full-time',
            'experience_level' => 'entry',
            'department' => 'Test',
            'salary_period' => 'monthly',
        ]);

        $application = JobApplication::create([
            'job_id' => $job->id,
            'first_name' => 'Test',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'phone' => '123456789',
            'country' => 'South Africa',
            'cover_letter' => 'Test cover letter',
            'experience_summary' => 'Test experience',
            'highest_qualification' => 'Bachelor Degree',
        ]);

        $this->assertEquals('pending', $application->status);
    }
}
