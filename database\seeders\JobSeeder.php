<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Job;

class JobSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $jobs = [
            [
                'title' => 'Senior Full Stack Developer',
                'description' => 'We are looking for an experienced Full Stack Developer to join our dynamic team. You will be responsible for developing and maintaining web applications using modern technologies like Laravel, React, and Vue.js. This role offers excellent opportunities for growth and learning in a collaborative environment.',
                'requirements' => "• Bachelor's degree in Computer Science or related field\n• 5+ years of experience in web development\n• Proficiency in PHP, Laravel, JavaScript, React/Vue.js\n• Experience with MySQL/PostgreSQL databases\n• Knowledge of Git version control\n• Understanding of RESTful APIs\n• Experience with cloud platforms (AWS/Azure preferred)",
                'responsibilities' => "• Develop and maintain web applications using Laravel and modern JavaScript frameworks\n• Collaborate with cross-functional teams including designers and product managers\n• Write clean, maintainable, and well-documented code\n• Participate in code reviews and technical discussions\n• Optimize applications for maximum speed and scalability\n• Troubleshoot and debug applications\n• Stay up-to-date with emerging technologies",
                'location' => 'Cape Town',
                'employment_type' => 'full-time',
                'experience_level' => 'senior',
                'department' => 'Engineering',
                'salary_min' => 45000,
                'salary_max' => 65000,
                'salary_currency' => 'ZAR',
                'salary_period' => 'monthly',
                'benefits' => "• Competitive salary with annual reviews\n• Health and dental insurance\n• Flexible working hours\n• Remote work options\n• Professional development budget (R15,000/year)\n• Modern equipment and tools\n• Team building events",
                'is_featured' => true,
                'is_active' => true,
                'is_remote' => true,
                'sort_order' => 1
            ],
            [
                'title' => 'Digital Marketing Specialist',
                'description' => 'Join our marketing team as a Digital Marketing Specialist. You will be responsible for developing and executing comprehensive digital marketing campaigns across multiple channels to drive brand awareness and customer acquisition.',
                'requirements' => "• Bachelor's degree in Marketing, Communications, or related field\n• 3+ years of experience in digital marketing\n• Proficiency in Google Analytics, Google Ads, Facebook Ads Manager\n• Knowledge of SEO/SEM best practices\n• Experience with email marketing platforms\n• Strong analytical and communication skills\n• Creative thinking and problem-solving abilities",
                'responsibilities' => "• Plan and execute digital marketing campaigns across various channels\n• Manage social media accounts and create engaging content\n• Analyze campaign performance and provide actionable insights\n• Optimize website content for search engines\n• Create and manage email marketing campaigns\n• Collaborate with design team on marketing materials\n• Monitor industry trends and competitor activities",
                'location' => 'Johannesburg',
                'employment_type' => 'full-time',
                'experience_level' => 'mid',
                'department' => 'Marketing',
                'salary_min' => 25000,
                'salary_max' => 35000,
                'salary_currency' => 'ZAR',
                'salary_period' => 'monthly',
                'benefits' => "• Health insurance\n• Performance-based bonuses\n• Training and certification opportunities\n• Flexible working arrangements\n• Career advancement opportunities",
                'is_featured' => false,
                'is_active' => true,
                'is_remote' => false,
                'sort_order' => 2
            ],
            [
                'title' => 'UI/UX Designer',
                'description' => 'We are seeking a talented UI/UX Designer to create amazing user experiences. You will be responsible for designing intuitive and visually appealing interfaces for web and mobile applications.',
                'requirements' => "• Bachelor's degree in Design, HCI, or related field\n• 3+ years of experience in UI/UX design\n• Proficiency in Figma, Sketch, Adobe Creative Suite\n• Strong portfolio demonstrating design skills\n• Understanding of user-centered design principles\n• Knowledge of HTML/CSS is a plus\n• Experience with prototyping tools",
                'responsibilities' => "• Create wireframes, prototypes, and high-fidelity designs\n• Conduct user research and usability testing\n• Collaborate with developers to ensure design implementation\n• Maintain design systems and style guides\n• Present design concepts to stakeholders\n• Stay current with design trends and best practices",
                'location' => 'Cape Town',
                'employment_type' => 'full-time',
                'experience_level' => 'mid',
                'department' => 'Design',
                'salary_min' => 30000,
                'salary_max' => 45000,
                'salary_currency' => 'ZAR',
                'salary_period' => 'monthly',
                'benefits' => "• Creative freedom and autonomy\n• Latest design tools and software\n• Conference and workshop attendance\n• Flexible working hours\n• Health insurance",
                'is_featured' => false,
                'is_active' => true,
                'is_remote' => true,
                'sort_order' => 3
            ],
            [
                'title' => 'Data Analyst Intern',
                'description' => 'Join our data team as an intern and gain hands-on experience in data analysis and business intelligence. This is a great opportunity for students or recent graduates to start their career in data analytics.',
                'requirements' => "• Currently pursuing or recently completed degree in Statistics, Mathematics, Computer Science, or related field\n• Basic knowledge of SQL and Excel\n• Familiarity with Python or R is a plus\n• Strong analytical and problem-solving skills\n• Excellent communication skills\n• Eagerness to learn and grow",
                'responsibilities' => "• Assist in data collection and cleaning processes\n• Create basic reports and visualizations\n• Support senior analysts with research projects\n• Learn to use business intelligence tools\n• Participate in team meetings and training sessions\n• Document processes and findings",
                'location' => 'Durban',
                'employment_type' => 'internship',
                'experience_level' => 'entry',
                'department' => 'Data Analytics',
                'salary_min' => 8000,
                'salary_max' => 12000,
                'salary_currency' => 'ZAR',
                'salary_period' => 'monthly',
                'benefits' => "• Mentorship from experienced data professionals\n• Training in industry-standard tools\n• Potential for full-time employment\n• Flexible schedule for students\n• Certificate of completion",
                'is_featured' => false,
                'is_active' => true,
                'is_remote' => false,
                'sort_order' => 4,
                'application_deadline' => now()->addDays(30)
            ]
        ];

        foreach ($jobs as $jobData) {
            Job::create($jobData);
        }
    }
}
