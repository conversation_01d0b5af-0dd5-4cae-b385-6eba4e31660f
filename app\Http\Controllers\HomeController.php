<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Project;
use App\Models\Service;
use App\Models\BlogPost;
use App\Models\ContactSubmission;
use App\Services\FileService;
use App\Services\ImageService;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Support\Facades\Log;

class HomeController extends Controller
{
    /**
     * Show the application homepage.
     */
    public function index(): View
    {
        // Get featured products
        $featuredProducts = Product::active()
            ->featured()
            ->with(['categories', 'variants'])
            ->limit(8)
            ->get();

        // Get featured services
        $featuredServices = Service::active()
            ->featured()
            ->ordered()
            ->limit(6)
            ->get();

        // Get featured projects
        $featuredProjects = Project::where('is_published', true)
            ->where('is_featured', true)
            ->where('is_deleted', false)
            ->with(['client', 'service'])
            ->limit(6)
            ->get();

        // Get latest blog posts
        $latestBlogPosts = BlogPost::where('is_published', true)
            ->where('is_deleted', false)
            ->with(['category', 'author'])
            ->orderBy('published_at', 'desc')
            ->limit(3)
            ->get();

        return view('pages.home', compact(
            'featuredProducts',
            'featuredServices', 
            'featuredProjects',
            'latestBlogPosts'
        ));
    }

    /**
     * Show the about page.
     */
    public function about(): View
    {
        return view('pages.about');
    }

    /**
     * Show the contact page.
     */
    public function contact(): View
    {
        return view('pages.contact');
    }

    /**
     * Handle contact form submission.
     */
    public function contactSubmit(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:200',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'company' => 'nullable|string|max:200',
            'service' => 'nullable|string|max:100',
            'subject' => 'nullable|string|max:300',
            'message' => 'required|string|max:5000',
            'attachments' => 'nullable|array|max:10',
            'attachments.*' => 'file|max:25600', // 25MB max per file
        ]);

        // Process file attachments if any
        $attachmentData = [];
        if ($request->hasFile('attachments')) {
            foreach ($request->file('attachments') as $file) {
                try {
                    // Use FileService for documents and ImageService for images
                    $mimeType = $file->getMimeType();

                    if (str_starts_with($mimeType, 'image/')) {
                        // Process image files with ImageService
                        $result = app(ImageService::class)->processUploadedFile($file, [
                            'folder' => 'contact-submissions/' . date('Y/m'),
                            'optimize' => true,
                            'convert_to_webp' => true,
                            'scan_for_viruses' => true,
                        ]);
                    } else {
                        // Process document files with FileService
                        $result = app(FileService::class)->processUploadedFile($file, [
                            'folder' => 'contact-submissions/' . date('Y/m'),
                            'scan_for_viruses' => true,
                            'scan_content' => true,
                        ]);
                    }

                    if ($result['success']) {
                        $attachmentData[] = [
                            'original_name' => $file->getClientOriginalName(),
                            'stored_name' => $result['filename'],
                            'path' => $result['path'],
                            'size' => $file->getSize(),
                            'mime_type' => $mimeType,
                            'scan_results' => $result['scan_results'] ?? null,
                        ];
                    } else {
                        Log::warning('File upload failed for contact submission', [
                            'file' => $file->getClientOriginalName(),
                            'error' => $result['message'],
                            'email' => $validated['email']
                        ]);
                    }
                } catch (\Exception $e) {
                    Log::error('Error processing contact form attachment', [
                        'file' => $file->getClientOriginalName(),
                        'error' => $e->getMessage(),
                        'email' => $validated['email']
                    ]);
                }
            }
        }

        // Add additional data
        $validated['ip_address'] = $request->ip();
        $validated['user_agent'] = $request->userAgent();
        $validated['referrer'] = $request->header('referer');
        $validated['attachments'] = $attachmentData;

        // Create contact submission
        ContactSubmission::create($validated);

        // Send notification email (implement as needed)
        // Mail::to(config('mail.contact_email'))->send(new ContactFormSubmitted($validated));

        $message = 'Thank you for your message! We will get back to you soon.';
        if (!empty($attachmentData)) {
            $fileCount = count($attachmentData);
            $message .= " Your {$fileCount} file(s) have been securely uploaded and scanned.";
        }

        return redirect()->back()->with('success', $message);
    }

    /**
     * Show the services page.
     */
    public function services(): View
    {
        $services = Service::active()
            ->ordered()
            ->get();

        return view('pages.services', compact('services'));
    }

    /**
     * Show a specific service.
     */
    public function service(Service $service): View
    {
        // Get related projects
        $relatedProjects = $service->publishedProjects()
            ->limit(6)
            ->get();

        return view('pages.service', compact('service', 'relatedProjects'));
    }

    /**
     * Show the portfolio/projects page.
     */
    public function portfolio(): View
    {
        $projects = Project::where('is_published', true)
            ->where('is_deleted', false)
            ->with(['client', 'service'])
            ->orderBy('is_featured', 'desc')
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        return view('pages.portfolio', compact('projects'));
    }

    /**
     * Show a specific project.
     */
    public function project(Project $project): View
    {
        // Get related projects
        $relatedProjects = Project::where('is_published', true)
            ->where('is_deleted', false)
            ->where('id', '!=', $project->id)
            ->limit(3)
            ->get();

        return view('pages.project', compact('project', 'relatedProjects'));
    }

    /**
     * Show the blog page.
     */
    public function blog(): View
    {
        $posts = BlogPost::where('is_published', true)
            ->where('is_deleted', false)
            ->with(['category', 'author'])
            ->orderBy('published_at', 'desc')
            ->paginate(10);

        return view('pages.blog', compact('posts'));
    }

    /**
     * Show a specific blog post.
     */
    public function blogPost(BlogPost $post): View
    {
        // Get related posts
        $relatedPosts = BlogPost::where('is_published', true)
            ->where('is_deleted', false)
            ->where('id', '!=', $post->id)
            ->where('category_id', $post->category_id)
            ->limit(3)
            ->get();

        return view('pages.blog-post', compact('post', 'relatedPosts'));
    }
}
