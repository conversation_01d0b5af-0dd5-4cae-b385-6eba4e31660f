<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Job extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'career_jobs';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'uuid',
        'title',
        'slug',
        'description',
        'requirements',
        'responsibilities',
        'location',
        'employment_type',
        'experience_level',
        'department',
        'salary_min',
        'salary_max',
        'salary_currency',
        'salary_period',
        'benefits',
        'application_deadline',
        'is_remote',
        'is_featured',
        'is_active',
        'is_deleted',
        'sort_order',
        'meta_title',
        'meta_description',
        'meta_keywords',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'salary_min' => 'decimal:2',
        'salary_max' => 'decimal:2',
        'application_deadline' => 'date',
        'is_remote' => 'boolean',
        'is_featured' => 'boolean',
        'is_active' => 'boolean',
        'is_deleted' => 'boolean',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($job) {
            if (empty($job->uuid)) {
                $job->uuid = (string) Str::uuid();
            }
            if (empty($job->slug)) {
                $job->slug = Str::slug($job->title);
            }
        });
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Scope a query to only include active jobs.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)->where('is_deleted', false);
    }

    /**
     * Scope a query to only include featured jobs.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope a query to order jobs.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('is_featured', 'desc')
                    ->orderBy('sort_order', 'asc')
                    ->orderBy('created_at', 'desc');
    }

    /**
     * Get the job applications for this job.
     */
    public function jobApplications(): HasMany
    {
        return $this->hasMany(JobApplication::class);
    }

    /**
     * Get employment type options.
     */
    public static function getEmploymentTypeOptions(): array
    {
        return [
            'full-time' => 'Full Time',
            'part-time' => 'Part Time',
            'contract' => 'Contract',
            'internship' => 'Internship',
            'freelance' => 'Freelance',
        ];
    }

    /**
     * Get experience level options.
     */
    public static function getExperienceLevelOptions(): array
    {
        return [
            'entry' => 'Entry Level',
            'mid' => 'Mid Level',
            'senior' => 'Senior Level',
            'executive' => 'Executive',
        ];
    }

    /**
     * Get salary period options.
     */
    public static function getSalaryPeriodOptions(): array
    {
        return [
            'hourly' => 'Per Hour',
            'monthly' => 'Per Month',
            'annually' => 'Per Year',
        ];
    }

    /**
     * Get formatted salary range.
     */
    public function getFormattedSalaryAttribute(): string
    {
        if (!$this->salary_min && !$this->salary_max) {
            return 'Competitive';
        }

        $currency = $this->salary_currency;
        $period = $this->getSalaryPeriodOptions()[$this->salary_period] ?? '';

        if ($this->salary_min && $this->salary_max) {
            return "{$currency} " . number_format($this->salary_min) . " - " . number_format($this->salary_max) . " {$period}";
        } elseif ($this->salary_min) {
            return "{$currency} " . number_format($this->salary_min) . "+ {$period}";
        } else {
            return "{$currency} " . number_format($this->salary_max) . " {$period}";
        }
    }
}
