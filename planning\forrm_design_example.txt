-----html
<div class="modal__background">
    <div class="modal__window">

        <form class="auth-form" name="form-auth" method="post">

            <label class="auth-form__label">
                <span class="auth-form__placeholder">email</span>
                <input class="auth-form__input input-email" type="email" name="email" autocomplete="off" required>
            </label>

            <label class="auth-form__label">
                <span class="auth-form__placeholder">password</span>
                <input class="auth-form__input input-password" type="password" name="password" autocomlete="off" required>
                <div class="auth-form__toggler">
                    <i class="la la-eye auth-form__icon"></i>
                    <input type="checkbox" class="auth-form__checkbox password-toggler">
                </div>
            </label>

            <div class="auth-form__answer"></div>

            <input class="auth-form__submit" type="submit" value="Login">
            
            <div class="auth-form__bottom">
                <span>Have no account?</span>
                <a href="#">Create new</a>
            </div>
        </form>

    </div>
</div>

<div class="hint"></div>


-----css
@import 'nib'

*
    margin 0

html, body
    min-height 100vh

body
    background #232323
    color #fff
    font-family Helvetica, Arial, sans-serif


.text-success
    color #28a745

.text-danger
    color #dc3545

.modal__background
    height 100vh
    display flex
    justify-content center
    align-items center

.modal__window
    box-sizing border-box
    padding 29px
    border 1px solid #999
    border-radius 15px

@media all and (min-width 576px)
    .modal__window
        padding 49px 79px 29px 

.auth-form
    display flex
    flex-direction column
    letter-spacing 1px

.auth-form__label
    margin-bottom 40px
    position relative

.auth-form__placeholder
    position absolute
    color #999
    cursor text
    font-size 16px
    top 9px
    transition all .2s

    &.focus
        top -12px
        font-size 12px

.auth-form__input
    position relative
    width 240px
    height 40px
    background transparent
    color #ccc
    box-sizing border-box
    font-size 16px
    outline none
    border-top none
    border-left none
    border-right none
    border-bottom none
    box-shadow 0 1px 0 0 #666

    &.focus
        box-shadow 0 2px 0 0 #bbb
    
.auth-form__toggler
.auth-form__icon
.auth-form__checkbox
    position absolute
    width 18px
    height 18px

.auth-form__checkbox
    cursor inherit

.auth-form__toggler
    cursor pointer
    right 2px
    top 50%
    transform translateY(-50%)

.auth-form__icon
    color #666
    
.auth-form__toggler:hover .auth-form__icon
    color #ccc

.password-toggler
    opacity 0
    box-sizing border-box
    border none

.auth-form__answer
    height 12px
    padding-top 4px
    padding-bottom 4px
    font-size 14px
    text-align center
    margin-top -10px
    margin-bottom 30px

.auth-form__submit
    font-family inherit 
    font-size inherit
    letter-spacing inherit
    cursor pointer
    border 1px solid #ccc
    height 50px
    border-radius 10px
    background transparent
    color #ccc
    outline none

    &:hover
        background #fff
        border 1px solid #fff
        color #232323
        
        
.auth-form__bottom
    margin-top 30px
    font-size 12px
    color #ddd
    text-align center

    a
        color #007bff

        &:hover
            color #ddd

        
        
.hint
    font-size 12px
    position absolute
    top 10px
    left 10px
    

------- js
window.onload = function() {
    (function() {
        const inputText = document.querySelectorAll('.auth-form__input');

        inputText.forEach( function(input) {
            input.addEventListener('focus', function() {
                this.classList.add('focus');
                this.parentElement.querySelector('.auth-form__placeholder').classList.add('focus');
            });
            input.addEventListener('blur', function() {
                this.classList.remove('focus');
                if (! this.value) {
                    this.parentElement.querySelector('.auth-form__placeholder').classList.remove('focus');
                }
            });
        });
    })();

    (function() {
        const togglers = document.querySelectorAll('.password-toggler');

        togglers.forEach( function(checkbox) {
            checkbox.addEventListener('change', function() {

                const toggler = this.parentElement,
                      input   = toggler.parentElement.querySelector('.input-password'),
                      icon    = toggler.querySelector('.auth-form__icon');

                if (checkbox.checked) {
                    input.type = 'text';
                    icon.classList.remove('la-eye')
                    icon.classList.add('la-eye-slash');
                }

                else
                {
                    input.type = 'password';
                    icon.classList.remove('la-eye-slash')
                    icon.classList.add('la-eye');
                }
            });
        });
    })();

    (function() {
        const validEmail = '<EMAIL>',
              validPassword = 'qwerty123';
        
        document.body.querySelector('.hint')
                     .innerHTML = `<p>${validEmail}</p><p>${validPassword}</p>`;

        document.forms['form-auth'].addEventListener('submit', function(e) {
            e.preventDefault();

            const answerContainer = this.querySelector('.auth-form__answer'),
                  email = this.elements.email.value,
                  password = this.elements.password.value;

            const placeholders = document.querySelectorAll('.auth-form__placeholder');

            if (email == validEmail && password == validPassword) {
                answerContainer.innerHTML = '<span class="text-success">you\'ve been logged successfully</span>';
            }

            else {
                placeholders.forEach(function(placeholder) {
                    placeholder.classList.remove('focus');
                });
                this.elements.email.value = '';
                this.elements.password.value = '';
                answerContainer.innerHTML = '<span class="text-danger">invalid email or password</span>';
            }
        });
    })();
};