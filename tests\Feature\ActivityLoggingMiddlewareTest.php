<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Role;
use App\Models\ActivityLog;
use App\Models\ProjectApplication;
use App\Models\Service;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class ActivityLoggingMiddlewareTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $customer;
    protected Service $service;
    protected Role $customerRole;

    protected function setUp(): void
    {
        parent::setUp();

        // Create customer role
        $this->customerRole = Role::factory()->create([
            'name' => 'customer',
            'slug' => 'customer',
            'is_active' => true,
        ]);

        $this->customer = User::factory()->create([
            'role_id' => $this->customerRole->id,
            'email_verified_at' => now(),
        ]);

        $this->service = Service::factory()->create([
            'name' => 'Web Development',
            'is_active' => true,
        ]);

        Storage::fake('public');
    }

    /** @test */
    public function dashboard_access_is_logged()
    {
        $this->actingAs($this->customer)
            ->get(route('dashboard'))
            ->assertStatus(200);

        $this->assertDatabaseHas('activity_logs', [
            'user_email' => $this->customer->email,
            'activity_type' => 'customer_dashboard_access',
            'activity_description' => 'Accessed customer dashboard'
        ]);
    }

    /** @test */
    public function profile_update_is_logged()
    {
        $updateData = [
            'first_name' => 'Updated',
            'last_name' => 'Name',
            'phone' => '+1234567890'
        ];

        $this->actingAs($this->customer)
            ->put(route('customer.profile.update'), $updateData)
            ->assertRedirect();

        $log = ActivityLog::where('activity_type', 'customer_profile_update')->first();
        $this->assertNotNull($log);
        $this->assertEquals($this->customer->email, $log->user_email);
        $this->assertStringContains('Updated profile information', $log->description);
    }

    /** @test */
    public function project_application_submission_is_logged()
    {
        $applicationData = [
            'service_id' => $this->service->id,
            'title' => 'Test Project',
            'description' => 'Test project description',
            'budget_range' => '5000-10000',
            'timeline' => '3 months',
            'requirements' => 'Test requirements'
        ];

        $this->actingAs($this->customer)
            ->post(route('customer.project-applications.store'), $applicationData);

        $log = ActivityLog::where('activity_type', 'customer_project_application_create')->first();
        $this->assertNotNull($log);
        $this->assertEquals($this->customer->email, $log->user_email);
        $this->assertStringContains('Created new project application', $log->description);
    }

    /** @test */
    public function file_upload_is_logged_with_metadata()
    {
        $file = UploadedFile::fake()->create('test-document.pdf', 1024, 'application/pdf');
        
        $applicationData = [
            'service_id' => $this->service->id,
            'title' => 'Test Project with File',
            'description' => 'Test project description',
            'attachments' => [$file]
        ];

        $this->actingAs($this->customer)
            ->post(route('customer.project-applications.store'), $applicationData);

        $log = ActivityLog::where('activity_type', 'customer_project_application_create')->first();
        $this->assertNotNull($log);
        
        $requestData = $log->request_data;
        $this->assertArrayHasKey('files_uploaded', $requestData);
        $this->assertEquals(1, $requestData['files_uploaded']);
        $this->assertArrayHasKey('file_details', $requestData);
    }

    /** @test */
    public function failed_requests_are_logged_with_higher_risk()
    {
        // Attempt to access non-existent project application
        $this->actingAs($this->customer)
            ->get(route('customer.project-applications.show', 999))
            ->assertStatus(404);

        $log = ActivityLog::where('status', 'failed')->first();
        $this->assertNotNull($log);
        $this->assertGreaterThan(30, $log->risk_score); // Failed requests should have higher risk
    }

    /** @test */
    public function suspicious_activity_is_flagged()
    {
        // Simulate rapid successive requests (potential enumeration)
        for ($i = 0; $i < 5; $i++) {
            $this->actingAs($this->customer)
                ->get(route('customer.project-applications.show', 999 + $i));
        }

        $suspiciousLogs = ActivityLog::where('is_suspicious', true)->count();
        $this->assertGreaterThan(0, $suspiciousLogs);
    }

    /** @test */
    public function activity_logs_contain_proper_metadata()
    {
        $this->actingAs($this->customer)
            ->get(route('dashboard'));

        $log = ActivityLog::latest()->first();
        
        // Check required fields
        $this->assertNotNull($log->user_email);
        $this->assertNotNull($log->ip_address);
        $this->assertNotNull($log->user_agent);
        $this->assertNotNull($log->activity_type);
        $this->assertNotNull($log->description);
        $this->assertNotNull($log->occurred_at);
        $this->assertIsNumeric($log->risk_score);
        
        // Check metadata structure
        $this->assertIsArray($log->request_data);
        $this->assertArrayHasKey('method', $log->request_data);
        $this->assertArrayHasKey('url', $log->request_data);
        $this->assertArrayHasKey('processing_time_ms', $log->request_data);
    }

    /** @test */
    public function device_information_is_captured()
    {
        $this->actingAs($this->customer)
            ->withHeaders([
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            ])
            ->get(route('dashboard'));

        $log = ActivityLog::latest()->first();
        $this->assertNotNull($log->device_info);
        $this->assertIsArray($log->device_info);
        $this->assertArrayHasKey('device_type', $log->device_info);
        $this->assertArrayHasKey('browser', $log->device_info);
        $this->assertArrayHasKey('platform', $log->device_info);
    }

    /** @test */
    public function location_information_is_captured()
    {
        $this->actingAs($this->customer)
            ->withServerVariables(['REMOTE_ADDR' => '*******']) // Google DNS IP
            ->get(route('dashboard'));

        $log = ActivityLog::latest()->first();
        $this->assertNotNull($log->location_info);
        $this->assertIsArray($log->location_info);
    }

    /** @test */
    public function risk_scoring_works_correctly()
    {
        // Normal dashboard access should have low risk
        $this->actingAs($this->customer)
            ->get(route('dashboard'));

        $normalLog = ActivityLog::latest()->first();
        $this->assertLessThanOrEqual(20, $normalLog->risk_score);

        // Failed authentication should have higher risk
        $this->post(route('login'), [
            'email' => '<EMAIL>',
            'password' => 'wrongpassword'
        ]);

        // Multiple failed requests should increase risk
        for ($i = 0; $i < 3; $i++) {
            $this->actingAs($this->customer)
                ->get(route('customer.project-applications.show', 999 + $i));
        }

        $failedLogs = ActivityLog::where('status', 'failed')->get();
        $this->assertTrue($failedLogs->avg('risk_score') > 30);
    }

    /** @test */
    public function sensitive_data_is_sanitized()
    {
        $sensitiveData = [
            'first_name' => 'John',
            'password' => 'secret123',
            'password_confirmation' => 'secret123',
            'credit_card' => '****************'
        ];

        $this->actingAs($this->customer)
            ->put(route('customer.profile.update'), $sensitiveData);

        $log = ActivityLog::where('activity_type', 'customer_profile_update')->first();
        $requestData = $log->request_data;
        
        // Sensitive fields should be sanitized
        $this->assertArrayNotHasKey('password', $requestData);
        $this->assertArrayNotHasKey('password_confirmation', $requestData);
        $this->assertArrayNotHasKey('credit_card', $requestData);
        
        // Non-sensitive fields should remain
        $this->assertArrayHasKey('first_name', $requestData);
    }

    /** @test */
    public function middleware_handles_json_responses()
    {
        $this->actingAs($this->customer)
            ->getJson(route('dashboard'))
            ->assertStatus(200);

        $log = ActivityLog::latest()->first();
        $this->assertNotNull($log);
        $this->assertEquals('customer_dashboard_access', $log->activity_type);
    }

    /** @test */
    public function middleware_captures_response_status_codes()
    {
        // Successful request
        $this->actingAs($this->customer)
            ->get(route('dashboard'));

        $successLog = ActivityLog::latest()->first();
        $this->assertEquals('success', $successLog->status);
        $this->assertEquals(200, $successLog->request_data['response_status']);

        // Failed request
        $this->actingAs($this->customer)
            ->get(route('customer.project-applications.show', 999));

        $failedLog = ActivityLog::where('status', 'failed')->latest()->first();
        $this->assertEquals('failed', $failedLog->status);
        $this->assertEquals(404, $failedLog->request_data['response_status']);
    }

    /** @test */
    public function bulk_operations_are_logged_appropriately()
    {
        // Create multiple project applications
        $applications = ProjectApplication::factory()->count(3)->create([
            'user_id' => $this->customer->id,
            'service_id' => $this->service->id
        ]);

        // Access multiple applications (simulating bulk view)
        foreach ($applications as $application) {
            $this->actingAs($this->customer)
                ->get(route('customer.project-applications.show', $application));
        }

        $logs = ActivityLog::where('activity_type', 'customer_project_application_view')->get();
        $this->assertCount(3, $logs);
        
        // Each log should have appropriate metadata
        foreach ($logs as $log) {
            $this->assertNotNull($log->request_data);
            $this->assertArrayHasKey('project_application_id', $log->request_data);
        }
    }
}
