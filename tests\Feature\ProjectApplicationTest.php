<?php

namespace Tests\Feature;

use App\Models\ProjectApplication;
use App\Models\User;
use App\Models\Service;
use App\Services\FileService;
use App\Services\ImageService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class ProjectApplicationTest extends TestCase
{
    use RefreshDatabase;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        Storage::fake('public');

        // Seed roles for User factory
        $this->seed(\Database\Seeders\RoleSeeder::class);

        $this->user = User::factory()->create();
    }

    public function test_authenticated_user_can_view_project_applications_index()
    {
        $this->actingAs($this->user);

        $response = $this->get(route('project-applications.index'));

        $response->assertStatus(200);
        $response->assertViewIs('project-applications.index');
    }

    public function test_guest_cannot_access_project_applications()
    {
        $response = $this->get(route('project-applications.index'));

        $response->assertRedirect(route('login'));
    }

    public function test_authenticated_user_can_view_create_form()
    {
        $this->actingAs($this->user);
        Service::factory()->count(3)->create();

        $response = $this->get(route('project-applications.create'));

        $response->assertStatus(200);
        $response->assertViewIs('project-applications.create');
        $response->assertViewHas('services');
    }

    public function test_authenticated_user_can_create_project_application_without_files()
    {
        $this->actingAs($this->user);
        $service = Service::factory()->create();

        $applicationData = [
            'service_id' => $service->id,
            'title' => 'E-commerce Website',
            'description' => 'Need a modern e-commerce platform with payment integration',
            'requirements' => 'Laravel, Vue.js, Stripe integration',
            'budget_range' => '15k-50k',
            'timeline' => '2-3-months',
            'priority' => 'high',
        ];

        $response = $this->post(route('project-applications.store'), $applicationData);

        $response->assertRedirect(route('project-applications.index'));
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('project_applications', [
            'user_id' => $this->user->id,
            'service_id' => $service->id,
            'title' => 'E-commerce Website',
            'status' => 'pending',
        ]);
    }

    public function test_authenticated_user_can_create_project_application_with_files()
    {
        $this->actingAs($this->user);

        // Mock the FileService and ImageService
        $this->mock(FileService::class, function ($mock) {
            $mock->shouldReceive('processUploadedFile')
                ->once()
                ->andReturn([
                    'success' => true,
                    'filename' => 'requirements_20250702_123456.pdf',
                    'path' => 'project-applications/' . $this->user->id . '/2025/07/requirements_20250702_123456.pdf',
                    'scan_results' => [
                        'virus_scan' => 'clean',
                        'content_scan' => 'safe',
                    ]
                ]);
        });

        $this->mock(ImageService::class, function ($mock) {
            $mock->shouldReceive('processUploadedFile')
                ->once()
                ->andReturn([
                    'success' => true,
                    'filename' => 'mockup_20250702_123457.webp',
                    'path' => 'project-applications/' . $this->user->id . '/2025/07/mockup_20250702_123457.webp',
                    'scan_results' => [
                        'virus_scan' => 'clean',
                        'optimized' => true,
                        'converted_to_webp' => true,
                    ]
                ]);
        });

        $pdfFile = UploadedFile::fake()->create('requirements.pdf', 1024, 'application/pdf');
        $imageFile = UploadedFile::fake()->create('mockup.jpg', 1024, 'image/jpeg');

        $applicationData = [
            'title' => 'Mobile App Development',
            'description' => 'Need a cross-platform mobile app for our business',
            'priority' => 'medium',
            'attachments' => [$pdfFile, $imageFile],
        ];

        $response = $this->post(route('project-applications.store'), $applicationData);

        $response->assertRedirect(route('project-applications.index'));
        $response->assertSessionHas('success');

        $application = ProjectApplication::where('user_id', $this->user->id)->first();
        $this->assertNotNull($application);
        $this->assertCount(2, $application->attachments);
        $this->assertEquals('requirements.pdf', $application->attachments[0]['original_name']);
        $this->assertEquals('mockup.jpg', $application->attachments[1]['original_name']);
    }

    public function test_user_can_view_their_own_project_application()
    {
        $this->actingAs($this->user);
        
        $application = ProjectApplication::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $response = $this->get(route('project-applications.show', $application));

        $response->assertStatus(200);
        $response->assertViewIs('project-applications.show');
        $response->assertViewHas('projectApplication', $application);
    }

    public function test_user_cannot_view_other_users_project_application()
    {
        $this->actingAs($this->user);
        $otherUser = User::factory()->create();
        
        $application = ProjectApplication::factory()->create([
            'user_id' => $otherUser->id,
        ]);

        $response = $this->get(route('project-applications.show', $application));

        $response->assertStatus(403);
    }

    public function test_user_can_edit_their_pending_project_application()
    {
        $this->actingAs($this->user);
        
        $application = ProjectApplication::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'pending',
        ]);

        $response = $this->get(route('project-applications.edit', $application));

        $response->assertStatus(200);
        $response->assertViewIs('project-applications.edit');
    }

    public function test_user_cannot_edit_non_pending_project_application()
    {
        $this->actingAs($this->user);
        
        $application = ProjectApplication::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'approved',
        ]);

        $response = $this->get(route('project-applications.edit', $application));

        $response->assertStatus(403);
    }

    public function test_user_can_update_their_pending_project_application()
    {
        $this->actingAs($this->user);
        
        $application = ProjectApplication::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'pending',
            'title' => 'Original Title',
        ]);

        $updateData = [
            'title' => 'Updated Title',
            'description' => 'Updated description',
            'priority' => 'high',
        ];

        $response = $this->put(route('project-applications.update', $application), $updateData);

        $response->assertRedirect(route('project-applications.show', $application));
        $response->assertSessionHas('success');

        $application->refresh();
        $this->assertEquals('Updated Title', $application->title);
        $this->assertEquals('Updated description', $application->description);
    }

    public function test_user_can_delete_their_pending_project_application()
    {
        $this->actingAs($this->user);
        
        $application = ProjectApplication::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'pending',
        ]);

        $response = $this->delete(route('project-applications.destroy', $application));

        $response->assertRedirect(route('project-applications.index'));
        $response->assertSessionHas('success');

        $this->assertDatabaseMissing('project_applications', [
            'id' => $application->id,
        ]);
    }

    public function test_user_cannot_delete_non_pending_project_application()
    {
        $this->actingAs($this->user);
        
        $application = ProjectApplication::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'approved',
        ]);

        $response = $this->delete(route('project-applications.destroy', $application));

        $response->assertStatus(403);

        $this->assertDatabaseHas('project_applications', [
            'id' => $application->id,
        ]);
    }

    public function test_project_application_validates_required_fields()
    {
        $this->actingAs($this->user);

        $response = $this->post(route('project-applications.store'), [
            'title' => '', // Required
            'description' => '', // Required
            'priority' => '', // Required
        ]);

        $response->assertSessionHasErrors(['title', 'description', 'priority']);
    }

    public function test_project_application_validates_file_size()
    {
        $this->actingAs($this->user);

        $largeFile = UploadedFile::fake()->create('large_file.pdf', 30000, 'application/pdf'); // 30MB

        $response = $this->post(route('project-applications.store'), [
            'title' => 'Test Project',
            'description' => 'Test description',
            'priority' => 'medium',
            'attachments' => [$largeFile],
        ]);

        $response->assertSessionHasErrors('attachments.0');
    }

    public function test_project_application_validates_maximum_number_of_files()
    {
        $this->actingAs($this->user);

        $files = [];
        for ($i = 0; $i < 16; $i++) { // More than the 15 file limit
            $files[] = UploadedFile::fake()->create("file_{$i}.pdf", 100, 'application/pdf');
        }

        $response = $this->post(route('project-applications.store'), [
            'title' => 'Test Project',
            'description' => 'Test description',
            'priority' => 'medium',
            'attachments' => $files,
        ]);

        $response->assertSessionHasErrors('attachments');
    }

    public function test_project_application_handles_file_processing_failure()
    {
        $this->actingAs($this->user);

        // Mock the FileService to return failure
        $this->mock(FileService::class, function ($mock) {
            $mock->shouldReceive('processUploadedFile')
                ->once()
                ->andReturn([
                    'success' => false,
                    'message' => 'Virus detected in file',
                ]);
        });

        $file = UploadedFile::fake()->create('suspicious_file.pdf', 1024, 'application/pdf');

        $response = $this->post(route('project-applications.store'), [
            'title' => 'Test Project',
            'description' => 'Test description',
            'priority' => 'medium',
            'attachments' => [$file],
        ]);

        $response->assertRedirect(route('project-applications.index'));
        $response->assertSessionHas('success');

        $application = ProjectApplication::where('user_id', $this->user->id)->first();
        $this->assertNotNull($application);
        // Should have empty attachments array since file processing failed
        $this->assertEquals([], $application->attachments ?? []);
    }

    public function test_project_application_success_message_includes_file_count()
    {
        $this->actingAs($this->user);

        // Mock the FileService to return success
        $this->mock(FileService::class, function ($mock) {
            $mock->shouldReceive('processUploadedFile')
                ->twice()
                ->andReturn([
                    'success' => true,
                    'filename' => 'test_file.pdf',
                    'path' => 'project-applications/' . $this->user->id . '/2025/07/test_file.pdf',
                    'scan_results' => ['virus_scan' => 'clean']
                ]);
        });

        $file1 = UploadedFile::fake()->create('file1.pdf', 1024, 'application/pdf');
        $file2 = UploadedFile::fake()->create('file2.pdf', 1024, 'application/pdf');

        $response = $this->post(route('project-applications.store'), [
            'title' => 'File Count Test',
            'description' => 'Testing file count in success message.',
            'priority' => 'medium',
            'attachments' => [$file1, $file2],
        ]);

        $response->assertRedirect(route('project-applications.index'));
        $response->assertSessionHas('success');
        
        $successMessage = session('success');
        $this->assertStringContainsString('2 file(s)', $successMessage);
        $this->assertStringContainsString('scanned', $successMessage);
    }
}
