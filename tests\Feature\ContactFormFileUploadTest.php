<?php

namespace Tests\Feature;

use App\Models\ContactSubmission;
use App\Services\FileService;
use App\Services\ImageService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class ContactFormFileUploadTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Storage::fake('public');

        // Seed roles for User factory
        $this->seed(\Database\Seeders\RoleSeeder::class);
    }

    public function test_contact_form_can_be_submitted_without_files()
    {
        $response = $this->post(route('contact.submit'), [
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'phone' => '+27123456789',
            'company' => 'Test Company',
            'service' => 'web-development',
            'subject' => 'Test Subject',
            'message' => 'This is a test message.',
        ]);

        $response->assertRedirect(route('contact'));
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('contact_submissions', [
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'subject' => 'Test Subject',
        ]);
    }

    public function test_contact_form_can_be_submitted_with_pdf_file()
    {
        // Mock the FileService to return success
        $this->mock(FileService::class, function ($mock) {
            $mock->shouldReceive('processUploadedFile')
                ->once()
                ->andReturn([
                    'success' => true,
                    'filename' => 'test_document_20250702_123456.pdf',
                    'path' => 'contact-submissions/2025/07/test_document_20250702_123456.pdf',
                    'scan_results' => [
                        'virus_scan' => 'clean',
                        'content_scan' => 'safe',
                        'scanned_at' => now()->toISOString(),
                    ]
                ]);
        });

        $file = UploadedFile::fake()->create('test_document.pdf', 1024, 'application/pdf');

        $response = $this->post(route('contact.submit'), [
            'name' => 'Jane Doe',
            'email' => '<EMAIL>',
            'message' => 'Test message with PDF attachment.',
            'attachments' => [$file],
        ]);

        $response->assertRedirect(route('contact'));
        $response->assertSessionHas('success');

        $submission = ContactSubmission::where('email', '<EMAIL>')->first();
        $this->assertNotNull($submission);
        $this->assertNotNull($submission->attachments);
        $this->assertCount(1, $submission->attachments);
        $this->assertEquals('test_document.pdf', $submission->attachments[0]['original_name']);
        $this->assertArrayHasKey('scan_results', $submission->attachments[0]);
    }

    public function test_contact_form_can_be_submitted_with_image_file()
    {
        // Mock the ImageService to return success
        $this->mock(ImageService::class, function ($mock) {
            $mock->shouldReceive('processUploadedFile')
                ->once()
                ->andReturn([
                    'success' => true,
                    'filename' => 'test_image_20250702_123456.webp',
                    'path' => 'contact-submissions/2025/07/test_image_20250702_123456.webp',
                    'scan_results' => [
                        'virus_scan' => 'clean',
                        'optimized' => true,
                        'converted_to_webp' => true,
                    ]
                ]);
        });

        $file = UploadedFile::fake()->image('test_image.jpg', 800, 600);

        $response = $this->post(route('contact.submit'), [
            'name' => 'Bob Smith',
            'email' => '<EMAIL>',
            'message' => 'Test message with image attachment.',
            'attachments' => [$file],
        ]);

        $response->assertRedirect(route('contact'));
        $response->assertSessionHas('success');

        $submission = ContactSubmission::where('email', '<EMAIL>')->first();
        $this->assertNotNull($submission);
        $this->assertNotNull($submission->attachments);
        $this->assertCount(1, $submission->attachments);
        $this->assertEquals('test_image.jpg', $submission->attachments[0]['original_name']);
        $this->assertArrayHasKey('scan_results', $submission->attachments[0]);
    }

    public function test_contact_form_can_be_submitted_with_multiple_files()
    {
        // Mock both services
        $this->mock(FileService::class, function ($mock) {
            $mock->shouldReceive('processUploadedFile')
                ->once()
                ->andReturn([
                    'success' => true,
                    'filename' => 'document_20250702_123456.pdf',
                    'path' => 'contact-submissions/2025/07/document_20250702_123456.pdf',
                    'scan_results' => ['virus_scan' => 'clean']
                ]);
        });

        $this->mock(ImageService::class, function ($mock) {
            $mock->shouldReceive('processUploadedFile')
                ->once()
                ->andReturn([
                    'success' => true,
                    'filename' => 'image_20250702_123457.webp',
                    'path' => 'contact-submissions/2025/07/image_20250702_123457.webp',
                    'scan_results' => ['virus_scan' => 'clean', 'converted_to_webp' => true]
                ]);
        });

        $pdfFile = UploadedFile::fake()->create('document.pdf', 1024, 'application/pdf');
        $imageFile = UploadedFile::fake()->image('image.png', 400, 300);

        $response = $this->post(route('contact.submit'), [
            'name' => 'Alice Johnson',
            'email' => '<EMAIL>',
            'message' => 'Test message with multiple attachments.',
            'attachments' => [$pdfFile, $imageFile],
        ]);

        $response->assertRedirect(route('contact'));
        $response->assertSessionHas('success');

        $submission = ContactSubmission::where('email', '<EMAIL>')->first();
        $this->assertNotNull($submission);
        $this->assertNotNull($submission->attachments);
        $this->assertCount(2, $submission->attachments);
    }

    public function test_contact_form_handles_file_processing_failure_gracefully()
    {
        // Mock the FileService to return failure
        $this->mock(FileService::class, function ($mock) {
            $mock->shouldReceive('processUploadedFile')
                ->once()
                ->andReturn([
                    'success' => false,
                    'message' => 'Virus detected in file',
                ]);
        });

        $file = UploadedFile::fake()->create('suspicious_file.pdf', 1024, 'application/pdf');

        $response = $this->post(route('contact.submit'), [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'message' => 'Test message with suspicious file.',
            'attachments' => [$file],
        ]);

        // Should still redirect successfully but without the failed file
        $response->assertRedirect(route('contact'));
        $response->assertSessionHas('success');

        $submission = ContactSubmission::where('email', '<EMAIL>')->first();
        $this->assertNotNull($submission);
        // Should have empty attachments array since file processing failed
        $this->assertEquals([], $submission->attachments ?? []);
    }

    public function test_contact_form_validates_file_size()
    {
        $largeFile = UploadedFile::fake()->create('large_file.pdf', 30000, 'application/pdf'); // 30MB

        $response = $this->post(route('contact.submit'), [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'message' => 'Test message with large file.',
            'attachments' => [$largeFile],
        ]);

        $response->assertSessionHasErrors('attachments.0');
    }

    public function test_contact_form_validates_maximum_number_of_files()
    {
        $files = [];
        for ($i = 0; $i < 16; $i++) { // More than the 15 file limit
            $files[] = UploadedFile::fake()->create("file_{$i}.pdf", 100, 'application/pdf');
        }

        $response = $this->post(route('contact.submit'), [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'message' => 'Test message with too many files.',
            'attachments' => $files,
        ]);

        $response->assertSessionHasErrors('attachments');
    }

    public function test_contact_form_validates_required_fields()
    {
        $response = $this->post(route('contact.submit'), [
            'email' => 'invalid-email',
            'message' => '', // Required field
        ]);

        $response->assertSessionHasErrors(['name', 'email', 'message']);
    }

    public function test_contact_form_stores_request_metadata()
    {
        $response = $this->post(route('contact.submit'), [
            'name' => 'Metadata Test',
            'email' => '<EMAIL>',
            'message' => 'Testing metadata storage.',
        ], [
            'HTTP_USER_AGENT' => 'Test User Agent',
            'HTTP_REFERER' => 'https://example.com/referrer',
        ]);

        $response->assertRedirect(route('contact'));

        $submission = ContactSubmission::where('email', '<EMAIL>')->first();
        $this->assertNotNull($submission);
        $this->assertNotNull($submission->ip_address);
        $this->assertEquals('Test User Agent', $submission->user_agent);
        $this->assertEquals('https://example.com/referrer', $submission->referrer);
    }

    public function test_contact_form_success_message_includes_file_count()
    {
        // Mock the FileService to return success
        $this->mock(FileService::class, function ($mock) {
            $mock->shouldReceive('processUploadedFile')
                ->twice()
                ->andReturn([
                    'success' => true,
                    'filename' => 'test_file.pdf',
                    'path' => 'contact-submissions/2025/07/test_file.pdf',
                    'scan_results' => ['virus_scan' => 'clean']
                ]);
        });

        $file1 = UploadedFile::fake()->create('file1.pdf', 1024, 'application/pdf');
        $file2 = UploadedFile::fake()->create('file2.pdf', 1024, 'application/pdf');

        $response = $this->post(route('contact.submit'), [
            'name' => 'File Count Test',
            'email' => '<EMAIL>',
            'message' => 'Testing file count in success message.',
            'attachments' => [$file1, $file2],
        ]);

        $response->assertRedirect(route('contact'));
        $response->assertSessionHas('success');
        
        $successMessage = session('success');
        $this->assertStringContainsString('2 file(s)', $successMessage);
        $this->assertStringContainsString('scanned', $successMessage);
    }
}
