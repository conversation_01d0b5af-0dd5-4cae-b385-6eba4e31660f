<?php

namespace Tests\Feature;

use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\ProductVariant;
use App\Models\ShoppingCart;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class CartTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $product;
    protected $variant;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();

        $category = ProductCategory::factory()->create([
            'is_active' => true,
        ]);

        $this->product = Product::factory()->create([
            'name' => 'Test Product',
            'price' => 50.00,
            'is_active' => true,
            'track_inventory' => false,
        ]);

        $this->product->categories()->attach($category);

        $this->variant = ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'name' => 'Red - Large',
            'price' => 60.00,
            'is_active' => true,
        ]);
    }

    /** @test */
    public function it_can_add_product_to_cart()
    {
        $this->actingAs($this->user)
            ->postJson('/cart/add', [
                'product_id' => $this->product->id,
                'quantity' => 2,
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Product added to cart successfully!',
            ]);

        $cart = ShoppingCart::where('user_id', $this->user->id)->first();
        $this->assertNotNull($cart);
        $this->assertEquals(1, $cart->items->count());
        $this->assertEquals(2, $cart->items->first()->quantity);
        $this->assertEquals(100.00, $cart->subtotal);
    }

    /** @test */
    public function it_can_add_product_variant_to_cart()
    {
        $this->actingAs($this->user)
            ->postJson('/cart/add', [
                'product_id' => $this->product->id,
                'variant_id' => $this->variant->id,
                'quantity' => 1,
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Product added to cart successfully!',
            ]);

        $cart = ShoppingCart::where('user_id', $this->user->id)->first();
        $item = $cart->items->first();
        $this->assertEquals($this->variant->id, $item->product_variant_id);
        $this->assertEquals(60.00, $item->unit_price);
        $this->assertEquals(60.00, $cart->subtotal);
    }

    /** @test */
    public function it_can_update_cart_item_quantity()
    {
        // First add item to cart
        $this->actingAs($this->user)
            ->postJson('/cart/add', [
                'product_id' => $this->product->id,
                'quantity' => 1,
            ]);

        $cart = ShoppingCart::where('user_id', $this->user->id)->first();
        $item = $cart->items->first();

        // Update quantity
        $this->actingAs($this->user)
            ->patchJson("/cart/update/{$item->id}", [
                'quantity' => 3,
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Cart updated successfully!',
            ]);

        $item->refresh();
        $cart->refresh();
        $this->assertEquals(3, $item->quantity);
        $this->assertEquals(150.00, $cart->subtotal);
    }

    /** @test */
    public function it_can_remove_item_from_cart()
    {
        // First add item to cart
        $this->actingAs($this->user)
            ->postJson('/cart/add', [
                'product_id' => $this->product->id,
                'quantity' => 1,
            ]);

        $cart = ShoppingCart::where('user_id', $this->user->id)->first();
        $item = $cart->items->first();

        // Remove item
        $this->actingAs($this->user)
            ->deleteJson("/cart/remove/{$item->id}")
            ->assertJson([
                'success' => true,
                'message' => 'Item removed from cart successfully!',
            ]);

        $cart->refresh();
        $this->assertEquals(0, $cart->items->count());
        $this->assertEquals(0, $cart->subtotal);
    }

    /** @test */
    public function it_can_clear_entire_cart()
    {
        // Add multiple items to cart
        $this->actingAs($this->user)
            ->postJson('/cart/add', [
                'product_id' => $this->product->id,
                'quantity' => 2,
            ]);

        $product2 = Product::factory()->create([
            'price' => 30.00,
            'is_active' => true,
            'track_inventory' => false,
        ]);

        $this->actingAs($this->user)
            ->postJson('/cart/add', [
                'product_id' => $product2->id,
                'quantity' => 1,
            ]);

        $cart = ShoppingCart::where('user_id', $this->user->id)->first();
        $this->assertEquals(2, $cart->items->count());

        // Clear cart
        $this->actingAs($this->user)
            ->deleteJson('/cart/clear')
            ->assertJson([
                'success' => true,
                'message' => 'Cart cleared successfully!',
            ]);

        $cart->refresh();
        $this->assertEquals(0, $cart->items->count());
        $this->assertEquals(0, $cart->subtotal);
    }

    /** @test */
    public function it_validates_add_to_cart_request()
    {
        $this->actingAs($this->user)
            ->postJson('/cart/add', [])
            ->assertJsonValidationErrors(['product_id', 'quantity']);

        $this->actingAs($this->user)
            ->postJson('/cart/add', [
                'product_id' => 999999,
                'quantity' => 0,
            ])
            ->assertJsonValidationErrors(['product_id', 'quantity']);
    }

    /** @test */
    public function it_prevents_adding_inactive_products()
    {
        $inactiveProduct = Product::factory()->create([
            'is_active' => false,
        ]);

        $this->actingAs($this->user)
            ->postJson('/cart/add', [
                'product_id' => $inactiveProduct->id,
                'quantity' => 1,
            ])
            ->assertJsonValidationErrors(['product_id']);
    }

    /** @test */
    public function it_can_get_cart_count()
    {
        // Add items to cart
        $this->actingAs($this->user)
            ->postJson('/cart/add', [
                'product_id' => $this->product->id,
                'quantity' => 3,
            ]);

        $this->actingAs($this->user)
            ->getJson('/cart/count')
            ->assertJson([
                'count' => 3,
            ]);
    }

    /** @test */
    public function it_displays_cart_page()
    {
        $this->actingAs($this->user)
            ->get('/cart')
            ->assertStatus(200)
            ->assertViewIs('pages.cart.index')
            ->assertViewHas('cart');
    }

    /** @test */
    public function it_merges_session_cart_with_user_cart_on_login()
    {
        // Create session cart (guest user)
        $sessionCart = ShoppingCart::factory()->create([
            'user_id' => null,
            'session_id' => 'test-session',
        ]);

        $sessionCart->items()->create([
            'product_id' => $this->product->id,
            'quantity' => 2,
            'unit_price' => 50.00,
            'total' => 100.00,
        ]);

        // Create user cart
        $userCart = ShoppingCart::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $userCart->items()->create([
            'product_id' => $this->product->id,
            'quantity' => 1,
            'unit_price' => 50.00,
            'total' => 50.00,
        ]);

        // Simulate cart merge (this would happen in the CartController)
        $totalQuantity = $sessionCart->items->sum('quantity') + $userCart->items->sum('quantity');
        
        $this->assertEquals(3, $totalQuantity);
    }

    /** @test */
    public function it_calculates_cart_totals_correctly()
    {
        $this->actingAs($this->user)
            ->postJson('/cart/add', [
                'product_id' => $this->product->id,
                'quantity' => 2,
            ]);

        $cart = ShoppingCart::where('user_id', $this->user->id)->first();
        
        $this->assertEquals(100.00, $cart->subtotal);
        $this->assertEquals(15.00, $cart->tax_amount); // 15% tax
        $this->assertEquals(0.00, $cart->shipping_amount); // Free shipping
        $this->assertEquals(115.00, $cart->total);
    }
}
