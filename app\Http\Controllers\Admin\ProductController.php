<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\ProductVariant;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Storage;

class ProductController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:admin,staff');
    }

    /**
     * Display a listing of products.
     */
    public function index(Request $request): View
    {
        $query = Product::with(['categories'])
                       ->where('is_deleted', false);

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('sku', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Category filter
        if ($request->filled('category')) {
            $query->whereHas('categories', function ($q) use ($request) {
                $q->where('product_categories.id', $request->category);
            });
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        // Stock filter
        if ($request->filled('stock')) {
            switch ($request->stock) {
                case 'in_stock':
                    $query->where('inventory_quantity', '>', 0);
                    break;
                case 'low_stock':
                    $query->whereRaw('inventory_quantity <= low_stock_threshold');
                    break;
                case 'out_of_stock':
                    $query->where('inventory_quantity', 0);
                    break;
            }
        }

        $products = $query->orderBy('created_at', 'desc')->paginate(20);
        $categories = ProductCategory::where('is_deleted', false)->orderBy('name')->get();

        return view('admin.products.index', compact('products', 'categories'));
    }

    /**
     * Show the form for creating a new product.
     */
    public function create(): View
    {
        $categories = ProductCategory::where('is_deleted', false)
                                   ->where('is_active', true)
                                   ->orderBy('name')
                                   ->get();

        return view('admin.products.create', compact('categories'));
    }

    /**
     * Store a newly created product.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $this->validateProductData($request);

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            $validated['featured_image'] = $request->file('featured_image')->store('products', 'public');
        }

        // Handle gallery images
        $gallery = [];
        if ($request->hasFile('gallery')) {
            foreach ($request->file('gallery') as $file) {
                $gallery[] = $file->store('products', 'public');
            }
        }
        $validated['gallery'] = $gallery;

        // Generate UUID and slug
        $validated['uuid'] = Str::uuid();
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['name']);
        }

        $product = Product::create($validated);

        // Attach categories
        if ($request->filled('categories')) {
            $product->categories()->attach($request->categories);
        }

        // Create variants if provided
        if ($request->filled('variants')) {
            foreach ($request->variants as $variantData) {
                if (!empty($variantData['name'])) {
                    $product->variants()->create([
                        'name' => $variantData['name'],
                        'sku' => $variantData['sku'] ?? $product->sku . '-' . Str::random(4),
                        'price' => $variantData['price'] ?? $product->price,
                        'inventory_quantity' => $variantData['inventory_quantity'] ?? 0,
                        'attributes' => $variantData['attributes'] ?? [],
                        'is_active' => true,
                    ]);
                }
            }
        }

        return redirect()->route('admin.products.index')
                        ->with('success', 'Product created successfully.');
    }

    /**
     * Display the specified product.
     */
    public function show(Product $product): View
    {
        $product->load(['categories', 'variants']);
        
        return view('admin.products.show', compact('product'));
    }

    /**
     * Show the form for editing the specified product.
     */
    public function edit(Product $product): View
    {
        $product->load(['categories', 'variants']);
        $categories = ProductCategory::where('is_deleted', false)
                                   ->where('is_active', true)
                                   ->orderBy('name')
                                   ->get();

        return view('admin.products.edit', compact('product', 'categories'));
    }

    /**
     * Update the specified product.
     */
    public function update(Request $request, Product $product): RedirectResponse
    {
        $validated = $this->validateProductData($request, $product);

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            // Delete old image
            if ($product->featured_image) {
                Storage::disk('public')->delete($product->featured_image);
            }
            $validated['featured_image'] = $request->file('featured_image')->store('products', 'public');
        }

        // Handle gallery images
        if ($request->hasFile('gallery')) {
            $gallery = $product->gallery ?? [];
            foreach ($request->file('gallery') as $file) {
                $gallery[] = $file->store('products', 'public');
            }
            $validated['gallery'] = $gallery;
        }

        // Generate slug if name changed
        if ($request->name !== $product->name && empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['name']);
        }

        $product->update($validated);

        // Sync categories
        if ($request->filled('categories')) {
            $product->categories()->sync($request->categories);
        } else {
            $product->categories()->detach();
        }

        return redirect()->route('admin.products.index')
                        ->with('success', 'Product updated successfully.');
    }

    /**
     * Remove the specified product.
     */
    public function destroy(Product $product): RedirectResponse
    {
        // Soft delete
        $product->update(['is_deleted' => true]);

        return redirect()->route('admin.products.index')
                        ->with('success', 'Product deleted successfully.');
    }

    /**
     * Upload additional images for a product.
     */
    public function uploadImages(Request $request, Product $product)
    {
        $request->validate([
            'images' => 'required|array',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $gallery = $product->gallery ?? [];

        foreach ($request->file('images') as $file) {
            $gallery[] = $file->store('products', 'public');
        }

        $product->update(['gallery' => $gallery]);

        return response()->json([
            'success' => true,
            'message' => 'Images uploaded successfully.',
            'images' => $gallery,
        ]);
    }

    /**
     * Delete a specific image from product gallery.
     */
    public function deleteImage(Request $request, Product $product, $imageIndex)
    {
        $gallery = $product->gallery ?? [];

        if (isset($gallery[$imageIndex])) {
            // Delete file from storage
            Storage::disk('public')->delete($gallery[$imageIndex]);
            
            // Remove from array
            unset($gallery[$imageIndex]);
            $gallery = array_values($gallery); // Re-index array

            $product->update(['gallery' => $gallery]);

            return response()->json([
                'success' => true,
                'message' => 'Image deleted successfully.',
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Image not found.',
        ], 404);
    }

    /**
     * Validate product data.
     */
    private function validateProductData(Request $request, Product $product = null): array
    {
        $rules = [
            'name' => 'required|string|max:300',
            'slug' => [
                'nullable',
                'string',
                'max:400',
                Rule::unique('products', 'slug')->ignore($product?->id)
            ],
            'short_description' => 'nullable|string',
            'description' => 'nullable|string',
            'sku' => [
                'required',
                'string',
                'max:100',
                Rule::unique('products', 'sku')->ignore($product?->id)
            ],
            'barcode' => 'nullable|string|max:100',
            'brand' => 'nullable|string|max:100',
            'model' => 'nullable|string|max:100',
            'price' => 'required|numeric|min:0',
            'compare_price' => 'nullable|numeric|min:0',
            'cost_price' => 'nullable|numeric|min:0',
            'track_inventory' => 'boolean',
            'inventory_quantity' => 'required_if:track_inventory,true|integer|min:0',
            'low_stock_threshold' => 'nullable|integer|min:0',
            'weight' => 'nullable|numeric|min:0',
            'dimensions' => 'nullable|array',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'gallery' => 'nullable|array',
            'gallery.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string',
            'meta_keywords' => 'nullable|string',
            'categories' => 'nullable|array',
            'categories.*' => 'exists:product_categories,id',
        ];

        return $request->validate($rules);
    }

    /**
     * Toggle product featured status via AJAX.
     */
    public function toggleFeatured(Product $product)
    {
        $product->update(['is_featured' => !$product->is_featured]);

        return response()->json([
            'success' => true,
            'is_featured' => $product->is_featured,
            'message' => $product->is_featured ? 'Product featured successfully.' : 'Product unfeatured successfully.'
        ]);
    }
}
