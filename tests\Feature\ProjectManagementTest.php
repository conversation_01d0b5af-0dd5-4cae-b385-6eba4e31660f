<?php

namespace Tests\Feature;

use App\Models\Project;
use App\Models\Role;
use App\Models\Service;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ProjectManagementTest extends TestCase
{
    use RefreshDatabase;

    protected $client;
    protected $otherClient;
    protected $service;

    protected function setUp(): void
    {
        parent::setUp();

        // Create roles first
        Role::create(['name' => 'admin', 'slug' => 'admin', 'is_active' => true]);
        Role::create(['name' => 'staff', 'slug' => 'staff', 'is_active' => true]);
        Role::create(['name' => 'client', 'slug' => 'client', 'is_active' => true]);
        Role::create(['name' => 'customer', 'slug' => 'customer', 'is_active' => true]);

        // Create client role
        $clientRole = Role::where('name', 'client')->first();

        // Create test users
        $this->client = User::factory()->create([
            'role_id' => $clientRole->id,
            'email_verified_at' => now(),
        ]);

        $this->otherClient = User::factory()->create([
            'role_id' => $clientRole->id,
            'email_verified_at' => now(),
        ]);

        // Create a service for testing (optional)
        $this->service = Service::factory()->create();
    }

    /** @test */
    public function authenticated_client_can_view_projects_index()
    {
        $this->actingAs($this->client);

        $response = $this->get(route('projects.index'));

        $response->assertStatus(200);
        $response->assertViewIs('projects.index');
        $response->assertViewHas('projects');
        $response->assertViewHas('statusCounts');
    }

    /** @test */
    public function unauthenticated_user_cannot_access_projects()
    {
        $response = $this->get(route('projects.index'));

        $response->assertRedirect(route('login'));
    }

    /** @test */
    public function non_client_user_cannot_access_projects()
    {
        $customerRole = Role::where('name', 'customer')->first();
        $customer = User::factory()->create(['role_id' => $customerRole->id]);

        $this->actingAs($customer);

        $response = $this->get(route('projects.index'));

        $response->assertStatus(403);
    }

    /** @test */
    public function client_can_view_their_own_projects()
    {
        $this->actingAs($this->client);

        $project = Project::factory()->create([
            'client_id' => $this->client->id,
        ]);

        $response = $this->get(route('projects.index'));

        $response->assertStatus(200);
        $response->assertSee($project->title);
        $response->assertSee($project->description);
    }

    /** @test */
    public function client_cannot_see_other_clients_projects()
    {
        $this->actingAs($this->client);

        $otherProject = Project::factory()->create([
            'client_id' => $this->otherClient->id,
        ]);

        $response = $this->get(route('projects.index'));

        $response->assertStatus(200);
        $response->assertDontSee($otherProject->title);
    }

    /** @test */
    public function deleted_projects_are_not_shown_in_index()
    {
        $this->actingAs($this->client);

        $project = Project::factory()->create([
            'client_id' => $this->client->id,
            'is_deleted' => true,
        ]);

        $response = $this->get(route('projects.index'));

        $response->assertStatus(200);
        $response->assertDontSee($project->title);
    }

    /** @test */
    public function client_can_filter_projects_by_status()
    {
        $this->actingAs($this->client);

        $inProgressProject = Project::factory()->create([
            'client_id' => $this->client->id,
            'status' => 'in_progress',
        ]);

        $completedProject = Project::factory()->create([
            'client_id' => $this->client->id,
            'status' => 'completed',
        ]);

        $response = $this->get(route('projects.index', ['status' => 'in_progress']));

        $response->assertStatus(200);
        $response->assertSee($inProgressProject->title);
        $response->assertDontSee($completedProject->title);
    }

    /** @test */
    public function client_can_filter_projects_by_priority()
    {
        $this->actingAs($this->client);

        $highPriorityProject = Project::factory()->create([
            'client_id' => $this->client->id,
            'priority' => 'high',
        ]);

        $lowPriorityProject = Project::factory()->create([
            'client_id' => $this->client->id,
            'priority' => 'low',
        ]);

        $response = $this->get(route('projects.index', ['priority' => 'high']));

        $response->assertStatus(200);
        $response->assertSee($highPriorityProject->title);
        $response->assertDontSee($lowPriorityProject->title);
    }

    /** @test */
    public function client_can_search_projects()
    {
        $this->actingAs($this->client);

        $project = Project::factory()->create([
            'client_id' => $this->client->id,
            'title' => 'Unique Project Title',
        ]);

        $otherProject = Project::factory()->create([
            'client_id' => $this->client->id,
            'title' => 'Different Title',
        ]);

        $response = $this->get(route('projects.index', ['search' => 'Unique']));

        $response->assertStatus(200);
        $response->assertSee($project->title);
        $response->assertDontSee($otherProject->title);
    }

    /** @test */
    public function client_can_view_project_details()
    {
        $this->actingAs($this->client);

        $project = Project::factory()->create([
            'client_id' => $this->client->id,
        ]);

        $response = $this->get(route('projects.show', $project));

        $response->assertStatus(200);
        $response->assertViewIs('projects.show');
        $response->assertViewHas('project');
        $response->assertSee($project->title);
        $response->assertSee($project->description);
    }

    /** @test */
    public function client_cannot_view_other_clients_project_details()
    {
        $this->actingAs($this->client);

        $otherProject = Project::factory()->create([
            'client_id' => $this->otherClient->id,
        ]);

        $response = $this->get(route('projects.show', $otherProject));

        $response->assertStatus(403);
    }

    /** @test */
    public function accessing_deleted_project_returns_404()
    {
        $this->actingAs($this->client);

        $project = Project::factory()->create([
            'client_id' => $this->client->id,
            'is_deleted' => true,
        ]);

        $response = $this->get(route('projects.show', $project));

        $response->assertStatus(404);
    }

    /** @test */
    public function client_can_delete_planning_project()
    {
        $this->actingAs($this->client);

        $project = Project::factory()->create([
            'client_id' => $this->client->id,
            'status' => 'planning',
        ]);

        $response = $this->delete(route('projects.destroy', $project));

        $response->assertRedirect(route('projects.index'));
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('projects', [
            'id' => $project->id,
            'is_deleted' => true,
        ]);
    }

    /** @test */
    public function client_can_delete_cancelled_project()
    {
        $this->actingAs($this->client);

        $project = Project::factory()->create([
            'client_id' => $this->client->id,
            'status' => 'cancelled',
        ]);

        $response = $this->delete(route('projects.destroy', $project));

        $response->assertRedirect(route('projects.index'));
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('projects', [
            'id' => $project->id,
            'is_deleted' => true,
        ]);
    }

    /** @test */
    public function client_can_delete_on_hold_project()
    {
        $this->actingAs($this->client);

        $project = Project::factory()->create([
            'client_id' => $this->client->id,
            'status' => 'on_hold',
        ]);

        $response = $this->delete(route('projects.destroy', $project));

        $response->assertRedirect(route('projects.index'));
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('projects', [
            'id' => $project->id,
            'is_deleted' => true,
        ]);
    }

    /** @test */
    public function client_cannot_delete_in_progress_project()
    {
        $this->actingAs($this->client);

        $project = Project::factory()->create([
            'client_id' => $this->client->id,
            'status' => 'in_progress',
        ]);

        $response = $this->delete(route('projects.destroy', $project));

        $response->assertRedirect();
        $response->assertSessionHas('error');

        $this->assertDatabaseHas('projects', [
            'id' => $project->id,
            'is_deleted' => false,
        ]);
    }

    /** @test */
    public function client_cannot_delete_completed_project()
    {
        $this->actingAs($this->client);

        $project = Project::factory()->create([
            'client_id' => $this->client->id,
            'status' => 'completed',
        ]);

        $response = $this->delete(route('projects.destroy', $project));

        $response->assertRedirect();
        $response->assertSessionHas('error');

        $this->assertDatabaseHas('projects', [
            'id' => $project->id,
            'is_deleted' => false,
        ]);
    }

    /** @test */
    public function client_cannot_delete_review_project()
    {
        $this->actingAs($this->client);

        $project = Project::factory()->create([
            'client_id' => $this->client->id,
            'status' => 'review',
        ]);

        $response = $this->delete(route('projects.destroy', $project));

        $response->assertRedirect();
        $response->assertSessionHas('error');

        $this->assertDatabaseHas('projects', [
            'id' => $project->id,
            'is_deleted' => false,
        ]);
    }

    /** @test */
    public function client_cannot_delete_other_clients_project()
    {
        $this->actingAs($this->client);

        $otherProject = Project::factory()->create([
            'client_id' => $this->otherClient->id,
            'status' => 'planning',
        ]);

        $response = $this->delete(route('projects.destroy', $otherProject));

        $response->assertStatus(403);

        $this->assertDatabaseHas('projects', [
            'id' => $otherProject->id,
            'is_deleted' => false,
        ]);
    }

    /** @test */
    public function deleting_already_deleted_project_returns_404()
    {
        $this->actingAs($this->client);

        $project = Project::factory()->create([
            'client_id' => $this->client->id,
            'status' => 'planning',
            'is_deleted' => true,
        ]);

        $response = $this->delete(route('projects.destroy', $project));

        $response->assertStatus(404);
    }

    /** @test */
    public function projects_index_shows_status_counts()
    {
        $this->actingAs($this->client);

        // Create projects with different statuses
        Project::factory()->create(['client_id' => $this->client->id, 'status' => 'planning']);
        Project::factory()->create(['client_id' => $this->client->id, 'status' => 'in_progress']);
        Project::factory()->create(['client_id' => $this->client->id, 'status' => 'completed']);

        $response = $this->get(route('projects.index'));

        $response->assertStatus(200);
        $response->assertViewHas('statusCounts');

        $statusCounts = $response->viewData('statusCounts');
        $this->assertEquals(3, $statusCounts['all']);
        $this->assertEquals(1, $statusCounts['planning']);
        $this->assertEquals(1, $statusCounts['in_progress']);
        $this->assertEquals(1, $statusCounts['completed']);
    }
}
