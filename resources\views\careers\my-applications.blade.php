@extends('layouts.app')

@section('title', 'My Job Applications')

@section('content')
<div class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-4">My Job Applications</h1>
            <p class="text-lg text-gray-600">Track the status of your job applications</p>
        </div>

        @if($applications->count() > 0)
            <!-- Applications Grid -->
            <div class="grid gap-6">
                @foreach($applications as $application)
                <div class="bg-white shadow-lg rounded-lg overflow-hidden">
                    <div class="p-6">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <div class="flex items-center justify-between mb-4">
                                    <h3 class="text-xl font-semibold text-gray-900">
                                        {{ $application->job->title }}
                                    </h3>
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                        @if($application->status === 'pending') bg-yellow-100 text-yellow-800
                                        @elseif($application->status === 'reviewing') bg-blue-100 text-blue-800
                                        @elseif($application->status === 'shortlisted') bg-green-100 text-green-800
                                        @elseif($application->status === 'interviewed') bg-purple-100 text-purple-800
                                        @elseif($application->status === 'offered') bg-emerald-100 text-emerald-800
                                        @elseif($application->status === 'hired') bg-green-100 text-green-800
                                        @elseif($application->status === 'rejected') bg-red-100 text-red-800
                                        @else bg-gray-100 text-gray-800
                                        @endif">
                                        {{ ucfirst($application->status) }}
                                    </span>
                                </div>
                                
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                    <div>
                                        <p class="text-sm font-medium text-gray-500">Reference Number</p>
                                        <p class="text-sm font-mono text-gray-900">{{ $application->reference_number }}</p>
                                    </div>
                                    
                                    <div>
                                        <p class="text-sm font-medium text-gray-500">Applied Date</p>
                                        <p class="text-sm text-gray-900">{{ $application->created_at->format('M d, Y') }}</p>
                                    </div>
                                    
                                    @if($application->reviewed_at)
                                    <div>
                                        <p class="text-sm font-medium text-gray-500">Last Updated</p>
                                        <p class="text-sm text-gray-900">{{ $application->reviewed_at->format('M d, Y') }}</p>
                                    </div>
                                    @endif
                                </div>
                                
                                <div class="mb-4">
                                    <p class="text-sm font-medium text-gray-500 mb-1">Job Details</p>
                                    <div class="flex flex-wrap gap-2">
                                        @if($application->job->department)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                            {{ $application->job->department }}
                                        </span>
                                        @endif
                                        
                                        @if($application->job->employment_type)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            {{ ucfirst($application->job->employment_type) }}
                                        </span>
                                        @endif
                                        
                                        @if($application->job->location)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            {{ $application->job->location }}
                                        </span>
                                        @endif
                                    </div>
                                </div>
                                
                                <!-- Progress Indicator -->
                                <div class="mb-4">
                                    <div class="flex items-center">
                                        <div class="flex-1">
                                            <div class="flex items-center justify-between text-xs text-gray-500 mb-1">
                                                <span>Submitted</span>
                                                <span>Review</span>
                                                <span>Decision</span>
                                            </div>
                                            <div class="w-full bg-gray-200 rounded-full h-2">
                                                <div class="bg-blue-600 h-2 rounded-full transition-all duration-300
                                                    @if($application->status === 'pending') w-1/3
                                                    @elseif(in_array($application->status, ['reviewing', 'shortlisted', 'interviewed'])) w-2/3
                                                    @elseif(in_array($application->status, ['offered', 'hired', 'rejected'])) w-full
                                                    @else w-1/3
                                                    @endif">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Actions -->
                        <div class="flex flex-col sm:flex-row gap-3 pt-4 border-t border-gray-200">
                            <a href="{{ route('careers.show', $application->job) }}" 
                               class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                                View Job
                            </a>
                            
                            @if($application->attachments && count($application->attachments) > 0)
                            <button type="button" 
                                    class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                                </svg>
                                {{ count($application->attachments) }} File(s)
                            </button>
                            @endif
                            
                            <span class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-gray-500">
                                Ref: {{ $application->reference_number }}
                            </span>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
            
            <!-- Pagination -->
            @if($applications->hasPages())
            <div class="mt-8">
                {{ $applications->links() }}
            </div>
            @endif
            
        @else
            <!-- Empty State -->
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No applications yet</h3>
                <p class="mt-1 text-sm text-gray-500">Get started by applying for a job position.</p>
                <div class="mt-6">
                    <a href="{{ route('careers.index') }}" 
                       class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Browse Jobs
                    </a>
                </div>
            </div>
        @endif
    </div>
</div>
@endsection
