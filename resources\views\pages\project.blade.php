@extends('layouts.app')

@section('title', $project->meta_title ?: $project->title . ' - Project - ' . __('common.company_name'))
@section('meta_description', $project->meta_description ?: Str::limit($project->description, 160))
@section('meta_keywords', $project->meta_keywords ?: 'project, ' . strtolower($project->title) . ', digital solution, web development')

@push('structured_data')
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "CreativeWork",
  "name": "{{ $project->title }}",
  "description": "{{ $project->description }}",
  "url": "{{ route('projects.show', $project->slug) }}",
  @if($project->featured_image)
  "image": "{{ asset('storage/' . $project->featured_image) }}",
  @endif
  "dateCreated": "{{ $project->start_date ? $project->start_date->format('Y-m-d') : $project->created_at->format('Y-m-d') }}",
  @if($project->end_date)
  "datePublished": "{{ $project->end_date->format('Y-m-d') }}",
  @endif
  "creator": {
    "@type": "Organization",
    "name": "{{ __('common.company_name') }}",
    "url": "{{ url('/') }}"
  },
  @if($project->client_name)
  "client": {
    "@type": "Organization",
    "name": "{{ $project->client_name }}"
  },
  @endif
  @if($project->project_url)
  "url": "{{ $project->project_url }}",
  @endif
  "genre": "{{ $project->service->name ?? 'Digital Solution' }}"
}
</script>
@endpush

@section('content')
<!-- Hero Section -->
<section class="relative bg-gradient-blue text-white overflow-hidden">
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></g></svg>');"></div>
    </div>
    
    <div class="container mx-auto px-4 py-20 lg:py-32 relative z-10">
        <div class="text-center max-w-4xl mx-auto">
            <nav class="flex items-center justify-center space-x-2 text-sm text-blue-200 mb-6">
                <a href="{{ route('projects.index') }}" class="hover:text-white">Portfolio</a>
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
                <span class="text-white">{{ $project->title }}</span>
            </nav>
            
            <h1 class="heading-1 text-white mb-6">
                {{ $project->title }}
            </h1>
            <p class="text-lead text-blue-100 mb-8">
                {{ $project->description }}
            </p>
            
            <div class="flex flex-wrap justify-center gap-4 mb-8">
                @if($project->service)
                    <span class="px-4 py-2 bg-white bg-opacity-20 text-white rounded-full text-sm">{{ $project->service->name }}</span>
                @endif
                @if($project->is_featured)
                    <span class="px-4 py-2 bg-yellow-500 text-yellow-900 rounded-full text-sm font-medium">Featured Project</span>
                @endif
            </div>
            
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                @if($project->project_url)
                    <a href="{{ $project->project_url }}" target="_blank" class="btn-primary bg-white text-blue-600 hover:bg-blue-50">
                        View Live Project
                        <svg class="ml-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </a>
                @endif
                <a href="{{ route('contact') }}" class="btn-outline border-white text-white hover:bg-white hover:text-blue-600">
                    Start Similar Project
                </a>
            </div>
        </div>
    </div>
    
    <div class="absolute bottom-0 left-0 w-full">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" class="relative block w-full h-16 fill-white">
            <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"></path>
        </svg>
    </div>
</section>

<!-- Project Details -->
<section class="py-20 bg-white">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-12">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Featured Image -->
                @if($project->featured_image)
                    <div class="mb-8">
                        <img src="{{ asset('storage/' . $project->featured_image) }}" alt="{{ $project->title }}" class="w-full h-96 object-cover rounded-lg shadow-lg">
                    </div>
                @endif
                
                <!-- Project Content -->
                <div class="prose prose-lg max-w-none">
                    @if($project->content)
                        {!! nl2br(e($project->content)) !!}
                    @else
                        <p class="text-gray-600 leading-relaxed">{{ $project->description }}</p>
                    @endif
                </div>
                
                <!-- Gallery -->
                @if($project->gallery && count($project->gallery) > 0)
                    <div class="mt-12">
                        <h3 class="text-2xl font-bold text-gray-900 mb-6">Project Gallery</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            @foreach($project->gallery as $image)
                                <div class="relative overflow-hidden rounded-lg shadow-lg">
                                    <img src="{{ asset('storage/' . $image) }}" alt="{{ $project->title }} Gallery" class="w-full h-64 object-cover hover:scale-105 transition-transform duration-300">
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif
            </div>
            
            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <div class="bg-gray-50 rounded-lg p-6 sticky top-6">
                    <h3 class="text-xl font-bold text-gray-900 mb-6">Project Details</h3>
                    
                    <div class="space-y-4">
                        @if($project->client_name)
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Client</dt>
                                <dd class="text-lg text-gray-900">{{ $project->client_name }}</dd>
                            </div>
                        @endif
                        
                        @if($project->service)
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Service</dt>
                                <dd class="text-lg text-gray-900">{{ $project->service->name }}</dd>
                            </div>
                        @endif
                        
                        @if($project->start_date)
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Start Date</dt>
                                <dd class="text-lg text-gray-900">{{ $project->start_date->format('F Y') }}</dd>
                            </div>
                        @endif
                        
                        @if($project->end_date)
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Completion Date</dt>
                                <dd class="text-lg text-gray-900">{{ $project->end_date->format('F Y') }}</dd>
                            </div>
                        @endif
                        
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Status</dt>
                            <dd class="text-lg">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                    @if($project->status === 'completed') bg-green-100 text-green-800
                                    @elseif($project->status === 'in_progress') bg-blue-100 text-blue-800
                                    @elseif($project->status === 'planning') bg-yellow-100 text-yellow-800
                                    @else bg-gray-100 text-gray-800
                                    @endif">
                                    {{ ucfirst(str_replace('_', ' ', $project->status)) }}
                                </span>
                            </dd>
                        </div>
                    </div>
                    
                    <div class="mt-8 space-y-3">
                        <a href="{{ route('projects.index') }}" 
                           class="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors duration-200 text-center block">
                            ← Back to Portfolio
                        </a>
                        
                        <a href="{{ route('contact') }}" 
                           class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200 text-center block">
                            Start Similar Project
                        </a>
                        
                        @if($project->project_url)
                            <a href="{{ $project->project_url }}" target="_blank" 
                               class="w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors duration-200 text-center block">
                                View Live Project
                            </a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Projects -->
@if($relatedProjects->count() > 0)
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="heading-2 text-gray-900 mb-4">Related Projects</h2>
            <p class="text-gray-600">Explore more of our work</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @foreach($relatedProjects as $relatedProject)
                <div class="project-card card-hover">
                    <div class="relative overflow-hidden rounded-lg mb-6">
                        @if($relatedProject->featured_image)
                            <img src="{{ $relatedProject->featured_image_url }}" alt="{{ $relatedProject->title }}" class="w-full h-48 object-cover hover-lift">
                        @else
                            <div class="w-full h-48 bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center">
                                <svg class="w-16 h-16 text-white opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                                </svg>
                            </div>
                        @endif
                        <div class="absolute inset-0 bg-blue-600 bg-opacity-0 hover:bg-opacity-90 transition-all duration-300 flex items-center justify-center">
                            <a href="{{ route('projects.show', $relatedProject->slug) }}" class="btn-primary opacity-0 hover:opacity-100 transition-opacity duration-300">
                                View Project
                            </a>
                        </div>
                    </div>
                    <div class="space-y-2">
                        <h3 class="text-xl font-semibold text-gray-900">{{ $relatedProject->title }}</h3>
                        <p class="text-gray-600 text-sm leading-relaxed">
                            {{ Str::limit($relatedProject->description, 100) }}
                        </p>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</section>
@endif

<!-- CTA Section -->
<section class="py-20 bg-blue-600 text-white">
    <div class="container mx-auto px-4 text-center">
        <h2 class="heading-2 text-white mb-4">
            Inspired by This Project?
        </h2>
        <p class="text-lead text-blue-100 mb-8 max-w-2xl mx-auto">
            Let's discuss how we can create something similar for your business. Get in touch to start your project.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{{ route('contact') }}" class="btn-primary bg-white text-blue-600 hover:bg-blue-50">
                Start Your Project
                <svg class="ml-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </a>
            <a href="{{ route('projects.index') }}" class="btn-outline border-white text-white hover:bg-white hover:text-blue-600">
                View All Projects
            </a>
        </div>
    </div>
</section>
@endsection
