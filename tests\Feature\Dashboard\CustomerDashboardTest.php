<?php

namespace Tests\Feature\Dashboard;

use App\Models\Order;
use App\Models\Project;
use App\Models\Role;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CustomerDashboardTest extends TestCase
{
    use RefreshDatabase;

    protected $seed = true; // Use seeders to populate required data

    /** @test */
    public function customer_can_access_dashboard()
    {
        $customerRole = Role::where('name', 'customer')->first();
        $customer = User::factory()->create(['role_id' => $customerRole->id]);

        $response = $this->actingAs($customer)->get('/dashboard');

        $response->assertStatus(200);
        $response->assertViewIs('dashboard.index');
        $response->assertSee('Welcome back, ' . $customer->first_name);
        $response->assertSee('Customer');
    }

    /** @test */
    public function client_can_access_dashboard()
    {
        $clientRole = Role::where('name', 'client')->first();
        $client = User::factory()->create(['role_id' => $clientRole->id]);

        $response = $this->actingAs($client)->get('/dashboard');

        $response->assertStatus(200);
        $response->assertViewIs('dashboard.index');
        $response->assertSee('Welcome back, ' . $client->first_name);
        $response->assertSee('Client');
    }

    /** @test */
    public function customer_dashboard_shows_order_statistics()
    {
        $customerRole = Role::where('name', 'customer')->first();
        $customer = User::factory()->create(['role_id' => $customerRole->id]);

        // Create some test orders
        Order::factory()->count(3)->completed()->create([
            'user_id' => $customer->id,
        ]);

        Order::factory()->count(2)->pending()->create([
            'user_id' => $customer->id,
        ]);

        $response = $this->actingAs($customer)->get('/dashboard');

        $response->assertStatus(200);
        $response->assertSee('Total Orders');
        $response->assertSee('Total Spent');
        $response->assertSee('Pending Orders');
    }

    /** @test */
    public function client_dashboard_shows_project_statistics()
    {
        $clientRole = Role::where('name', 'client')->first();
        $client = User::factory()->create(['role_id' => $clientRole->id]);

        // Create some test projects
        Project::factory()->inProgress()->create([
            'client_id' => $client->id,
            'title' => 'Test Project 1',
            'total_amount' => 5000.00,
        ]);

        Project::factory()->completed()->create([
            'client_id' => $client->id,
            'title' => 'Test Project 2',
            'total_amount' => 3000.00,
        ]);

        $response = $this->actingAs($client)->get('/dashboard');

        $response->assertStatus(200);
        $response->assertSee('Total Projects');
        $response->assertSee('Active Projects');
        $response->assertSee('Project Value');
    }

    /** @test */
    public function unauthenticated_user_cannot_access_dashboard()
    {
        $response = $this->get('/dashboard');

        $response->assertRedirect('/login');
    }

    /** @test */
    public function dashboard_search_works_for_customers()
    {
        $customerRole = Role::where('name', 'customer')->first();
        $customer = User::factory()->create(['role_id' => $customerRole->id]);

        // Create a test order
        $order = Order::factory()->completed()->create([
            'user_id' => $customer->id,
            'order_number' => 'ORD-2024-0001',
        ]);

        $response = $this->actingAs($customer)->getJson('/dashboard/search?q=ORD-2024');

        $response->assertStatus(200);
        $response->assertJsonStructure(['results']);
    }

    /** @test */
    public function dashboard_search_works_for_clients()
    {
        $clientRole = Role::where('name', 'client')->first();
        $client = User::factory()->create(['role_id' => $clientRole->id]);

        // Create a test project
        Project::factory()->inProgress()->create([
            'client_id' => $client->id,
            'title' => 'Website Development',
        ]);

        $response = $this->actingAs($client)->getJson('/dashboard/search?q=Website');

        $response->assertStatus(200);
        $response->assertJsonStructure(['results']);
    }
}
