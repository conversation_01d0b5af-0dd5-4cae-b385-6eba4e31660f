<?php

namespace App\Http\Middleware;

use App\Providers\RouteServiceProvider;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class RedirectIfAuthenticated
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @param  string|null  ...$guards
     */
    public function handle(Request $request, Closure $next, string ...$guards): Response
    {
        $guards = empty($guards) ? [null] : $guards;

        foreach ($guards as $guard) {
            if (Auth::guard($guard)->check()) {
                $user = Auth::guard($guard)->user();
                
                // Redirect authenticated users to appropriate dashboard
                $redirectPath = $this->getRedirectPathByRole($user);
                
                return redirect($redirectPath);
            }
        }

        return $next($request);
    }

    /**
     * Get the appropriate redirect path based on user role.
     */
    protected function getRedirectPathByRole($user): string
    {
        $role = $user->role->name;
        
        switch ($role) {
            case 'admin':
            case 'staff':
                return route('admin.dashboard');
            case 'client':
            case 'customer':
            default:
                return route('dashboard');
        }
    }
}
