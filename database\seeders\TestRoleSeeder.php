<?php

namespace Database\Seeders;

use App\Models\Role;
use Illuminate\Database\Seeder;

class TestRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create basic roles for testing
        $roles = [
            [
                'id' => 1,
                'name' => 'admin',
                'slug' => 'admin',
                'description' => 'Administrator role for testing',
                'permissions' => json_encode(['all' => true]),
                'is_active' => true
            ],
            [
                'id' => 2,
                'name' => 'customer',
                'slug' => 'customer',
                'description' => 'Customer role for testing',
                'permissions' => json_encode(['orders' => ['create', 'read']]),
                'is_active' => true
            ],
            [
                'id' => 3,
                'name' => 'client',
                'slug' => 'client',
                'description' => 'Client role for testing',
                'permissions' => json_encode(['projects' => ['read']]),
                'is_active' => true
            ],
        ];

        foreach ($roles as $role) {
            Role::updateOrCreate(
                ['id' => $role['id']],
                $role
            );
        }
    }
}
