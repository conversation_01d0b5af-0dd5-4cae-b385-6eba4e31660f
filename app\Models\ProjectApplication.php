<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class ProjectApplication extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'uuid',
        'reference_number',
        'user_id', // Nullable for public applications
        // Contact Information (for public applications)
        'full_name',
        'email',
        'phone',
        'company',
        'position',
        'country',
        'preferred_contact_method',
        // Project Information
        'service_id',
        'project_type',
        'project_category',
        'target_audience',
        'project_timeline',
        'estimated_budget',
        'title',
        'description',
        'requirements',
        'budget_range',
        'timeline',
        'priority',
        // Feature Requirements
        'user_authentication',
        'admin_panel',
        'payment_processing',
        'multi_language',
        'mobile_responsive',
        'api_development',
        'third_party_integrations',
        'data_analytics',
        // Technical Preferences
        'preferred_technologies',
        'hosting_preference',
        'existing_system_integration',
        'existing_systems_details',
        'performance_requirements',
        'security_level',
        'maintenance_support',
        'training_required',
        // Additional Information
        'additional_notes',
        'newsletter_signup',
        'terms_accepted',
        'attachments',
        // Status and Management
        'status',
        'is_completed',
        'submitted_at',
        'last_saved_at',
        'admin_notes',
        'reviewed_at',
        'reviewed_by',
        'assigned_at',
        'assigned_by',
        'estimated_cost',
        'estimated_hours',
        'project_id', // If approved and project created
        // Tracking Information
        'ip_address',
        'user_agent',
        'referrer',
        'utm_source',
        'utm_medium',
        'utm_campaign',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'attachments' => 'array',
            'reviewed_at' => 'datetime',
            'submitted_at' => 'datetime',
            'last_saved_at' => 'datetime',
            'assigned_at' => 'datetime',
            'user_authentication' => 'boolean',
            'admin_panel' => 'boolean',
            'payment_processing' => 'boolean',
            'multi_language' => 'boolean',
            'mobile_responsive' => 'boolean',
            'api_development' => 'boolean',
            'third_party_integrations' => 'boolean',
            'data_analytics' => 'boolean',
            'existing_system_integration' => 'boolean',
            'maintenance_support' => 'boolean',
            'training_required' => 'boolean',
            'newsletter_signup' => 'boolean',
            'terms_accepted' => 'boolean',
            'is_completed' => 'boolean',
        ];
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($application) {
            if (empty($application->uuid)) {
                $application->uuid = Str::uuid();
            }
            if (empty($application->status)) {
                $application->status = 'pending';
            }
            // Generate reference number for public applications (when user_id is null)
            if (empty($application->user_id) && empty($application->reference_number)) {
                $application->reference_number = 'APP-' . strtoupper(Str::random(8));
            }
            // Set submission tracking
            if (empty($application->last_saved_at)) {
                $application->last_saved_at = now();
            }
        });

        static::updating(function ($application) {
            $application->last_saved_at = now();
        });
    }

    /**
     * Get the user who submitted the application.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the service for this application.
     */
    public function service(): BelongsTo
    {
        return $this->belongsTo(Service::class);
    }

    /**
     * Get the admin who reviewed this application.
     */
    public function reviewedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    /**
     * Get the admin who assigned this application.
     */
    public function assignedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_by');
    }

    /**
     * Get the project if this application was approved.
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * Scope for pending applications.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for approved applications.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope for rejected applications.
     */
    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    /**
     * Scope for public applications (no user_id).
     */
    public function scopePublic($query)
    {
        return $query->whereNull('user_id');
    }

    /**
     * Scope for authenticated applications (has user_id).
     */
    public function scopeAuthenticated($query)
    {
        return $query->whereNotNull('user_id');
    }

    /**
     * Scope for completed applications.
     */
    public function scopeCompleted($query)
    {
        return $query->where('is_completed', true);
    }

    /**
     * Mark as reviewed.
     */
    public function markAsReviewed(User $admin, string $status, string $notes = null): void
    {
        $this->update([
            'status' => $status,
            'admin_notes' => $notes,
            'reviewed_at' => now(),
            'reviewed_by' => $admin->id,
        ]);
    }

    /**
     * Check if this is a public application.
     */
    public function isPublic(): bool
    {
        return is_null($this->user_id);
    }

    /**
     * Check if this is an authenticated application.
     */
    public function isAuthenticated(): bool
    {
        return !is_null($this->user_id);
    }

    /**
     * Get the applicant name (either from user or contact info).
     */
    public function getApplicantNameAttribute(): string
    {
        if ($this->user) {
            return $this->user->name;
        }
        return $this->full_name ?? 'Unknown';
    }

    /**
     * Get the applicant email (either from user or contact info).
     */
    public function getApplicantEmailAttribute(): string
    {
        if ($this->user) {
            return $this->user->email;
        }
        return $this->email ?? '';
    }

    /**
     * Mark application as completed.
     */
    public function markAsCompleted(): void
    {
        $this->update([
            'is_completed' => true,
            'submitted_at' => now(),
        ]);
    }

    /**
     * Get status badge color.
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'pending' => 'yellow',
            'under_review' => 'blue',
            'approved' => 'green',
            'rejected' => 'red',
            'in_progress' => 'indigo',
            'completed' => 'green',
            'cancelled' => 'gray',
            default => 'gray'
        };
    }

    /**
     * Check if the application can be edited by the user.
     */
    public function canBeEditedByUser(): bool
    {
        return in_array($this->status, ['pending', 'under_review']);
    }

    /**
     * Check if the application can be deleted by the user.
     */
    public function canBeDeletedByUser(): bool
    {
        return in_array($this->status, ['pending', 'rejected']);
    }

    /**
     * Get available status options.
     */
    public static function getStatusOptions(): array
    {
        return [
            'pending' => 'Pending Review',
            'under_review' => 'Under Review',
            'approved' => 'Approved',
            'rejected' => 'Rejected',
            'in_progress' => 'In Progress',
            'completed' => 'Completed',
            'cancelled' => 'Cancelled',
        ];
    }

    /**
     * Get available priority options.
     */
    public static function getPriorityOptions(): array
    {
        return [
            'low' => 'Low',
            'medium' => 'Medium',
            'high' => 'High',
            'urgent' => 'Urgent',
        ];
    }

    /**
     * Get status label for display.
     */
    public function getStatusLabelAttribute(): string
    {
        $options = self::getStatusOptions();
        return $options[$this->status] ?? ucfirst($this->status);
    }

    /**
     * Get priority label for display.
     */
    public function getPriorityLabelAttribute(): string
    {
        $options = self::getPriorityOptions();
        return $options[$this->priority] ?? ucfirst($this->priority);
    }

    /**
     * Get priority color for UI display.
     */
    public function getPriorityColorAttribute(): string
    {
        return match ($this->priority) {
            'low' => 'green',
            'medium' => 'yellow',
            'high' => 'orange',
            'urgent' => 'red',
            default => 'gray'
        };
    }
}
