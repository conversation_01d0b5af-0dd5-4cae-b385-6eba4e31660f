<?php

namespace Tests\Feature\Services;

use App\Services\ImageService;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Config;
use Tests\TestCase;

class ImageServiceFeatureTest extends TestCase
{
    private ImageService $imageService;
    
    protected function setUp(): void
    {
        parent::setUp();
        $this->imageService = app(ImageService::class);
        
        // Set up test configuration
        Config::set('image.max_file_size', 10 * 1024 * 1024);
        Config::set('image.allowed_mimes', ['image/jpeg', 'image/png', 'image/gif', 'image/webp']);
        Config::set('image.allowed_extensions', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
        Config::set('image.enable_optimization', true);
        Config::set('image.enable_webp_conversion', true);
        Config::set('image.virus_scan.enabled', false);
        Config::set('image.storage.disk', 'testing');
        Config::set('image.storage.path', 'images');
        Config::set('image.storage.temp_path', 'temp/images');
        Config::set('image.logging.enabled', false);
        
        Config::set('image.sizes', [
            'thumbnail' => ['width' => 150, 'height' => 150, 'crop' => true],
            'medium' => ['width' => 400, 'height' => 400, 'crop' => false],
            'large' => ['width' => 800, 'height' => 800, 'crop' => false],
        ]);
        
        Config::set('filesystems.disks.testing', [
            'driver' => 'local',
            'root' => storage_path('app/testing'),
        ]);
        
        Storage::fake('testing');
    }
    
    protected function tearDown(): void
    {
        Storage::disk('testing')->deleteDirectory('');
        parent::tearDown();
    }
    
    /** @test */
    public function it_can_process_uploaded_image_successfully()
    {
        $file = UploadedFile::fake()->image('test.jpg', 800, 600)->size(1024);
        
        $result = $this->imageService->processUploadedImage($file, [
            'subdirectory' => 'test',
            'create_variants' => false,
            'create_webp' => false
        ]);
        
        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('original_path', $result);
        $this->assertArrayHasKey('file_info', $result);
        $this->assertArrayHasKey('processing_time_ms', $result);
        $this->assertIsNumeric($result['processing_time_ms']);
    }
    
    /** @test */
    public function it_creates_webp_version_when_enabled()
    {
        $file = UploadedFile::fake()->image('test.jpg', 400, 300)->size(512);
        
        $result = $this->imageService->processUploadedImage($file, [
            'subdirectory' => 'test',
            'create_variants' => false,
            'create_webp' => true
        ]);
        
        $this->assertTrue($result['success']);
        $this->assertNotNull($result['webp_path']);
        $this->assertStringContainsString('.webp', $result['webp_path']);
    }
    
    /** @test */
    public function it_creates_size_variants_when_enabled()
    {
        $file = UploadedFile::fake()->image('test.jpg', 1000, 800)->size(2048);
        
        $result = $this->imageService->processUploadedImage($file, [
            'subdirectory' => 'test',
            'create_variants' => true,
            'create_webp' => false
        ]);
        
        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('variants', $result);
        $this->assertArrayHasKey('thumbnail', $result['variants']);
        $this->assertArrayHasKey('medium', $result['variants']);
        $this->assertArrayHasKey('large', $result['variants']);
    }
    
    /** @test */
    public function it_creates_both_variants_and_webp_when_enabled()
    {
        $file = UploadedFile::fake()->image('test.png', 1200, 900)->size(3072);
        
        $result = $this->imageService->processUploadedImage($file, [
            'subdirectory' => 'test',
            'create_variants' => true,
            'create_webp' => true
        ]);
        
        $this->assertTrue($result['success']);
        $this->assertNotNull($result['webp_path']);
        $this->assertArrayHasKey('variants', $result);
        $this->assertGreaterThan(0, count($result['variants']));
        
        // Check that WebP variants are also created
        $webpVariants = array_filter($result['variants'], function($path) {
            return strpos($path, '.webp') !== false;
        });
        $this->assertGreaterThan(0, count($webpVariants));
    }
    
    /** @test */
    public function it_fails_processing_invalid_file()
    {
        $file = UploadedFile::fake()->create('document.pdf', 1024, 'application/pdf');
        
        $result = $this->imageService->processUploadedImage($file);
        
        $this->assertFalse($result['success']);
        $this->assertArrayHasKey('errors', $result);
        $this->assertNotEmpty($result['errors']);
    }
    
    /** @test */
    public function it_fails_processing_oversized_file()
    {
        Config::set('image.max_file_size', 512); // 512 bytes
        $file = UploadedFile::fake()->image('large.jpg', 800, 600)->size(1024); // 1KB
        
        $result = $this->imageService->processUploadedImage($file);
        
        $this->assertFalse($result['success']);
        $this->assertContains('File size exceeds maximum allowed size', $result['errors']);
    }
    
    /** @test */
    public function it_can_process_image_from_path()
    {
        // Create a test image file
        $tempPath = tempnam(sys_get_temp_dir(), 'test_image');
        $image = imagecreate(300, 200);
        $white = imagecolorallocate($image, 255, 255, 255);
        imagefill($image, 0, 0, $white);
        imagejpeg($image, $tempPath);
        imagedestroy($image);
        
        $result = $this->imageService->processImageFromPath($tempPath, [
            'create_variants' => false,
            'create_webp' => false
        ]);
        
        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('original_path', $result);
        $this->assertArrayHasKey('file_info', $result);
        $this->assertEquals($tempPath, $result['original_path']);
        
        unlink($tempPath);
    }
    
    /** @test */
    public function it_fails_processing_non_existent_path()
    {
        $result = $this->imageService->processImageFromPath('/non/existent/path.jpg');
        
        $this->assertFalse($result['success']);
        $this->assertContains('File does not exist', $result['errors'][0]);
    }
    
    /** @test */
    public function quick_upload_works_correctly()
    {
        $file = UploadedFile::fake()->image('quick.jpg', 400, 300)->size(512);
        
        $result = $this->imageService->quickUpload($file, 'quick-test');
        
        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('original_path', $result);
        $this->assertNotNull($result['webp_path']); // WebP should be created
        $this->assertEmpty($result['variants']); // No variants for quick upload
    }
    
    /** @test */
    public function full_upload_works_correctly()
    {
        $file = UploadedFile::fake()->image('full.jpg', 800, 600)->size(1024);
        
        $result = $this->imageService->fullUpload($file, 'full-test');
        
        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('original_path', $result);
        $this->assertNotNull($result['webp_path']); // WebP should be created
        $this->assertNotEmpty($result['variants']); // Variants should be created
    }
    
    /** @test */
    public function it_can_delete_image_successfully()
    {
        // First upload an image
        $file = UploadedFile::fake()->image('delete-test.jpg', 400, 300)->size(512);
        $result = $this->imageService->processUploadedImage($file, [
            'subdirectory' => 'delete-test',
            'create_variants' => true,
            'create_webp' => true
        ]);
        
        $this->assertTrue($result['success']);
        
        // Extract just the relative path for deletion
        $originalPath = $result['original_path'];
        $relativePath = str_replace(Storage::disk('testing')->path(''), '', $originalPath);
        $relativePath = ltrim($relativePath, '/\\');
        
        // Now delete it
        $deleted = $this->imageService->deleteImage($relativePath, true);
        
        $this->assertTrue($deleted);
    }
    
    /** @test */
    public function it_handles_custom_processing_options()
    {
        $file = UploadedFile::fake()->image('custom.jpg', 600, 400)->size(1024);
        
        $customSizes = [
            'small' => ['width' => 100, 'height' => 100, 'crop' => true],
            'custom' => ['width' => 300, 'height' => 200, 'crop' => false],
        ];
        
        $result = $this->imageService->processUploadedImage($file, [
            'subdirectory' => 'custom-test',
            'create_variants' => true,
            'create_webp' => true,
            'sizes' => $customSizes,
            'quality' => 90
        ]);
        
        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('variants', $result);
        $this->assertArrayHasKey('small', $result['variants']);
        $this->assertArrayHasKey('custom', $result['variants']);
    }
    
    /** @test */
    public function it_handles_processing_errors_gracefully()
    {
        // Mock a scenario where file storage fails
        Storage::shouldReceive('disk->storeAs')
            ->andReturn(false);
        
        $file = UploadedFile::fake()->image('error-test.jpg', 400, 300)->size(512);
        
        $result = $this->imageService->processUploadedImage($file);
        
        $this->assertFalse($result['success']);
        $this->assertArrayHasKey('errors', $result);
    }
    
    /** @test */
    public function it_returns_consistent_response_structure()
    {
        $file = UploadedFile::fake()->image('structure-test.jpg', 400, 300)->size(512);
        
        $result = $this->imageService->processUploadedImage($file);
        
        // Check required keys exist
        $this->assertArrayHasKey('success', $result);
        $this->assertIsBool($result['success']);
        
        if ($result['success']) {
            $this->assertArrayHasKey('original_path', $result);
            $this->assertArrayHasKey('webp_path', $result);
            $this->assertArrayHasKey('variants', $result);
            $this->assertArrayHasKey('file_info', $result);
            $this->assertArrayHasKey('processing_time_ms', $result);
        } else {
            $this->assertArrayHasKey('errors', $result);
            $this->assertIsArray($result['errors']);
        }
    }
    
    /** @test */
    public function it_preserves_subdirectory_structure()
    {
        $file = UploadedFile::fake()->image('subdir-test.jpg', 400, 300)->size(512);
        
        $result = $this->imageService->processUploadedImage($file, [
            'subdirectory' => 'level1/level2/level3'
        ]);
        
        $this->assertTrue($result['success']);
        $this->assertStringContainsString('level1/level2/level3', $result['original_path']);
    }
}
