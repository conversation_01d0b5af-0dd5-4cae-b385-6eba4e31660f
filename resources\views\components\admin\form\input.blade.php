@props([
    'label' => null,
    'name' => null,
    'type' => 'text',
    'required' => false,
    'placeholder' => null,
    'help' => null,
    'error' => null,
    'value' => null,
    'prefix' => null,
    'suffix' => null
])

@php
$inputClasses = 'w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200';

if ($error) {
    $inputClasses .= ' border-red-500 focus:ring-red-500';
}

if ($prefix) {
    $inputClasses = str_replace('px-4', 'pl-10 pr-4', $inputClasses);
}

if ($suffix) {
    $inputClasses = str_replace('px-4', 'pl-4 pr-10', $inputClasses);
}

if ($prefix && $suffix) {
    $inputClasses = str_replace('pl-10 pr-4', 'pl-10', $inputClasses);
    $inputClasses = str_replace('pl-4 pr-10', 'pr-10', $inputClasses);
}
@endphp

<div {{ $attributes->only('class') }}>
    @if($label)
        <label for="{{ $name }}" class="block text-sm font-medium text-gray-700 mb-2">
            {{ $label }}
            @if($required)
                <span class="text-red-500">*</span>
            @endif
        </label>
    @endif

    <div class="relative">
        @if($prefix)
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <span class="text-gray-500 text-sm">{{ $prefix }}</span>
            </div>
        @endif

        <input type="{{ $type }}"
               @if($name) name="{{ $name }}" id="{{ $name }}" @endif
               @if($value !== null) value="{{ $value }}" @endif
               @if($placeholder) placeholder="{{ $placeholder }}" @endif
               @if($required) required @endif
               {{ $attributes->except(['class', 'label', 'name', 'type', 'required', 'placeholder', 'help', 'error', 'value', 'prefix', 'suffix'])->merge(['class' => $inputClasses]) }}>

        @if($suffix)
            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                <span class="text-gray-500 text-sm">{{ $suffix }}</span>
            </div>
        @endif
    </div>

    @if($help && !$error)
        <p class="mt-1 text-xs text-gray-500">{{ $help }}</p>
    @endif

    @if($error)
        <p class="mt-1 text-sm text-red-600">{{ $error }}</p>
    @endif
</div>
