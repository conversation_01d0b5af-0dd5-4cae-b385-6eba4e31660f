@extends('layouts.app')

@section('title', 'Data Analytics & Consultancy - ' . __('common.company_name'))
@section('meta_description', 'Leading data analytics consulting company in South Africa specializing in business intelligence solutions, data visualization services, predictive analytics, big data consulting, and AI-powered analytics for data-driven decision making.')
@section('meta_keywords', 'data analytics consulting South Africa, business intelligence solutions, data visualization services, predictive analytics, big data consulting, data science consulting, AI-powered analytics, machine learning analytics, data warehouse solutions, business intelligence dashboards, data analytics company South Africa')

@push('structured_data')
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Service",
  "name": "Data Analytics Consulting Services",
  "description": "Leading data analytics consulting company in South Africa specializing in business intelligence solutions, predictive analytics, data visualization services, and AI-powered analytics",
  "provider": {
    "@type": "Organization",
    "name": "{{ __('common.company_name') }}",
    "url": "{{ url('/') }}",
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "ZA",
      "addressRegion": "South Africa"
    }
  },
  "areaServed": {
    "@type": "Country",
    "name": "South Africa"
  },
  "serviceType": "Data Analytics Consulting",
  "hasOfferCatalog": {
    "@type": "OfferCatalog",
    "name": "Data Analytics Services",
    "itemListElement": [
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Business Intelligence Solutions",
          "description": "Comprehensive business intelligence dashboards and reporting systems"
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Predictive Analytics",
          "description": "AI-powered predictive analytics and machine learning models"
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Data Visualization Services",
          "description": "Interactive data visualization and dashboard development"
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Big Data Consulting",
          "description": "Big data architecture and analytics consulting services"
        }
      }
    ]
  },
  "keywords": "data analytics consulting South Africa, business intelligence solutions, predictive analytics, data visualization services, big data consulting"
}
</script>
@endpush

@section('content')
<!-- Hero Section -->
<section class="relative bg-gradient-blue text-white overflow-hidden">
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></g></svg>');"></div>
    </div>
    
    <div class="container mx-auto px-4 py-20 lg:py-32 relative z-10">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div class="text-center lg:text-left">
                <h1 class="heading-1 text-white mb-6">
                    Data Analytics Consulting in South Africa
                </h1>
                <p class="text-lead text-blue-100 mb-8">
                    Leading data analytics consulting company specializing in business intelligence solutions, predictive analytics, and AI-powered data visualization. Transform your data into strategic insights for competitive advantage.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <a href="{{ route('project-applications.create') }}" class="btn-primary bg-white text-blue-600 hover:bg-blue-50">
                        Start Your Project
                        <svg class="ml-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </a>
                    <a href="{{ route('contact') }}" class="btn-outline border-white text-white hover:bg-white hover:text-blue-600">
                        Free Consultation
                    </a>
                </div>
            </div>
            
            <div class="relative">
                <div class="relative z-10">
                    <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-lg p-8">
                        <div class="space-y-6">
                            <!-- Dashboard Preview -->
                            <div class="bg-gray-800 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-4">
                                    <h3 class="text-white font-semibold">Analytics Dashboard</h3>
                                    <div class="flex space-x-1">
                                        <div class="w-2 h-2 bg-red-400 rounded-full"></div>
                                        <div class="w-2 h-2 bg-yellow-400 rounded-full"></div>
                                        <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                                    </div>
                                </div>
                                <!-- Chart representation -->
                                <div class="space-y-3">
                                    <div class="flex items-end space-x-1 h-16">
                                        <div class="bg-blue-400 w-4 h-8 rounded-t"></div>
                                        <div class="bg-blue-400 w-4 h-12 rounded-t"></div>
                                        <div class="bg-blue-400 w-4 h-6 rounded-t"></div>
                                        <div class="bg-blue-400 w-4 h-14 rounded-t"></div>
                                        <div class="bg-blue-400 w-4 h-10 rounded-t"></div>
                                        <div class="bg-blue-400 w-4 h-16 rounded-t"></div>
                                    </div>
                                    <div class="text-xs text-gray-400 text-center">Revenue Growth Trends</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Background decoration -->
                <div class="absolute -top-4 -right-4 w-72 h-72 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
                <div class="absolute -bottom-8 -left-4 w-72 h-72 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
            </div>
        </div>
    </div>
</section>

<!-- Services Overview -->
<section class="py-20 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="heading-2 mb-4">
                Business Intelligence <span class="text-blue-600">Solutions</span>
            </h2>
            <p class="text-lead max-w-3xl mx-auto">
                Advanced data analytics consulting services including predictive analytics, AI-powered insights, and comprehensive business intelligence solutions for data-driven decision making.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Business Intelligence -->
            <div class="card-hover group">
                <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-blue-600 transition-colors">
                    <svg class="w-8 h-8 text-blue-600 group-hover:text-white transition-colors" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Business Intelligence Solutions</h3>
                <p class="text-gray-600 mb-6">Advanced business intelligence dashboards and data warehouse solutions with real-time analytics and automated reporting systems.</p>
                <ul class="space-y-2 text-sm text-gray-600">
                    <li class="flex items-center">
                        <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Interactive Dashboards
                    </li>
                    <li class="flex items-center">
                        <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Real-time Reporting
                    </li>
                    <li class="flex items-center">
                        <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        KPI Monitoring
                    </li>
                </ul>
            </div>

            <!-- Data Visualization -->
            <div class="card-hover group">
                <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-blue-600 transition-colors">
                    <svg class="w-8 h-8 text-blue-600 group-hover:text-white transition-colors" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Data Visualization Services</h3>
                <p class="text-gray-600 mb-6">Professional data visualization services with interactive dashboards, custom analytics reports, and compelling data storytelling for executive presentations.</p>
                <ul class="space-y-2 text-sm text-gray-600">
                    <li class="flex items-center">
                        <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Custom Charts & Graphs
                    </li>
                    <li class="flex items-center">
                        <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Interactive Reports
                    </li>
                    <li class="flex items-center">
                        <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Data Storytelling
                    </li>
                </ul>
            </div>

            <!-- Predictive Analytics -->
            <div class="card-hover group">
                <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-blue-600 transition-colors">
                    <svg class="w-8 h-8 text-blue-600 group-hover:text-white transition-colors" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                        <path d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Predictive Analytics & AI</h3>
                <p class="text-gray-600 mb-6">AI-powered predictive analytics using machine learning algorithms for trend forecasting, risk assessment, and strategic business planning.</p>
                <ul class="space-y-2 text-sm text-gray-600">
                    <li class="flex items-center">
                        <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Trend Forecasting
                    </li>
                    <li class="flex items-center">
                        <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Risk Assessment
                    </li>
                    <li class="flex items-center">
                        <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Customer Behavior Analysis
                    </li>
                </ul>
            </div>
        </div>
    </div>
</section>

<!-- Technologies & Tools -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="heading-2 mb-4">
                Technologies & <span class="text-blue-600">Tools</span>
            </h2>
            <p class="text-lead max-w-3xl mx-auto">
                We use industry-leading tools and technologies to deliver powerful analytics solutions.
            </p>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8">
            <!-- Power BI -->
            <div class="text-center group">
                <div class="w-16 h-16 bg-white rounded-lg shadow-md flex items-center justify-center mx-auto mb-4 group-hover:shadow-lg transition-shadow">
                    <div class="w-8 h-8 bg-yellow-500 rounded"></div>
                </div>
                <h4 class="font-semibold text-gray-900">Power BI</h4>
            </div>

            <!-- Tableau -->
            <div class="text-center group">
                <div class="w-16 h-16 bg-white rounded-lg shadow-md flex items-center justify-center mx-auto mb-4 group-hover:shadow-lg transition-shadow">
                    <div class="w-8 h-8 bg-blue-600 rounded"></div>
                </div>
                <h4 class="font-semibold text-gray-900">Tableau</h4>
            </div>

            <!-- Google Analytics -->
            <div class="text-center group">
                <div class="w-16 h-16 bg-white rounded-lg shadow-md flex items-center justify-center mx-auto mb-4 group-hover:shadow-lg transition-shadow">
                    <div class="w-8 h-8 bg-orange-500 rounded"></div>
                </div>
                <h4 class="font-semibold text-gray-900">Google Analytics</h4>
            </div>

            <!-- Python -->
            <div class="text-center group">
                <div class="w-16 h-16 bg-white rounded-lg shadow-md flex items-center justify-center mx-auto mb-4 group-hover:shadow-lg transition-shadow">
                    <div class="w-8 h-8 bg-blue-500 rounded"></div>
                </div>
                <h4 class="font-semibold text-gray-900">Python</h4>
            </div>

            <!-- R -->
            <div class="text-center group">
                <div class="w-16 h-16 bg-white rounded-lg shadow-md flex items-center justify-center mx-auto mb-4 group-hover:shadow-lg transition-shadow">
                    <div class="w-8 h-8 bg-blue-700 rounded"></div>
                </div>
                <h4 class="font-semibold text-gray-900">R</h4>
            </div>

            <!-- SQL -->
            <div class="text-center group">
                <div class="w-16 h-16 bg-white rounded-lg shadow-md flex items-center justify-center mx-auto mb-4 group-hover:shadow-lg transition-shadow">
                    <div class="w-8 h-8 bg-gray-700 rounded"></div>
                </div>
                <h4 class="font-semibold text-gray-900">SQL</h4>
            </div>
        </div>
    </div>
</section>

<!-- Process Section -->
<section class="py-20 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="heading-2 mb-4">
                Our <span class="text-blue-600">Process</span>
            </h2>
            <p class="text-lead max-w-3xl mx-auto">
                We follow a structured approach to ensure your analytics project delivers maximum value.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <!-- Step 1 -->
            <div class="text-center">
                <div class="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                    1
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Data Assessment</h3>
                <p class="text-gray-600 text-sm">
                    We analyze your current data sources, quality, and infrastructure to understand your analytics needs.
                </p>
            </div>

            <!-- Step 2 -->
            <div class="text-center">
                <div class="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                    2
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Strategy Development</h3>
                <p class="text-gray-600 text-sm">
                    We create a comprehensive analytics strategy aligned with your business objectives and goals.
                </p>
            </div>

            <!-- Step 3 -->
            <div class="text-center">
                <div class="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                    3
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Implementation</h3>
                <p class="text-gray-600 text-sm">
                    We build and deploy your analytics solution with proper data integration and visualization.
                </p>
            </div>

            <!-- Step 4 -->
            <div class="text-center">
                <div class="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                    4
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Training & Support</h3>
                <p class="text-gray-600 text-sm">
                    We provide comprehensive training and ongoing support to ensure you get maximum value from your analytics.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-20 bg-blue-600 text-white">
    <div class="container mx-auto px-4 text-center">
        <h2 class="heading-2 text-white mb-4">
            Ready to Unlock Your Data's Potential?
        </h2>
        <p class="text-lead text-blue-100 mb-8 max-w-2xl mx-auto">
            Let's discuss how our data analytics solutions can help you make better decisions and drive business growth.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{{ route('project-applications.create') }}" class="btn-primary bg-white text-blue-600 hover:bg-blue-50">
                Start Your Analytics Project
                <svg class="ml-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </a>
            <a href="{{ route('contact') }}" class="btn-outline border-white text-white hover:bg-white hover:text-blue-600">
                Get Free Consultation
            </a>
        </div>
    </div>
</section>
@endsection
