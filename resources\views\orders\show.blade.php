@extends('layouts.dashboard')

@section('title', 'Order #' . $order->order_number . ' - ' . __('common.company_name'))
@section('page_title', 'Order Details')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <div class="flex items-center space-x-3">
                    <a href="{{ route('orders.index') }}" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </a>
                    <h1 class="text-2xl font-bold text-gray-900">Order #{{ $order->order_number }}</h1>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                        @if($order->status === 'delivered') bg-green-100 text-green-800
                        @elseif($order->status === 'pending') bg-yellow-100 text-yellow-800
                        @elseif($order->status === 'confirmed') bg-blue-100 text-blue-800
                        @elseif($order->status === 'processing') bg-indigo-100 text-indigo-800
                        @elseif($order->status === 'shipped') bg-purple-100 text-purple-800
                        @elseif($order->status === 'cancelled') bg-red-100 text-red-800
                        @elseif($order->status === 'refunded') bg-gray-100 text-gray-800
                        @else bg-gray-100 text-gray-800
                        @endif">
                        {{ ucfirst($order->status) }}
                    </span>
                </div>
                <p class="text-gray-600 mt-1">Placed on {{ $order->created_at->format('F j, Y \a\t g:i A') }}</p>
            </div>
            
            <div class="flex space-x-3">
                @if(in_array($order->status, ['pending', 'cancelled']))
                    <form action="{{ route('orders.destroy', $order) }}" method="POST" 
                          onsubmit="return confirm('Are you sure you want to delete this order? This action cannot be undone.')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="bg-red-100 text-red-700 px-4 py-2 rounded-lg hover:bg-red-200 transition-colors">
                            Delete Order
                        </button>
                    </form>
                @endif
                
                <a href="{{ route('shop.index') }}" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    Continue Shopping
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Order Items -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Order Items</h3>
                </div>
                <div class="divide-y divide-gray-200">
                    @foreach($order->items as $item)
                        <div class="p-6">
                            <div class="flex items-start space-x-4">
                                <!-- Product Image -->
                                <div class="flex-shrink-0">
                                    @if($item->product && $item->product->featured_image)
                                        <img src="{{ Storage::url($item->product->featured_image) }}" 
                                             alt="{{ $item->product->name }}" 
                                             class="w-16 h-16 object-cover rounded-lg">
                                    @else
                                        <div class="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                                            <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                            </svg>
                                        </div>
                                    @endif
                                </div>

                                <!-- Product Details -->
                                <div class="flex-1">
                                    <h4 class="text-lg font-medium text-gray-900">
                                        {{ $item->product_snapshot['name'] ?? $item->product->name ?? 'Product' }}
                                    </h4>
                                    
                                    @if($item->variant)
                                        <p class="text-sm text-gray-600 mt-1">
                                            Variant: {{ $item->variant->name }}
                                        </p>
                                    @endif
                                    
                                    @if(isset($item->product_snapshot['sku']))
                                        <p class="text-sm text-gray-500 mt-1">SKU: {{ $item->product_snapshot['sku'] }}</p>
                                    @endif

                                    <div class="mt-2 flex items-center justify-between">
                                        <div class="flex items-center space-x-4">
                                            <span class="text-sm text-gray-600">Qty: {{ $item->quantity }}</span>
                                            <span class="text-sm text-gray-600">
                                                Unit Price: {{ $order->currency->symbol ?? 'R' }}{{ number_format($item->unit_price, 2) }}
                                            </span>
                                        </div>
                                        <span class="text-lg font-semibold text-gray-900">
                                            {{ $order->currency->symbol ?? 'R' }}{{ number_format($item->total_price, 2) }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Order Summary & Details -->
        <div class="space-y-6">
            <!-- Order Summary -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Order Summary</h3>
                
                <div class="space-y-3">
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Subtotal</span>
                        <span class="text-gray-900">{{ $order->currency->symbol ?? 'R' }}{{ number_format($order->subtotal, 2) }}</span>
                    </div>
                    
                    @if($order->tax_amount > 0)
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Tax</span>
                            <span class="text-gray-900">{{ $order->currency->symbol ?? 'R' }}{{ number_format($order->tax_amount, 2) }}</span>
                        </div>
                    @endif
                    
                    @if($order->shipping_amount > 0)
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Shipping</span>
                            <span class="text-gray-900">{{ $order->currency->symbol ?? 'R' }}{{ number_format($order->shipping_amount, 2) }}</span>
                        </div>
                    @endif
                    
                    @if($order->discount_amount > 0)
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">
                                Discount
                                @if($order->coupon_code)
                                    ({{ $order->coupon_code }})
                                @endif
                            </span>
                            <span class="text-green-600">-{{ $order->currency->symbol ?? 'R' }}{{ number_format($order->discount_amount, 2) }}</span>
                        </div>
                    @endif
                    
                    <div class="border-t pt-3">
                        <div class="flex justify-between">
                            <span class="text-lg font-medium text-gray-900">Total</span>
                            <span class="text-lg font-bold text-gray-900">{{ $order->currency->symbol ?? 'R' }}{{ number_format($order->total_amount, 2) }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Information -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Payment Information</h3>
                
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Payment Status</span>
                        <span class="text-sm font-medium
                            @if($order->payment_status === 'completed') text-green-600
                            @elseif($order->payment_status === 'pending') text-yellow-600
                            @elseif($order->payment_status === 'failed') text-red-600
                            @elseif($order->payment_status === 'refunded') text-gray-600
                            @else text-gray-600
                            @endif">
                            {{ ucfirst($order->payment_status) }}
                        </span>
                    </div>
                    
                    @if($order->payment_reference)
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Payment Reference</span>
                            <span class="text-sm text-gray-900 font-mono">{{ $order->payment_reference }}</span>
                        </div>
                    @endif
                    
                    @if($order->paid_at)
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Paid On</span>
                            <span class="text-sm text-gray-900">{{ $order->paid_at->format('M j, Y g:i A') }}</span>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Shipping Information -->
            @if($order->shipping_address)
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Shipping Address</h3>
                    
                    <div class="text-sm text-gray-600 space-y-1">
                        @if(isset($order->shipping_address['first_name']) || isset($order->shipping_address['last_name']))
                            <p class="font-medium text-gray-900">
                                {{ $order->shipping_address['first_name'] ?? '' }} {{ $order->shipping_address['last_name'] ?? '' }}
                            </p>
                        @endif
                        
                        @if(isset($order->shipping_address['company']) && $order->shipping_address['company'])
                            <p>{{ $order->shipping_address['company'] }}</p>
                        @endif
                        
                        @if(isset($order->shipping_address['address_line_1']))
                            <p>{{ $order->shipping_address['address_line_1'] }}</p>
                        @endif
                        
                        @if(isset($order->shipping_address['address_line_2']) && $order->shipping_address['address_line_2'])
                            <p>{{ $order->shipping_address['address_line_2'] }}</p>
                        @endif
                        
                        <p>
                            @if(isset($order->shipping_address['city']))
                                {{ $order->shipping_address['city'] }}
                            @endif
                            @if(isset($order->shipping_address['state']) && $order->shipping_address['state'])
                                , {{ $order->shipping_address['state'] }}
                            @endif
                            @if(isset($order->shipping_address['postal_code']))
                                {{ $order->shipping_address['postal_code'] }}
                            @endif
                        </p>
                        
                        @if(isset($order->shipping_address['country']))
                            <p>{{ $order->shipping_address['country'] }}</p>
                        @endif
                        
                        @if(isset($order->shipping_address['phone']) && $order->shipping_address['phone'])
                            <p class="mt-2">Phone: {{ $order->shipping_address['phone'] }}</p>
                        @endif
                    </div>
                </div>
            @endif

            <!-- Order Timeline -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Order Timeline</h3>
                
                <div class="space-y-4">
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                        <div>
                            <p class="text-sm font-medium text-gray-900">Order Placed</p>
                            <p class="text-xs text-gray-500">{{ $order->created_at->format('M j, Y g:i A') }}</p>
                        </div>
                    </div>
                    
                    @if($order->payment_status === 'completed' && $order->paid_at)
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0 w-2 h-2 bg-green-600 rounded-full mt-2"></div>
                            <div>
                                <p class="text-sm font-medium text-gray-900">Payment Confirmed</p>
                                <p class="text-xs text-gray-500">{{ $order->paid_at->format('M j, Y g:i A') }}</p>
                            </div>
                        </div>
                    @endif
                    
                    @if($order->shipped_at)
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0 w-2 h-2 bg-purple-600 rounded-full mt-2"></div>
                            <div>
                                <p class="text-sm font-medium text-gray-900">Order Shipped</p>
                                <p class="text-xs text-gray-500">{{ $order->shipped_at->format('M j, Y g:i A') }}</p>
                            </div>
                        </div>
                    @endif
                    
                    @if($order->delivered_at)
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0 w-2 h-2 bg-green-600 rounded-full mt-2"></div>
                            <div>
                                <p class="text-sm font-medium text-gray-900">Order Delivered</p>
                                <p class="text-xs text-gray-500">{{ $order->delivered_at->format('M j, Y g:i A') }}</p>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            @if($order->notes)
                <!-- Order Notes -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Order Notes</h3>
                    <p class="text-sm text-gray-600">{{ $order->notes }}</p>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
