<?php

namespace Database\Factories;

use App\Models\ProjectApplication;
use App\Models\User;
use App\Models\Service;
use App\Models\Project;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProjectApplication>
 */
class ProjectApplicationFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ProjectApplication::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'service_id' => Service::factory(),
            'project_id' => Project::factory(),
            'title' => $this->faker->sentence(4),
            'description' => $this->faker->paragraph(3),
            'budget_range' => $this->faker->randomElement(['1000-5000', '5000-10000', '10000-25000', '25000+']),
            'timeline' => $this->faker->randomElement(['1-2 weeks', '2-4 weeks', '1-2 months', '2-3 months', '3+ months']),
            'priority' => $this->faker->randomElement(['low', 'medium', 'high', 'urgent']),
            'status' => 'pending',
            'requirements' => $this->faker->paragraph(2),
            'attachments' => null,
            'admin_notes' => null,
            'reviewed_by' => null,
            'reviewed_at' => null,
        ];
    }

    /**
     * Indicate that the project application is approved.
     */
    public function approved(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'approved',
            'reviewed_by' => User::factory(),
            'reviewed_at' => now(),
            'admin_notes' => $this->faker->sentence(),
        ]);
    }

    /**
     * Indicate that the project application is rejected.
     */
    public function rejected(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'rejected',
            'reviewed_by' => User::factory(),
            'reviewed_at' => now(),
            'admin_notes' => $this->faker->sentence(),
        ]);
    }

    /**
     * Indicate that the project application is in progress.
     */
    public function inProgress(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'in_progress',
            'reviewed_by' => User::factory(),
            'reviewed_at' => now(),
            'admin_notes' => $this->faker->sentence(),
        ]);
    }

    /**
     * Indicate that the project application has attachments.
     */
    public function withAttachments(): static
    {
        return $this->state(fn (array $attributes) => [
            'attachments' => [
                [
                    'original_name' => 'document.pdf',
                    'stored_name' => 'project-applications/2024/01/document_123.pdf',
                    'size' => 1024000,
                    'mime_type' => 'application/pdf',
                    'uploaded_at' => now()->toISOString(),
                ],
                [
                    'original_name' => 'requirements.docx',
                    'stored_name' => 'project-applications/2024/01/requirements_456.docx',
                    'size' => 512000,
                    'mime_type' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    'uploaded_at' => now()->toISOString(),
                ],
            ],
        ]);
    }
}
