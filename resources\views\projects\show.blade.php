@extends('layouts.dashboard')

@section('title', $project->title . ' - Project Details - ' . __('common.company_name'))
@section('page_title', 'Project Details')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
                <nav class="flex items-center space-x-2 text-sm text-gray-500 mb-2">
                    <a href="{{ route('projects.index') }}" class="hover:text-gray-700">My Projects</a>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <span class="text-gray-900">{{ $project->title }}</span>
                </nav>
                <h1 class="text-2xl font-bold text-gray-900">{{ $project->title }}</h1>
                <p class="text-gray-600">{{ $project->description }}</p>
            </div>
            <div class="flex items-center gap-3">
                @if($project->project_url)
                    <a href="{{ $project->project_url }}" target="_blank" 
                       class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors duration-200">
                        <svg class="w-4 h-4 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                        </svg>
                        View Live Project
                    </a>
                @endif
                <a href="{{ route('contact') }}" 
                   class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200">
                    <svg class="w-4 h-4 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                    Contact Us
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Project Status and Progress -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Project Status & Progress</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Status and Priority -->
                    <div>
                        <div class="flex items-center gap-3 mb-4">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                @if($project->status === 'completed') bg-green-100 text-green-800
                                @elseif($project->status === 'in_progress') bg-blue-100 text-blue-800
                                @elseif($project->status === 'review') bg-purple-100 text-purple-800
                                @elseif($project->status === 'planning') bg-gray-100 text-gray-800
                                @elseif($project->status === 'on_hold') bg-yellow-100 text-yellow-800
                                @elseif($project->status === 'cancelled') bg-red-100 text-red-800
                                @else bg-gray-100 text-gray-800
                                @endif">
                                {{ ucfirst(str_replace('_', ' ', $project->status)) }}
                            </span>
                            
                            @if($project->priority)
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                    @if($project->priority === 'urgent') bg-red-100 text-red-800
                                    @elseif($project->priority === 'high') bg-orange-100 text-orange-800
                                    @elseif($project->priority === 'medium') bg-yellow-100 text-yellow-800
                                    @elseif($project->priority === 'low') bg-green-100 text-green-800
                                    @else bg-gray-100 text-gray-800
                                    @endif">
                                    {{ ucfirst($project->priority) }} Priority
                                </span>
                            @endif
                        </div>
                        
                        @if($project->service)
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Service Type</label>
                                <span class="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-50 text-blue-700">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                    </svg>
                                    {{ $project->service->title }}
                                </span>
                            </div>
                        @endif
                    </div>
                    
                    <!-- Progress Bar -->
                    @if($project->estimated_hours && $project->actual_hours)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Project Progress</label>
                            <div class="mb-2">
                                <div class="flex justify-between text-sm text-gray-600 mb-1">
                                    <span>{{ number_format($project->actual_hours, 1) }} / {{ number_format($project->estimated_hours, 1) }} hours</span>
                                    <span>{{ $project->progress_percentage }}%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-3">
                                    <div class="bg-blue-600 h-3 rounded-full transition-all duration-300" style="width: {{ $project->progress_percentage }}%"></div>
                                </div>
                            </div>
                            @if($project->progress_percentage >= 100)
                                <p class="text-sm text-green-600 font-medium">Project hours completed!</p>
                            @elseif($project->progress_percentage >= 75)
                                <p class="text-sm text-yellow-600 font-medium">Nearing completion</p>
                            @else
                                <p class="text-sm text-gray-600">{{ 100 - $project->progress_percentage }}% remaining</p>
                            @endif
                        </div>
                    @endif
                </div>
            </div>

            <!-- Project Description -->
            @if($project->content)
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Project Description</h2>
                    <div class="prose max-w-none text-gray-700">
                        {!! nl2br(e($project->content)) !!}
                    </div>
                </div>
            @endif

            <!-- Project Gallery -->
            @if($project->gallery && count($project->gallery) > 0)
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Project Gallery</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        @foreach($project->gallery as $image)
                            <div class="aspect-w-16 aspect-h-9">
                                <img src="{{ $image }}" alt="Project image" class="w-full h-48 object-cover rounded-lg">
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif

            <!-- Project Actions -->
            @if(in_array($project->status, ['planning', 'cancelled', 'on_hold']))
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Project Actions</h2>
                    <div class="flex items-center justify-between p-4 bg-red-50 rounded-lg">
                        <div>
                            <h3 class="text-sm font-medium text-red-800">Delete Project</h3>
                            <p class="text-sm text-red-600">Permanently remove this project from your account. This action cannot be undone.</p>
                        </div>
                        <form action="{{ route('projects.destroy', $project) }}" method="POST" class="inline-block" 
                              onsubmit="return confirm('Are you sure you want to delete this project? This action cannot be undone.')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" 
                                    class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors duration-200">
                                Delete Project
                            </button>
                        </form>
                    </div>
                </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Project Details -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Project Details</h2>
                
                <div class="space-y-4">
                    @if($project->total_amount)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Project Value</label>
                            <p class="text-2xl font-bold text-gray-900">{{ $project->formatted_total }}</p>
                            @if($project->hourly_rate)
                                <p class="text-sm text-gray-500">R{{ number_format($project->hourly_rate, 2) }} per hour</p>
                            @endif
                        </div>
                    @endif
                    
                    @if($project->start_date)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                            <p class="text-gray-900">{{ $project->start_date->format('F j, Y') }}</p>
                        </div>
                    @endif
                    
                    @if($project->end_date)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                @if($project->status === 'completed') Completion Date @else Target Date @endif
                            </label>
                            <p class="text-gray-900">{{ $project->end_date->format('F j, Y') }}</p>
                            @if($project->end_date->isPast() && $project->status !== 'completed')
                                <p class="text-sm text-red-600 mt-1">Overdue</p>
                            @elseif($project->end_date->isToday())
                                <p class="text-sm text-yellow-600 mt-1">Due today</p>
                            @elseif($project->end_date->isFuture())
                                <p class="text-sm text-gray-500 mt-1">{{ $project->end_date->diffForHumans() }}</p>
                            @endif
                        </div>
                    @endif
                    
                    @if($project->client_name)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Client Name</label>
                            <p class="text-gray-900">{{ $project->client_name }}</p>
                        </div>
                    @endif
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Created</label>
                        <p class="text-gray-900">{{ $project->created_at->format('F j, Y') }}</p>
                        <p class="text-sm text-gray-500">{{ $project->created_at->diffForHumans() }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Last Updated</label>
                        <p class="text-gray-900">{{ $project->updated_at->format('F j, Y') }}</p>
                        <p class="text-sm text-gray-500">{{ $project->updated_at->diffForHumans() }}</p>
                    </div>
                </div>
            </div>

            <!-- Project Timeline -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Project Timeline</h2>
                
                <div class="space-y-4">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="w-3 h-3 bg-blue-600 rounded-full mt-1"></div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-900">Project Created</p>
                            <p class="text-sm text-gray-500">{{ $project->created_at->format('M j, Y \a\t g:i A') }}</p>
                        </div>
                    </div>
                    
                    @if($project->start_date)
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <div class="w-3 h-3 {{ $project->start_date->isPast() ? 'bg-green-600' : 'bg-gray-300' }} rounded-full mt-1"></div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-900">Project Started</p>
                                <p class="text-sm text-gray-500">{{ $project->start_date->format('M j, Y') }}</p>
                            </div>
                        </div>
                    @endif
                    
                    @if($project->end_date)
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <div class="w-3 h-3 {{ $project->status === 'completed' ? 'bg-green-600' : 'bg-gray-300' }} rounded-full mt-1"></div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-900">
                                    @if($project->status === 'completed') Project Completed @else Target Completion @endif
                                </p>
                                <p class="text-sm text-gray-500">{{ $project->end_date->format('M j, Y') }}</p>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
                
                <div class="space-y-3">
                    <a href="{{ route('projects.index') }}" 
                       class="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors duration-200 text-center block">
                        ← Back to Projects
                    </a>
                    
                    <a href="{{ route('contact') }}" 
                       class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200 text-center block">
                        Contact About Project
                    </a>
                    
                    @if($project->project_url)
                        <a href="{{ $project->project_url }}" target="_blank" 
                           class="w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors duration-200 text-center block">
                            View Live Project
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success/Error Messages -->
@if(session('success'))
    <div class="fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg shadow-lg z-50" id="success-message">
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            {{ session('success') }}
        </div>
    </div>
@endif

@if(session('error'))
    <div class="fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg shadow-lg z-50" id="error-message">
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
            {{ session('error') }}
        </div>
    </div>
@endif

<script>
    // Auto-hide success/error messages after 5 seconds
    setTimeout(function() {
        const successMessage = document.getElementById('success-message');
        const errorMessage = document.getElementById('error-message');
        if (successMessage) successMessage.style.display = 'none';
        if (errorMessage) errorMessage.style.display = 'none';
    }, 5000);
</script>
@endsection
