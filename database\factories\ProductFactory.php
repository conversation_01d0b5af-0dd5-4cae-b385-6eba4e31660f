<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
class ProductFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = $this->faker->words(3, true);

        return [
            'name' => $name,
            'slug' => \Illuminate\Support\Str::slug($name),
            'description' => $this->faker->paragraph(),
            'price' => $this->faker->randomFloat(2, 10, 1000),
            'currency_id' => \App\Models\Currency::factory(),
            'is_active' => true,
            'track_inventory' => true,
            'inventory_quantity' => $this->faker->numberBetween(0, 100),
            'sku' => $this->faker->unique()->regexify('[A-Z]{3}-[0-9]{3}'),
            'brand' => $this->faker->company(),
            'model_number' => $this->faker->regexify('[A-Z]{2}[0-9]{4}'),
        ];
    }
}
