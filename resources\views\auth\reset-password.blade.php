@extends('layouts.auth')

@section('title', __('Reset Password'))

@section('content')
<div class="auth-container">
    <div class="auth-card">
        <div class="auth-header">
            <h1 class="auth-title">{{ __('Reset Password') }}</h1>
            <p class="auth-subtitle">{{ __('Enter your new password below') }}</p>
        </div>

        <!-- Messages Container -->
        <div id="form-messages" class="form-messages"></div>

        <form id="reset-password-form" class="auth-form" method="POST" action="{{ route('password.update') }}">
            @csrf

            <!-- Hidden Token Field -->
            <input type="hidden" name="token" value="{{ $token }}">

            <!-- Email Field (readonly) -->
            <div class="floating-input-group">
                <input 
                    type="email" 
                    name="email" 
                    id="email"
                    class="floating-input @error('email') error @enderror" 
                    value="{{ $email ?? old('email') }}"
                    required 
                    readonly
                    placeholder=" "
                >
                <label for="email" class="floating-label">{{ __('auth.email') }}</label>
                @error('email')
                    <span class="error-message">{{ $message }}</span>
                @enderror
            </div>

            <!-- Password Field -->
            <div class="floating-input-group">
                <input 
                    type="password" 
                    name="password" 
                    id="password"
                    class="floating-input @error('password') error @enderror" 
                    required 
                    autocomplete="new-password"
                    placeholder=" "
                    minlength="8"
                >
                <label for="password" class="floating-label">{{ __('auth.new_password') }}</label>
                <button type="button" class="password-toggle" onclick="togglePassword('password')">
                    <svg class="eye-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                </button>
                @error('password')
                    <span class="error-message">{{ $message }}</span>
                @enderror
            </div>

            <!-- Password Strength Indicator -->
            <div id="password-strength" class="password-strength">
                <div class="strength-bar">
                    <div class="strength-fill"></div>
                </div>
                <div class="strength-text">{{ __('auth.password_strength') }}: <span class="strength-level">{{ __('auth.weak') }}</span></div>
            </div>

            <!-- Password Confirmation Field -->
            <div class="floating-input-group">
                <input 
                    type="password" 
                    name="password_confirmation" 
                    id="password_confirmation"
                    class="floating-input @error('password_confirmation') error @enderror" 
                    required 
                    autocomplete="new-password"
                    placeholder=" "
                    minlength="8"
                >
                <label for="password_confirmation" class="floating-label">{{ __('auth.confirm_password') }}</label>
                <button type="button" class="password-toggle" onclick="togglePassword('password_confirmation')">
                    <svg class="eye-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                </button>
                @error('password_confirmation')
                    <span class="error-message">{{ $message }}</span>
                @enderror
            </div>

            <!-- Password Requirements -->
            <div class="password-requirements">
                <p class="requirements-title">{{ __('auth.password_requirements') }}:</p>
                <ul class="requirements-list">
                    <li id="req-length" class="requirement">{{ __('auth.password_req_length') }}</li>
                    <li id="req-lowercase" class="requirement">{{ __('auth.password_req_lowercase') }}</li>
                    <li id="req-uppercase" class="requirement">{{ __('auth.password_req_uppercase') }}</li>
                    <li id="req-number" class="requirement">{{ __('auth.password_req_number') }}</li>
                    <li id="req-special" class="requirement">{{ __('auth.password_req_special') }}</li>
                </ul>
            </div>

            <!-- Submit Button -->
            <button type="submit" id="reset-btn" class="auth-submit-btn">
                <span class="btn-text">{{ __('Reset Password') }}</span>
                <span class="btn-loading" style="display: none;">
                    <svg class="loading-spinner" viewBox="0 0 24 24">
                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none" opacity="0.25"></circle>
                        <path fill="currentColor" opacity="0.75" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {{ __('auth.resetting_password') }}
                </span>
            </button>

            <!-- Back to Login -->
            <div class="auth-links">
                <a href="{{ route('login') }}" class="auth-link">
                    <svg class="link-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    {{ __('auth.back_to_login') }}
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Success/Error Messages -->
@if(session('success'))
    <div class="alert alert-success">
        {{ session('success') }}
    </div>
@endif

@if(session('error'))
    <div class="alert alert-error">
        {{ session('error') }}
    </div>
@endif
@endsection

@push('scripts')
<script src="{{ asset('js/auth-forms.js') }}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize reset password form with AJAX validation
    const resetForm = new AuthForm('reset-password-form', {
        submitButton: 'reset-btn',
        loadingText: '{{ __("auth.resetting_password") }}',
        successRedirect: '{{ route("login") }}',
        messages: {
            networkError: '{{ __("auth.network_error") }}',
            serverError: '{{ __("auth.server_error") }}',
            validationError: '{{ __("auth.validation_error") }}'
        }
    });

    // Initialize password strength checker
    const passwordStrengthChecker = new PasswordStrength('password', 'password-strength');

    // Password confirmation matching
    const password = document.getElementById('password');
    const passwordConfirmation = document.getElementById('password_confirmation');
    
    function checkPasswordMatch() {
        if (passwordConfirmation.value && password.value !== passwordConfirmation.value) {
            passwordConfirmation.setCustomValidity('{{ __("auth.password_mismatch") }}');
            passwordConfirmation.classList.add('error');
        } else {
            passwordConfirmation.setCustomValidity('');
            passwordConfirmation.classList.remove('error');
        }
    }

    password.addEventListener('input', checkPasswordMatch);
    passwordConfirmation.addEventListener('input', checkPasswordMatch);
});

// Password toggle function
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.nextElementSibling.querySelector('.password-toggle');
    
    if (field.type === 'password') {
        field.type = 'text';
        button.setAttribute('aria-label', '{{ __("auth.hide_password") }}');
    } else {
        field.type = 'password';
        button.setAttribute('aria-label', '{{ __("auth.show_password") }}');
    }
}
</script>
@endpush
