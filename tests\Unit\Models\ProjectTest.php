<?php

namespace Tests\Unit\Models;

use App\Models\Project;
use App\Models\Service;
use App\Models\User;
use App\Models\Role;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ProjectTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function project_has_fillable_attributes()
    {
        $fillable = [
            'uuid',
            'title',
            'slug',
            'description',
            'content',
            'client_name',
            'client_id',
            'service_id',
            'featured_image',
            'gallery',
            'project_url',
            'start_date',
            'end_date',
            'estimated_hours',
            'actual_hours',
            'hourly_rate',
            'total_amount',
            'currency_code',
            'status',
            'priority',
            'is_featured',
            'is_published',
            'is_deleted',
            'meta_title',
            'meta_description',
            'meta_keywords',
        ];

        $project = new Project();
        $this->assertEquals($fillable, $project->getFillable());
    }

    /** @test */
    public function project_has_correct_casts()
    {
        $project = new Project();
        $casts = $project->getCasts();

        $this->assertEquals('date', $casts['start_date']);
        $this->assertEquals('date', $casts['end_date']);
        $this->assertEquals('decimal:2', $casts['estimated_hours']);
        $this->assertEquals('decimal:2', $casts['actual_hours']);
        $this->assertEquals('decimal:2', $casts['hourly_rate']);
        $this->assertEquals('decimal:2', $casts['total_amount']);
        $this->assertEquals('array', $casts['gallery']);
        $this->assertEquals('boolean', $casts['is_featured']);
        $this->assertEquals('boolean', $casts['is_published']);
        $this->assertEquals('boolean', $casts['is_deleted']);
    }

    /** @test */
    public function project_belongs_to_client()
    {
        $clientRole = Role::factory()->create(['name' => 'client']);
        $client = User::factory()->create(['role_id' => $clientRole->id]);
        $project = Project::factory()->create(['client_id' => $client->id]);

        $this->assertInstanceOf(User::class, $project->client);
        $this->assertEquals($client->id, $project->client->id);
    }

    /** @test */
    public function project_belongs_to_service()
    {
        $service = Service::factory()->create();
        $project = Project::factory()->create(['service_id' => $service->id]);

        $this->assertInstanceOf(Service::class, $project->service);
        $this->assertEquals($service->id, $project->service->id);
    }

    /** @test */
    public function project_can_have_null_client()
    {
        $project = Project::factory()->create(['client_id' => null]);

        $this->assertNull($project->client);
    }

    /** @test */
    public function project_can_have_null_service()
    {
        $project = Project::factory()->create(['service_id' => null]);

        $this->assertNull($project->service);
    }

    /** @test */
    public function active_scope_excludes_deleted_projects()
    {
        $activeProject = Project::factory()->create(['is_deleted' => false]);
        $deletedProject = Project::factory()->create(['is_deleted' => true]);

        $activeProjects = Project::active()->get();

        $this->assertTrue($activeProjects->contains($activeProject));
        $this->assertFalse($activeProjects->contains($deletedProject));
    }

    /** @test */
    public function published_scope_includes_only_published_non_deleted_projects()
    {
        $publishedProject = Project::factory()->create([
            'is_published' => true,
            'is_deleted' => false
        ]);
        $unpublishedProject = Project::factory()->create([
            'is_published' => false,
            'is_deleted' => false
        ]);
        $deletedPublishedProject = Project::factory()->create([
            'is_published' => true,
            'is_deleted' => true
        ]);

        $publishedProjects = Project::published()->get();

        $this->assertTrue($publishedProjects->contains($publishedProject));
        $this->assertFalse($publishedProjects->contains($unpublishedProject));
        $this->assertFalse($publishedProjects->contains($deletedPublishedProject));
    }

    /** @test */
    public function featured_scope_includes_only_featured_projects()
    {
        $featuredProject = Project::factory()->create(['is_featured' => true]);
        $regularProject = Project::factory()->create(['is_featured' => false]);

        $featuredProjects = Project::featured()->get();

        $this->assertTrue($featuredProjects->contains($featuredProject));
        $this->assertFalse($featuredProjects->contains($regularProject));
    }

    /** @test */
    public function for_client_scope_filters_by_client_id()
    {
        $clientRole = Role::factory()->create(['name' => 'client']);
        $client1 = User::factory()->create(['role_id' => $clientRole->id]);
        $client2 = User::factory()->create(['role_id' => $clientRole->id]);

        $project1 = Project::factory()->create(['client_id' => $client1->id]);
        $project2 = Project::factory()->create(['client_id' => $client2->id]);

        $client1Projects = Project::forClient($client1->id)->get();

        $this->assertTrue($client1Projects->contains($project1));
        $this->assertFalse($client1Projects->contains($project2));
    }

    /** @test */
    public function in_progress_scope_includes_active_and_in_progress_projects()
    {
        $activeProject = Project::factory()->create(['status' => 'active']);
        $inProgressProject = Project::factory()->create(['status' => 'in_progress']);
        $completedProject = Project::factory()->create(['status' => 'completed']);
        $planningProject = Project::factory()->create(['status' => 'planning']);

        $inProgressProjects = Project::inProgress()->get();

        $this->assertTrue($inProgressProjects->contains($activeProject));
        $this->assertTrue($inProgressProjects->contains($inProgressProject));
        $this->assertFalse($inProgressProjects->contains($completedProject));
        $this->assertFalse($inProgressProjects->contains($planningProject));
    }

    /** @test */
    public function project_can_be_created_with_all_attributes()
    {
        $clientRole = Role::factory()->create(['name' => 'client']);
        $client = User::factory()->create(['role_id' => $clientRole->id]);
        $service = Service::factory()->create();

        $projectData = [
            'title' => 'Test Project',
            'slug' => 'test-project',
            'description' => 'Test project description',
            'content' => 'Detailed test project content',
            'client_name' => 'Test Client',
            'client_id' => $client->id,
            'service_id' => $service->id,
            'project_url' => 'https://example.com',
            'start_date' => now()->subDays(30),
            'end_date' => now(),
            'estimated_hours' => 100.00,
            'actual_hours' => 95.50,
            'hourly_rate' => 75.00,
            'total_amount' => 7162.50,
            'currency_code' => 'ZAR',
            'status' => 'completed',
            'priority' => 'high',
            'is_featured' => true,
            'is_published' => true,
            'is_deleted' => false,
            'meta_title' => 'Test Project - SEO Title',
            'meta_description' => 'Test project SEO description',
            'meta_keywords' => 'test, project, seo',
        ];

        $project = Project::create($projectData);

        $this->assertDatabaseHas('projects', $projectData);
        $this->assertEquals('Test Project', $project->title);
        $this->assertEquals('test-project', $project->slug);
        $this->assertEquals($client->id, $project->client_id);
        $this->assertEquals($service->id, $project->service_id);
    }

    /** @test */
    public function project_status_can_be_updated()
    {
        $project = Project::factory()->create(['status' => 'planning']);

        $project->update(['status' => 'in_progress']);

        $this->assertEquals('in_progress', $project->fresh()->status);
    }

    /** @test */
    public function project_can_be_soft_deleted()
    {
        $project = Project::factory()->create(['is_deleted' => false]);

        $project->update(['is_deleted' => true]);

        $this->assertTrue($project->fresh()->is_deleted);
        $this->assertDatabaseHas('projects', [
            'id' => $project->id,
            'is_deleted' => true
        ]);
    }

    /** @test */
    public function project_can_be_published_and_unpublished()
    {
        $project = Project::factory()->create(['is_published' => false]);

        $project->update(['is_published' => true]);
        $this->assertTrue($project->fresh()->is_published);

        $project->update(['is_published' => false]);
        $this->assertFalse($project->fresh()->is_published);
    }

    /** @test */
    public function project_can_be_featured_and_unfeatured()
    {
        $project = Project::factory()->create(['is_featured' => false]);

        $project->update(['is_featured' => true]);
        $this->assertTrue($project->fresh()->is_featured);

        $project->update(['is_featured' => false]);
        $this->assertFalse($project->fresh()->is_featured);
    }

    /** @test */
    public function project_gallery_is_cast_to_array()
    {
        $galleryData = ['image1.jpg', 'image2.jpg', 'image3.jpg'];
        $project = Project::factory()->create(['gallery' => $galleryData]);

        $this->assertIsArray($project->gallery);
        $this->assertEquals($galleryData, $project->gallery);
    }

    /** @test */
    public function project_dates_are_cast_to_carbon_instances()
    {
        $startDate = now()->subDays(30);
        $endDate = now();

        $project = Project::factory()->create([
            'start_date' => $startDate,
            'end_date' => $endDate
        ]);

        $this->assertInstanceOf(\Carbon\Carbon::class, $project->start_date);
        $this->assertInstanceOf(\Carbon\Carbon::class, $project->end_date);
        $this->assertEquals($startDate->format('Y-m-d'), $project->start_date->format('Y-m-d'));
        $this->assertEquals($endDate->format('Y-m-d'), $project->end_date->format('Y-m-d'));
    }

    /** @test */
    public function project_numeric_fields_are_cast_to_decimal()
    {
        $project = Project::factory()->create([
            'estimated_hours' => 100.50,
            'actual_hours' => 95.25,
            'hourly_rate' => 75.00,
            'total_amount' => 7143.75
        ]);

        $this->assertEquals('100.50', $project->estimated_hours);
        $this->assertEquals('95.25', $project->actual_hours);
        $this->assertEquals('75.00', $project->hourly_rate);
        $this->assertEquals('7143.75', $project->total_amount);
    }
}
