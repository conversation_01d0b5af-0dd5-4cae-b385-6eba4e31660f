# API Documentation & Specifications
## ChiSolution Digital Agency Platform

### 🌐 API Overview

#### **Base Information**
```yaml
API Version: v1
Base URL: https://api.chisolution.com/v1
Authentication: <PERSON><PERSON> (Laravel Sanctum)
Content-Type: application/json
Rate Limiting: 1000 requests/hour per user
```

#### **Response Format Standards**
```json
{
  "success": true,
  "data": {
    // Response data here
  },
  "meta": {
    "pagination": {
      "current_page": 1,
      "total_pages": 10,
      "per_page": 20,
      "total": 200
    },
    "timestamp": "2025-06-25T10:30:00Z",
    "version": "v1"
  },
  "links": {
    "self": "/api/v1/products",
    "next": "/api/v1/products?page=2",
    "prev": null
  }
}
```

#### **Error Response Format**
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "The given data was invalid.",
    "details": {
      "email": ["The email field is required."],
      "password": ["The password must be at least 8 characters."]
    },
    "timestamp": "2025-06-25T10:30:00Z",
    "request_id": "req_123456789"
  }
}
```

### 🔐 Authentication Endpoints

#### **POST /auth/register**
Register a new user account.

**Request:**
```json
{
  "first_name": "John",
  "last_name": "Doe",
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "password_confirmation": "SecurePass123!",
  "phone": "+***********",
  "role": "customer"
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "uuid": "550e8400-e29b-41d4-a716-************",
      "first_name": "John",
      "last_name": "Doe",
      "email": "<EMAIL>",
      "phone": "+***********",
      "role": "customer",
      "email_verified_at": null,
      "created_at": "2025-06-25T10:30:00Z"
    },
    "token": "1|abc123def456ghi789jkl012mno345pqr678stu901vwx234yz"
  }
}
```

#### **POST /auth/login**
Authenticate user and return access token.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "remember": true
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "uuid": "550e8400-e29b-41d4-a716-************",
      "first_name": "John",
      "last_name": "Doe",
      "email": "<EMAIL>",
      "role": "customer"
    },
    "token": "1|abc123def456ghi789jkl012mno345pqr678stu901vwx234yz",
    "expires_at": "2025-07-25T10:30:00Z"
  }
}
```

#### **POST /auth/logout**
Revoke current access token.

**Headers:**
```
Authorization: Bearer {token}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "message": "Successfully logged out"
  }
}
```

### 🛍️ Product Management Endpoints

#### **GET /products**
Retrieve paginated list of products with filtering and search.

**Query Parameters:**
```
?page=1
&per_page=20
&search=laptop
&category_id=5
&price_min=500
&price_max=2000
&sort=price
&order=asc
&include=category,images,variants
```

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "uuid": "550e8400-e29b-41d4-a716-************",
      "name": "Gaming Laptop Pro",
      "slug": "gaming-laptop-pro",
      "short_description": "High-performance gaming laptop",
      "price": 1299.99,
      "compare_price": 1499.99,
      "currency": "ZAR",
      "sku": "GLP-001",
      "inventory_quantity": 15,
      "is_featured": true,
      "featured_image": {
        "url": "/storage/products/laptop-main.jpg",
        "alt": "Gaming Laptop Pro"
      },
      "category": {
        "id": 5,
        "name": "Laptops",
        "slug": "laptops"
      },
      "images": [
        {
          "id": 1,
          "url": "/storage/products/laptop-1.jpg",
          "alt": "Gaming Laptop Pro - Front View"
        }
      ],
      "created_at": "2025-06-25T10:30:00Z",
      "updated_at": "2025-06-25T10:30:00Z"
    }
  ],
  "meta": {
    "pagination": {
      "current_page": 1,
      "total_pages": 5,
      "per_page": 20,
      "total": 95
    }
  }
}
```

#### **GET /products/{id}**
Retrieve single product details.

**Response (200):**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "uuid": "550e8400-e29b-41d4-a716-************",
    "name": "Gaming Laptop Pro",
    "slug": "gaming-laptop-pro",
    "description": "Full product description with specifications...",
    "short_description": "High-performance gaming laptop",
    "price": 1299.99,
    "compare_price": 1499.99,
    "cost_price": 800.00,
    "currency": "ZAR",
    "sku": "GLP-001",
    "barcode": "1234567890123",
    "weight": 2.5,
    "dimensions": {
      "length": 35.5,
      "width": 24.2,
      "height": 2.3,
      "unit": "cm"
    },
    "inventory_quantity": 15,
    "low_stock_threshold": 5,
    "track_inventory": true,
    "is_active": true,
    "is_featured": true,
    "meta": {
      "title": "Gaming Laptop Pro - High Performance",
      "description": "SEO meta description...",
      "keywords": "gaming, laptop, high-performance"
    },
    "category": {
      "id": 5,
      "name": "Laptops",
      "slug": "laptops",
      "parent": {
        "id": 1,
        "name": "Computers",
        "slug": "computers"
      }
    },
    "variants": [
      {
        "id": 1,
        "name": "16GB RAM / 512GB SSD",
        "sku": "GLP-001-16-512",
        "price": 1299.99,
        "inventory_quantity": 10,
        "attributes": {
          "ram": "16GB",
          "storage": "512GB SSD"
        }
      }
    ],
    "images": [
      {
        "id": 1,
        "url": "/storage/products/laptop-1.jpg",
        "alt": "Gaming Laptop Pro - Front View",
        "sort_order": 1
      }
    ],
    "related_products": [
      {
        "id": 2,
        "name": "Gaming Mouse",
        "price": 79.99,
        "featured_image": "/storage/products/mouse.jpg"
      }
    ],
    "created_at": "2025-06-25T10:30:00Z",
    "updated_at": "2025-06-25T10:30:00Z"
  }
}
```

#### **POST /products** (Admin/Staff Only)
Create a new product.

**Headers:**
```
Authorization: Bearer {admin_token}
Content-Type: application/json
```

**Request:**
```json
{
  "name": "New Gaming Laptop",
  "description": "Product description...",
  "short_description": "Brief description",
  "price": 1599.99,
  "compare_price": 1799.99,
  "cost_price": 1000.00,
  "sku": "NGL-001",
  "barcode": "9876543210987",
  "category_id": 5,
  "inventory_quantity": 20,
  "low_stock_threshold": 5,
  "track_inventory": true,
  "weight": 2.8,
  "dimensions": {
    "length": 36.0,
    "width": 25.0,
    "height": 2.5
  },
  "is_active": true,
  "is_featured": false,
  "meta": {
    "title": "Custom SEO title",
    "description": "Custom SEO description",
    "keywords": "gaming, laptop, new"
  },
  "images": [
    {
      "url": "/storage/temp/laptop-image-1.jpg",
      "alt": "New Gaming Laptop - Main View",
      "sort_order": 1
    }
  ],
  "variants": [
    {
      "name": "32GB RAM / 1TB SSD",
      "sku": "NGL-001-32-1TB",
      "price": 1899.99,
      "inventory_quantity": 5,
      "attributes": {
        "ram": "32GB",
        "storage": "1TB SSD"
      }
    }
  ]
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "id": 25,
    "uuid": "550e8400-e29b-41d4-a716-************",
    "name": "New Gaming Laptop",
    "slug": "new-gaming-laptop",
    // ... full product data
  }
}
```

### 🛒 Shopping Cart Endpoints

#### **GET /cart**
Retrieve current user's cart contents.

**Headers:**
```
Authorization: Bearer {token}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "user_id": 1,
    "items": [
      {
        "id": 1,
        "product_id": 1,
        "variant_id": null,
        "quantity": 2,
        "price": 1299.99,
        "total": 2599.98,
        "product": {
          "id": 1,
          "name": "Gaming Laptop Pro",
          "slug": "gaming-laptop-pro",
          "featured_image": "/storage/products/laptop-main.jpg"
        }
      }
    ],
    "totals": {
      "subtotal": 2599.98,
      "tax": 377.00,
      "shipping": 150.00,
      "discount": 0.00,
      "total": 3126.98,
      "currency": "ZAR"
    },
    "item_count": 2,
    "updated_at": "2025-06-25T10:30:00Z"
  }
}
```

#### **POST /cart/items**
Add item to cart.

**Request:**
```json
{
  "product_id": 1,
  "variant_id": null,
  "quantity": 1
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "item": {
      "id": 2,
      "product_id": 1,
      "quantity": 1,
      "price": 1299.99,
      "total": 1299.99
    },
    "cart_totals": {
      "subtotal": 3899.97,
      "total": 4526.97,
      "item_count": 3
    }
  }
}
```

#### **PUT /cart/items/{id}**
Update cart item quantity.

**Request:**
```json
{
  "quantity": 3
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "item": {
      "id": 1,
      "quantity": 3,
      "total": 3899.97
    },
    "cart_totals": {
      "subtotal": 3899.97,
      "total": 4526.97,
      "item_count": 3
    }
  }
}
```

#### **DELETE /cart/items/{id}**
Remove item from cart.

**Response (200):**
```json
{
  "success": true,
  "data": {
    "message": "Item removed from cart",
    "cart_totals": {
      "subtotal": 2599.98,
      "total": 3126.98,
      "item_count": 2
    }
  }
}
```

### 📦 Order Management Endpoints

#### **POST /orders**
Create a new order from cart contents.

**Request:**
```json
{
  "billing_address": {
    "first_name": "John",
    "last_name": "Doe",
    "company": "Tech Corp",
    "address_line_1": "123 Main Street",
    "address_line_2": "Suite 100",
    "city": "Cape Town",
    "state": "Western Cape",
    "postal_code": "8001",
    "country": "ZA",
    "phone": "+***********"
  },
  "shipping_address": {
    // Same structure as billing_address
    // If not provided, billing_address is used
  },
  "shipping_method_id": 1,
  "payment_method": "stripe",
  "payment_token": "tok_1234567890",
  "notes": "Please deliver after 2 PM"
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "uuid": "550e8400-e29b-41d4-a716-************",
    "order_number": "ORD-2025-001",
    "status": "pending",
    "payment_status": "pending",
    "currency": "ZAR",
    "subtotal": 2599.98,
    "tax_amount": 377.00,
    "shipping_amount": 150.00,
    "discount_amount": 0.00,
    "total_amount": 3126.98,
    "billing_address": {
      "first_name": "John",
      "last_name": "Doe",
      // ... full address
    },
    "shipping_address": {
      // ... shipping address
    },
    "items": [
      {
        "id": 1,
        "product_id": 1,
        "quantity": 2,
        "price": 1299.99,
        "total": 2599.98,
        "product_snapshot": {
          "name": "Gaming Laptop Pro",
          "sku": "GLP-001"
        }
      }
    ],
    "shipping_method": {
      "id": 1,
      "name": "Standard Delivery",
      "estimated_days": "3-5 business days"
    },
    "created_at": "2025-06-25T10:30:00Z",
    "estimated_delivery": "2025-06-30T10:30:00Z"
  }
}
```

#### **GET /orders**
Retrieve user's order history.

**Query Parameters:**
```
?page=1
&per_page=10
&status=completed
&date_from=2025-01-01
&date_to=2025-12-31
```

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "uuid": "550e8400-e29b-41d4-a716-************",
      "order_number": "ORD-2025-001",
      "status": "completed",
      "payment_status": "paid",
      "total_amount": 3126.98,
      "currency": "ZAR",
      "item_count": 2,
      "created_at": "2025-06-25T10:30:00Z",
      "delivered_at": "2025-06-28T14:30:00Z"
    }
  ],
  "meta": {
    "pagination": {
      "current_page": 1,
      "total_pages": 3,
      "per_page": 10,
      "total": 25
    }
  }
}
```

#### **GET /orders/{id}**
Retrieve specific order details.

**Response (200):**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "uuid": "550e8400-e29b-41d4-a716-************",
    "order_number": "ORD-2025-001",
    "status": "shipped",
    "payment_status": "paid",
    "currency": "ZAR",
    "subtotal": 2599.98,
    "tax_amount": 377.00,
    "shipping_amount": 150.00,
    "total_amount": 3126.98,
    "items": [
      {
        "id": 1,
        "product_id": 1,
        "quantity": 2,
        "price": 1299.99,
        "total": 2599.98,
        "product": {
          "id": 1,
          "name": "Gaming Laptop Pro",
          "slug": "gaming-laptop-pro",
          "featured_image": "/storage/products/laptop-main.jpg"
        }
      }
    ],
    "tracking": {
      "tracking_number": "TRK123456789",
      "carrier": "PostNet",
      "status": "in_transit",
      "estimated_delivery": "2025-06-28T17:00:00Z",
      "tracking_url": "https://tracking.postnet.co.za/TRK123456789"
    },
    "timeline": [
      {
        "status": "pending",
        "timestamp": "2025-06-25T10:30:00Z",
        "note": "Order placed"
      },
      {
        "status": "processing",
        "timestamp": "2025-06-25T14:30:00Z",
        "note": "Payment confirmed, preparing for shipment"
      },
      {
        "status": "shipped",
        "timestamp": "2025-06-26T09:00:00Z",
        "note": "Package shipped with tracking number TRK123456789"
      }
    ],
    "created_at": "2025-06-25T10:30:00Z"
  }
}
```
