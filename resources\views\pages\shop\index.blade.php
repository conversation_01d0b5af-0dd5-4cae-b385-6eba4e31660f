@extends('layouts.app')

@section('title', 'Shop - ' . __('common.company_name'))
@section('meta_description', 'Browse our collection of digital products and services. Find the perfect solution for your business needs.')
@section('meta_keywords', 'shop, products, digital services, e-commerce, online store')

@section('og_title', 'Shop - ' . __('common.company_name'))
@section('og_description', 'Browse our collection of digital products and services. Find the perfect solution for your business needs.')
@section('twitter_title', 'Shop - ' . __('common.company_name'))
@section('twitter_description', 'Browse our collection of digital products and services. Find the perfect solution for your business needs.')

@section('content')
<!-- Hero Section -->
<section class="relative bg-gradient-blue text-white overflow-hidden">
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></g></svg>');"></div>
    </div>
    
    <div class="container mx-auto px-4 py-20 lg:py-32 relative z-10">
        <div class="text-center max-w-4xl mx-auto">
            <h1 class="heading-1 text-white mb-6">
                Our <span class="text-blue-300">Shop</span>
            </h1>
            <p class="text-lead text-blue-100 mb-8">
                Discover our range of digital products and services designed to help your business grow and succeed in the digital world.
            </p>
            
            <!-- Search Bar -->
            <div class="max-w-2xl mx-auto">
                <form action="{{ route('shop.index') }}" method="GET" class="relative">
                    <input type="text" name="search" value="{{ request('search') }}" 
                           placeholder="Search products..." 
                           class="w-full px-6 py-4 pr-16 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-300">
                    <button type="submit" class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-blue-600 text-white p-2 rounded-lg hover:bg-blue-700 transition-colors">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="absolute bottom-0 left-0 w-full">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" class="relative block w-full h-16 fill-white">
            <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"></path>
        </svg>
    </div>
</section>

<!-- Shop Content -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- Sidebar Filters -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow-sm p-6 sticky top-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">Filters</h3>
                    
                    <form action="{{ route('shop.index') }}" method="GET" id="filter-form">
                        @if(request('search'))
                            <input type="hidden" name="search" value="{{ request('search') }}">
                        @endif
                        
                        <!-- Categories -->
                        @if($categories->count() > 0)
                        <div class="mb-8">
                            <h4 class="font-medium text-gray-900 mb-4">Categories</h4>
                            <div class="space-y-2">
                                @foreach($categories as $category)
                                <div>
                                    <label class="flex items-center">
                                        <input type="radio" name="category" value="{{ $category->slug }}" 
                                               {{ request('category') === $category->slug ? 'checked' : '' }}
                                               class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500">
                                        <span class="ml-2 text-sm text-gray-700">{{ $category->name }}</span>
                                        <span class="ml-auto text-xs text-gray-500">({{ $category->products_count }})</span>
                                    </label>
                                    
                                    @if($category->children->count() > 0)
                                    <div class="ml-6 mt-2 space-y-2">
                                        @foreach($category->children as $child)
                                        <label class="flex items-center">
                                            <input type="radio" name="category" value="{{ $child->slug }}" 
                                                   {{ request('category') === $child->slug ? 'checked' : '' }}
                                                   class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500">
                                            <span class="ml-2 text-sm text-gray-600">{{ $child->name }}</span>
                                            <span class="ml-auto text-xs text-gray-500">({{ $child->products_count }})</span>
                                        </label>
                                        @endforeach
                                    </div>
                                    @endif
                                </div>
                                @endforeach
                                
                                @if(request('category'))
                                <div class="pt-2">
                                    <button type="button" onclick="clearCategory()" class="text-sm text-blue-600 hover:text-blue-700">
                                        Clear Category Filter
                                    </button>
                                </div>
                                @endif
                            </div>
                        </div>
                        @endif
                        
                        <!-- Price Range -->
                        @if($priceRange && $priceRange->min_price !== $priceRange->max_price)
                        <div class="mb-8">
                            <h4 class="font-medium text-gray-900 mb-4">Price Range</h4>
                            <div class="space-y-4">
                                <div class="grid grid-cols-2 gap-2">
                                    <div>
                                        <label class="block text-xs text-gray-600 mb-1">Min Price</label>
                                        <input type="number" name="min_price" value="{{ request('min_price') }}" 
                                               min="{{ $priceRange->min_price }}" max="{{ $priceRange->max_price }}"
                                               placeholder="R{{ number_format($priceRange->min_price, 0) }}"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500">
                                    </div>
                                    <div>
                                        <label class="block text-xs text-gray-600 mb-1">Max Price</label>
                                        <input type="number" name="max_price" value="{{ request('max_price') }}" 
                                               min="{{ $priceRange->min_price }}" max="{{ $priceRange->max_price }}"
                                               placeholder="R{{ number_format($priceRange->max_price, 0) }}"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500">
                                    </div>
                                </div>
                                <div class="text-xs text-gray-500">
                                    Range: R{{ number_format($priceRange->min_price, 0) }} - R{{ number_format($priceRange->max_price, 0) }}
                                </div>
                            </div>
                        </div>
                        @endif
                        
                        <!-- Filter Actions -->
                        <div class="space-y-3">
                            <button type="submit" class="w-full btn-primary">
                                Apply Filters
                            </button>
                            <a href="{{ route('shop.index') }}" class="w-full btn-outline text-center block">
                                Clear All Filters
                            </a>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Products Grid -->
            <div class="lg:col-span-3">
                <!-- Sort and Results Info -->
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
                    <div class="text-gray-600 mb-4 sm:mb-0">
                        Showing {{ $products->firstItem() ?? 0 }}-{{ $products->lastItem() ?? 0 }} of {{ $products->total() }} products
                        @if(request('search'))
                            for "<strong>{{ request('search') }}</strong>"
                        @endif
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <label class="text-sm text-gray-600">Sort by:</label>
                        <select name="sort" onchange="updateSort(this)" class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500">
                            <option value="name" {{ request('sort') === 'name' ? 'selected' : '' }}>Name</option>
                            <option value="price" {{ request('sort') === 'price' ? 'selected' : '' }}>Price</option>
                            <option value="created_at" {{ request('sort') === 'created_at' ? 'selected' : '' }}>Newest</option>
                            <option value="featured" {{ request('sort') === 'featured' ? 'selected' : '' }}>Featured</option>
                        </select>
                    </div>
                </div>
                
                @if($products->count() > 0)
                <!-- Products Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
                    @foreach($products as $product)
                    <div class="product-card bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                        <div class="relative">
                            <a href="{{ route('shop.product', $product->slug) }}">
                                <img src="{{ $product->primary_image }}" alt="{{ $product->name }}" 
                                     class="w-full h-48 object-cover hover-lift">
                            </a>
                            
                            @if($product->discount_percentage > 0)
                            <div class="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-medium">
                                -{{ $product->discount_percentage }}%
                            </div>
                            @endif
                            
                            @if($product->is_featured)
                            <div class="absolute top-2 right-2 bg-blue-600 text-white px-2 py-1 rounded text-xs font-medium">
                                Featured
                            </div>
                            @endif
                        </div>
                        
                        <div class="p-4">
                            <div class="mb-2">
                                @foreach($product->categories->take(2) as $category)
                                <span class="inline-block px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded mr-1">
                                    {{ $category->name }}
                                </span>
                                @endforeach
                            </div>
                            
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                <a href="{{ route('shop.product', $product->slug) }}" class="hover:text-blue-600 transition-colors">
                                    {{ $product->name }}
                                </a>
                            </h3>
                            
                            @if($product->short_description)
                            <div class="text-gray-600 text-sm mb-3 line-clamp-2 prose prose-sm max-w-none">
                                {!! $product->short_description !!}
                            </div>
                            @endif
                            
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <span class="text-lg font-bold text-gray-900">{{ $product->formatted_price }}</span>
                                    @if($product->compare_price)
                                    <span class="text-sm text-gray-500 line-through">{{ $product->formatted_compare_price }}</span>
                                    @endif
                                </div>
                                
                                <div class="text-xs text-gray-500">
                                    {{ $product->stock_status }}
                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <a href="{{ route('shop.product', $product->slug) }}" class="w-full btn-primary text-center block">
                                    View Details
                                </a>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
                
                <!-- Pagination -->
                <div class="flex justify-center">
                    {{ $products->links() }}
                </div>
                @else
                <!-- No Products Found -->
                <div class="text-center py-12">
                    <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-12 h-12 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No products found</h3>
                    <p class="text-gray-600 mb-4">
                        @if(request('search'))
                            No products match your search for "{{ request('search') }}".
                        @else
                            No products match your current filters.
                        @endif
                    </p>
                    <a href="{{ route('shop.index') }}" class="btn-primary">
                        Browse All Products
                    </a>
                </div>
                @endif
            </div>
        </div>
    </div>
</section>

@push('scripts')
<script>
function updateSort(select) {
    const url = new URL(window.location);
    url.searchParams.set('sort', select.value);
    window.location.href = url.toString();
}

function clearCategory() {
    const url = new URL(window.location);
    url.searchParams.delete('category');
    window.location.href = url.toString();
}

// Auto-submit form when filters change
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('filter-form');
    const inputs = form.querySelectorAll('input[type="radio"], input[type="number"]');
    
    inputs.forEach(input => {
        if (input.type === 'radio') {
            input.addEventListener('change', function() {
                form.submit();
            });
        }
    });
});
</script>
@endpush

@push('structured_data')
<script type="application/ld+json">
{
    "@context": "https://schema.org/",
    "@type": "CollectionPage",
    "name": "Shop - {{ __('common.company_name') }}",
    "description": "Browse our collection of digital products and services. Find the perfect solution for your business needs.",
    "url": "{{ route('shop.index') }}",
    "mainEntity": {
        "@type": "ItemList",
        "numberOfItems": "{{ $products->total() }}",
        "itemListElement": [
            @foreach($products->take(10) as $index => $product)
            {
                "@type": "Product",
                "position": {{ $index + 1 }},
                "name": "{{ $product->name }}",
                "url": "{{ route('shop.product', $product->slug) }}",
                "image": "{{ $product->featured_image ? asset('storage/' . $product->featured_image) : $product->primary_image }}",
                @if($product->short_description)
                "description": "{{ $product->short_description }}",
                @endif
                "offers": {
                    "@type": "Offer",
                    "price": "{{ $product->price }}",
                    "priceCurrency": "ZAR",
                    "availability": "{{ $product->isInStock() ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock' }}"
                }
            }@if(!$loop->last),@endif
            @endforeach
        ]
    },
    "breadcrumb": {
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                "position": 1,
                "name": "Home",
                "item": "{{ route('home') }}"
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": "Shop",
                "item": "{{ route('shop.index') }}"
            }
        ]
    }
}
</script>
@endpush

@endsection
