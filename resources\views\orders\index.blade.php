@extends('layouts.dashboard')

@section('title', 'My Orders - ' . __('common.company_name'))
@section('page_title', 'My Orders')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">My Orders</h1>
                <p class="text-gray-600">Track and manage your order history</p>
            </div>
            <a href="{{ route('shop.index') }}" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v5a2 2 0 01-2 2H9a2 2 0 01-2-2v-5m6 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"></path>
                </svg>
                Continue Shopping
            </a>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="mb-6 bg-white rounded-lg shadow p-4">
        <form method="GET" action="{{ route('orders.index') }}" class="flex flex-col md:flex-row gap-4">
            <!-- Search -->
            <div class="flex-1">
                <label for="search" class="sr-only">Search orders</label>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <input type="text" name="search" id="search" value="{{ request('search') }}"
                           class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="Search by order number, email, or notes...">
                </div>
            </div>

            <!-- Status Filter -->
            <div class="md:w-48">
                <label for="status" class="sr-only">Filter by status</label>
                <select name="status" id="status" class="block w-full px-3 py-2 border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    <option value="all" {{ request('status', 'all') === 'all' ? 'selected' : '' }}>All Orders</option>
                    <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>Pending</option>
                    <option value="confirmed" {{ request('status') === 'confirmed' ? 'selected' : '' }}>Confirmed</option>
                    <option value="processing" {{ request('status') === 'processing' ? 'selected' : '' }}>Processing</option>
                    <option value="shipped" {{ request('status') === 'shipped' ? 'selected' : '' }}>Shipped</option>
                    <option value="delivered" {{ request('status') === 'delivered' ? 'selected' : '' }}>Delivered</option>
                    <option value="cancelled" {{ request('status') === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                </select>
            </div>

            <!-- Submit Button -->
            <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors">
                Filter
            </button>

            <!-- Clear Filters -->
            @if(request()->hasAny(['search', 'status']) && (request('search') || request('status') !== 'all'))
                <a href="{{ route('orders.index') }}" class="bg-gray-100 text-gray-700 px-6 py-2 rounded-md hover:bg-gray-200 transition-colors">
                    Clear
                </a>
            @endif
        </form>
    </div>

    <!-- Status Tabs -->
    <div class="mb-6">
        <nav class="flex space-x-8 overflow-x-auto">
            <a href="{{ route('orders.index', ['status' => 'all']) }}"
               class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm {{ request('status', 'all') === 'all' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                All Orders
                <span class="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs">{{ $statusCounts['all'] }}</span>
            </a>
            <a href="{{ route('orders.index', ['status' => 'pending']) }}"
               class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm {{ request('status') === 'pending' ? 'border-yellow-500 text-yellow-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                Pending
                <span class="ml-2 bg-yellow-100 text-yellow-900 py-0.5 px-2.5 rounded-full text-xs">{{ $statusCounts['pending'] }}</span>
            </a>
            <a href="{{ route('orders.index', ['status' => 'confirmed']) }}"
               class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm {{ request('status') === 'confirmed' ? 'border-green-500 text-green-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                Confirmed
                <span class="ml-2 bg-green-100 text-green-900 py-0.5 px-2.5 rounded-full text-xs">{{ $statusCounts['confirmed'] }}</span>
            </a>
            <a href="{{ route('orders.index', ['status' => 'processing']) }}"
               class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm {{ request('status') === 'processing' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                Processing
                <span class="ml-2 bg-blue-100 text-blue-900 py-0.5 px-2.5 rounded-full text-xs">{{ $statusCounts['processing'] }}</span>
            </a>
            <a href="{{ route('orders.index', ['status' => 'shipped']) }}"
               class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm {{ request('status') === 'shipped' ? 'border-purple-500 text-purple-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                Shipped
                <span class="ml-2 bg-purple-100 text-purple-900 py-0.5 px-2.5 rounded-full text-xs">{{ $statusCounts['shipped'] }}</span>
            </a>
            <a href="{{ route('orders.index', ['status' => 'delivered']) }}"
               class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm {{ request('status') === 'delivered' ? 'border-green-500 text-green-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                Delivered
                <span class="ml-2 bg-green-100 text-green-900 py-0.5 px-2.5 rounded-full text-xs">{{ $statusCounts['delivered'] }}</span>
            </a>
            <a href="{{ route('orders.index', ['status' => 'cancelled']) }}"
               class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm {{ request('status') === 'cancelled' ? 'border-red-500 text-red-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                Cancelled
                <span class="ml-2 bg-red-100 text-red-900 py-0.5 px-2.5 rounded-full text-xs">{{ $statusCounts['cancelled'] }}</span>
            </a>
        </nav>
    </div>

    @if($orders->count() > 0)
        <!-- Orders List -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">
                    Order History
                    @if(request('search') || (request('status') && request('status') !== 'all'))
                        <span class="text-sm font-normal text-gray-500">
                            ({{ $orders->total() }} {{ Str::plural('result', $orders->total()) }})
                        </span>
                    @endif
                </h3>
            </div>
            <div class="divide-y divide-gray-200">
                @foreach($orders as $order)
                    <div class="p-6 hover:bg-gray-50 transition-colors">
                        <div class="flex items-start justify-between">
                            <div class="flex items-start space-x-4">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2">
                                        <h4 class="text-lg font-medium text-gray-900">Order #{{ $order->order_number }}</h4>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            @if($order->status === 'delivered') bg-green-100 text-green-800
                                            @elseif($order->status === 'pending') bg-yellow-100 text-yellow-800
                                            @elseif($order->status === 'confirmed') bg-blue-100 text-blue-800
                                            @elseif($order->status === 'processing') bg-indigo-100 text-indigo-800
                                            @elseif($order->status === 'shipped') bg-purple-100 text-purple-800
                                            @elseif($order->status === 'cancelled') bg-red-100 text-red-800
                                            @elseif($order->status === 'refunded') bg-gray-100 text-gray-800
                                            @else bg-gray-100 text-gray-800
                                            @endif">
                                            {{ ucfirst($order->status) }}
                                        </span>
                                    </div>
                                    <p class="text-sm text-gray-500 mt-1">Placed on {{ $order->created_at->format('M j, Y \a\t g:i A') }}</p>

                                    <!-- Order Items Summary -->
                                    <div class="mt-2">
                                        <p class="text-sm text-gray-600">
                                            {{ $order->total_items }} {{ Str::plural('item', $order->total_items) }}
                                            @if($order->items->count() > 0)
                                                - {{ $order->items->first()->product->name ?? 'Product' }}
                                                @if($order->items->count() > 1)
                                                    and {{ $order->items->count() - 1 }} more
                                                @endif
                                            @endif
                                        </p>
                                    </div>

                                    <!-- Shipping Info -->
                                    @if($order->shipped_at)
                                        <div class="mt-2 flex items-center text-sm text-green-600">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                            </svg>
                                            Shipped on {{ $order->shipped_at->format('M j, Y') }}
                                        </div>
                                    @endif

                                    @if($order->delivered_at)
                                        <div class="mt-2 flex items-center text-sm text-green-600">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            Delivered on {{ $order->delivered_at->format('M j, Y') }}
                                        </div>
                                    @endif
                                </div>
                            </div>

                            <div class="flex items-center space-x-4">
                                <div class="text-right">
                                    <p class="text-lg font-semibold text-gray-900">
                                        {{ $order->currency->symbol ?? 'R' }}{{ number_format($order->total_amount, 2) }}
                                    </p>
                                    <p class="text-sm text-gray-500">
                                        Payment:
                                        <span class="font-medium
                                            @if($order->payment_status === 'completed') text-green-600
                                            @elseif($order->payment_status === 'pending') text-yellow-600
                                            @elseif($order->payment_status === 'failed') text-red-600
                                            @elseif($order->payment_status === 'refunded') text-gray-600
                                            @else text-gray-600
                                            @endif">
                                            {{ ucfirst($order->payment_status) }}
                                        </span>
                                    </p>
                                </div>

                                <div class="flex flex-col space-y-2">
                                    <a href="{{ route('orders.show', $order) }}"
                                       class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-center text-sm">
                                        View Details
                                    </a>

                                    @if(in_array($order->status, ['pending', 'cancelled']))
                                        <form action="{{ route('orders.destroy', $order) }}" method="POST"
                                              onsubmit="return confirm('Are you sure you want to delete this order? This action cannot be undone.')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit"
                                                    class="w-full bg-red-100 text-red-700 px-4 py-2 rounded-lg hover:bg-red-200 transition-colors text-sm">
                                                Delete
                                            </button>
                                        </form>
                                    @endif
                                </div>
                            </div>
                        </div>

                        @if($order->notes)
                            <div class="mt-4 p-3 bg-gray-50 rounded-lg">
                                <p class="text-sm text-gray-600"><strong>Notes:</strong> {{ $order->notes }}</p>
                            </div>
                        @endif
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Pagination -->
        @if($orders->hasPages())
            <div class="mt-6">
                {{ $orders->links() }}
            </div>
        @endif
    @else
        <!-- Empty State -->
        <div class="bg-white rounded-lg shadow p-12 text-center">
            <svg class="mx-auto h-16 w-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
            </svg>
            <h3 class="mt-4 text-lg font-medium text-gray-900">No orders yet</h3>
            <p class="mt-2 text-gray-500">You haven't placed any orders yet. Start shopping to see your orders here.</p>
            <div class="mt-6">
                <a href="{{ route('shop.index') }}" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v5a2 2 0 01-2 2H9a2 2 0 01-2-2v-5m6 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"></path>
                    </svg>
                    Start Shopping
                </a>
            </div>
        </div>
    @endif
</div>
@endsection
