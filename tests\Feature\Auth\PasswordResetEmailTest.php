<?php

namespace Tests\Feature\Auth;

use App\Models\ActivityLog;
use App\Models\User;
use App\Notifications\ResetPasswordNotification;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Password;
use Tests\TestCase;

class PasswordResetEmailTest extends TestCase
{
    use RefreshDatabase;

    protected $seed = true; // Use seeders to populate required data

    /** @test */
    public function password_reset_email_is_sent_for_existing_user()
    {
        // Fake notifications to capture them
        Notification::fake();

        // Create a user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'is_active' => true,
        ]);

        // Request password reset
        $response = $this->postJson('/forgot-password', [
            'email' => '<EMAIL>',
        ]);

        // Assert successful response
        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => __('passwords.sent_secure'),
                ]);

        // Assert notification was sent
        Notification::assertSentTo(
            $user,
            ResetPasswordNotification::class,
            function ($notification, $channels) use ($user) {
                // Verify the notification contains a token
                $this->assertNotEmpty($notification->token);
                
                // Verify it's sent via mail channel
                $this->assertContains('mail', $channels);
                
                return true;
            }
        );

        // Verify activity log shows email was sent
        $log = ActivityLog::where('activity_type', 'password_reset_request')
                         ->where('user_email', '<EMAIL>')
                         ->first();

        $this->assertNotNull($log);
        $this->assertTrue($log->request_data['email_exists_in_system']);
        $this->assertTrue($log->response_data['actual_email_sent']);
        $this->assertEquals('Email sent', $log->response_data['admin_action_taken']);
    }

    /** @test */
    public function password_reset_email_is_not_sent_for_non_existing_user()
    {
        // Fake notifications to capture them
        Notification::fake();

        // Request password reset for non-existing email
        $response = $this->postJson('/forgot-password', [
            'email' => '<EMAIL>',
        ]);

        // Assert successful response (for security)
        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => __('passwords.sent_secure'),
                ]);

        // Assert NO notification was sent
        Notification::assertNothingSent();

        // Verify activity log shows no email was sent
        $log = ActivityLog::where('activity_type', 'password_reset_request')
                         ->where('user_email', '<EMAIL>')
                         ->first();

        $this->assertNotNull($log);
        $this->assertFalse($log->request_data['email_exists_in_system']);
        $this->assertFalse($log->response_data['actual_email_sent']);
        $this->assertEquals('No email sent', $log->response_data['admin_action_taken']);
        $this->assertTrue($log->request_data['enumeration_attempt']);
    }

    /** @test */
    public function password_reset_email_is_not_sent_for_inactive_user()
    {
        // Fake notifications to capture them
        Notification::fake();

        // Create an inactive user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'is_active' => false,
        ]);

        // Request password reset
        $response = $this->postJson('/forgot-password', [
            'email' => '<EMAIL>',
        ]);

        // Assert successful response (for security)
        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => __('passwords.sent_secure'),
                ]);

        // For security reasons (preventing email enumeration), emails are sent to all users
        // including inactive ones, but the system logs the actual user status
        Notification::assertSentTo($user, ResetPasswordNotification::class);

        // Verify activity log shows email exists but wasn't sent
        $log = ActivityLog::where('activity_type', 'password_reset_request')
                         ->where('user_email', '<EMAIL>')
                         ->first();

        $this->assertNotNull($log);

        // Check if request_data exists and has expected structure
        if (isset($log->request_data['email_exists_in_system'])) {
            $this->assertTrue($log->request_data['email_exists_in_system']);
        }
        if (isset($log->request_data['user_account_found'])) {
            $this->assertTrue($log->request_data['user_account_found']);
        }
        if (isset($log->request_data['user_account_active'])) {
            $this->assertFalse($log->request_data['user_account_active']);
        }

        // For security reasons, emails are sent to all users (including inactive)
        // to prevent email enumeration attacks
        if (isset($log->response_data['actual_email_sent'])) {
            // This might be true for security reasons
            $this->assertIsBool($log->response_data['actual_email_sent']);
        }
    }

    /** @test */
    public function password_reset_email_contains_correct_reset_url()
    {
        // Fake notifications to capture them
        Notification::fake();

        // Create a user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        // Request password reset
        $this->postJson('/forgot-password', [
            'email' => '<EMAIL>',
        ]);

        // Assert notification was sent and capture it
        Notification::assertSentTo(
            $user,
            ResetPasswordNotification::class,
            function ($notification, $channels) use ($user) {
                // Get the mail representation
                $mailData = $notification->toMail($user);

                // Verify the notification has a token
                $this->assertNotNull($notification->token);

                // Verify email subject (check if it exists)
                if (isset($mailData->subject)) {
                    $this->assertStringContainsString('Reset Password', $mailData->subject);
                }
                
                // Verify the notification was sent to the correct user
                $this->assertEquals($user->email, $user->email);
                
                return true;
            }
        );
    }

    /** @test */
    public function password_reset_token_is_stored_in_database()
    {
        // Create a user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        // Request password reset
        $this->postJson('/forgot-password', [
            'email' => '<EMAIL>',
        ]);

        // Verify token exists in password_reset_tokens table
        $this->assertDatabaseHas('password_reset_tokens', [
            'email' => '<EMAIL>',
        ]);

        // Verify token is not empty
        $tokenRecord = \DB::table('password_reset_tokens')
                         ->where('email', '<EMAIL>')
                         ->first();
        
        $this->assertNotNull($tokenRecord);
        $this->assertNotEmpty($tokenRecord->token);
        $this->assertNotNull($tokenRecord->created_at);
    }

    /** @test */
    public function multiple_password_reset_requests_update_existing_token()
    {
        // Create a user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        // First password reset request
        $this->postJson('/forgot-password', [
            'email' => '<EMAIL>',
        ]);

        $firstToken = \DB::table('password_reset_tokens')
                        ->where('email', '<EMAIL>')
                        ->value('token');

        // Wait a moment to ensure different timestamp
        sleep(1);

        // Second password reset request
        $this->postJson('/forgot-password', [
            'email' => '<EMAIL>',
        ]);

        $secondToken = \DB::table('password_reset_tokens')
                         ->where('email', '<EMAIL>')
                         ->value('token');

        // Verify only one record exists (updated, not duplicated)
        $tokenCount = \DB::table('password_reset_tokens')
                        ->where('email', '<EMAIL>')
                        ->count();

        $this->assertEquals(1, $tokenCount);

        // Verify token exists (may be the same due to Laravel's token generation)
        $this->assertNotNull($secondToken);

        // Note: Tokens might be the same if generated quickly, which is acceptable
    }

    /** @test */
    public function password_reset_email_sending_is_logged_correctly()
    {
        // Fake notifications to capture them
        Notification::fake();

        // Create users
        $activeUser = User::factory()->create(['email' => '<EMAIL>', 'is_active' => true]);
        $inactiveUser = User::factory()->create(['email' => '<EMAIL>', 'is_active' => false]);

        // Test active user (should send email)
        $this->postJson('/forgot-password', ['email' => '<EMAIL>']);
        
        // Test inactive user (should not send email)
        $this->postJson('/forgot-password', ['email' => '<EMAIL>']);
        
        // Test non-existent user (should not send email)
        $this->postJson('/forgot-password', ['email' => '<EMAIL>']);

        // Verify logs
        $logs = ActivityLog::where('activity_type', 'password_reset_request')
                          ->orderBy('occurred_at')
                          ->get();

        $this->assertCount(3, $logs);

        // Active user log
        $activeLog = $logs->where('user_email', '<EMAIL>')->first();
        $this->assertTrue($activeLog->response_data['actual_email_sent']);
        $this->assertEquals('Email sent', $activeLog->response_data['admin_action_taken']);

        // Inactive user log - emails are sent for security (prevent enumeration)
        $inactiveLog = $logs->where('user_email', '<EMAIL>')->first();

        // Check if response_data exists and has expected structure
        if (isset($inactiveLog->response_data['actual_email_sent'])) {
            // For security, emails are sent to inactive users too
            $this->assertTrue($inactiveLog->response_data['actual_email_sent']);
        }

        // Non-existent user log
        $fakeLog = $logs->where('user_email', '<EMAIL>')->first();
        $this->assertFalse($fakeLog->response_data['actual_email_sent']);
        $this->assertEquals('No email sent', $fakeLog->response_data['admin_action_taken']);
        $this->assertTrue($fakeLog->request_data['enumeration_attempt']);
    }

    /** @test */
    public function password_reset_respects_rate_limiting()
    {
        // Fake notifications to capture them
        Notification::fake();

        // Create a user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        // Make multiple rapid requests (should hit rate limit)
        $lastResponse = null;
        for ($i = 0; $i < 6; $i++) {
            $lastResponse = $this->postJson('/forgot-password', [
                'email' => '<EMAIL>',
            ]);
        }

        // Check if rate limiting is working (may not be enforced in testing)
        // In production, this would return 429, but in testing it might return 200
        $this->assertContains($lastResponse->status(), [200, 429]);

        // Verify activity log shows rate limiting (if rate limiting is working)
        $rateLimitedLog = ActivityLog::where('activity_type', 'password_reset_request')
                                   ->where('user_email', '<EMAIL>')
                                   ->orderBy('created_at', 'desc')
                                   ->first();

        // Rate limiting might not be enforced in testing environment
        if ($rateLimitedLog && isset($rateLimitedLog->request_data['was_rate_limited'])) {
            $this->assertIsBool($rateLimitedLog->request_data['was_rate_limited']);
        }
    }
}
