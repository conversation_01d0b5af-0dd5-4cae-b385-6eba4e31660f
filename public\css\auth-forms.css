/* Auth Forms Styles - Based on form design example */

/* Container and Layout */
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 1rem;
}

.auth-background {
    width: 100%;
    max-width: 480px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.auth-window {
    width: 100%;
    background: white;
    border-radius: 15px;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    padding: 2rem;
    box-sizing: border-box;
}

.auth-window.register-window {
    max-width: 520px;
}

@media (min-width: 576px) {
    .auth-window {
        padding: 3rem 4rem 2rem;
    }
}

/* Header */
.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: #111827;
    margin-bottom: 0.5rem;
    letter-spacing: -0.025em;
}

.auth-subtitle {
    color: #6b7280;
    font-size: 1rem;
    margin: 0;
}

/* Form Styles */
.auth-form {
    display: flex;
    flex-direction: column;
    letter-spacing: 0.5px;
}

/* Enhanced Floating Input Groups */
.floating-input-group {
    position: relative;
    margin-bottom: 1.75rem;
}

.floating-input {
    width: 100%;
    padding: 1.25rem 0 0.75rem 0;
    border: none;
    border-bottom: 2px solid #e5e7eb;
    background: transparent;
    outline: none;
    font-size: 1rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    color: #374151;
    font-weight: 400;
    box-sizing: border-box;
}

.floating-input:focus {
    border-bottom-color: #3b82f6;
    box-shadow: 0 1px 0 0 #3b82f6;
}

.floating-input.error {
    border-bottom-color: #ef4444;
}

.floating-input.success {
    border-bottom-color: #10b981;
}

.floating-label {
    position: absolute;
    top: 1.25rem;
    left: 0;
    font-size: 1rem;
    color: #9ca3af;
    pointer-events: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: left top;
    background: transparent;
    z-index: 1;
}

.floating-input:focus ~ .floating-label,
.floating-input:not(:placeholder-shown) ~ .floating-label,
.floating-input.has-value ~ .floating-label {
    transform: translateY(-1.75rem) scale(0.85);
    color: #3b82f6;
    font-weight: 500;
}

.floating-input.error ~ .floating-label {
    color: #ef4444;
}

.floating-input.success ~ .floating-label {
    color: #10b981;
}

/* Password Toggle */
.password-toggle {
    position: absolute;
    right: 0;
    top: 1rem;
    z-index: 2;
}

.password-toggle-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    color: #6b7280;
    transition: color 0.2s;
    font-size: 1.2rem;
}

.password-toggle-btn:hover {
    color: #374151;
}

/* Error Messages */
.error-message {
    display: block;
    color: #ef4444;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    font-weight: 500;
}

/* Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

/* Checkbox Styles */
.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.875rem;
    color: #374151;
    line-height: 1.4;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkbox-custom {
    width: 18px;
    height: 18px;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    margin-right: 0.75rem;
    position: relative;
    transition: all 0.2s;
    flex-shrink: 0;
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom {
    background-color: #3b82f6;
    border-color: #3b82f6;
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.checkbox-text {
    flex: 1;
}

/* Links */
.forgot-password-link,
.terms-link {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s;
}

.forgot-password-link:hover,
.terms-link:hover {
    color: #1d4ed8;
    text-decoration: underline;
}

/* Submit Button */
.auth-submit-btn {
    width: 100%;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 1rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    margin-bottom: 1.5rem;
    position: relative;
    overflow: hidden;
}

.auth-submit-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.4);
}

.auth-submit-btn:active {
    transform: translateY(0);
}

.auth-submit-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.btn-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-loading svg {
    width: 1.25rem;
    height: 1.25rem;
}

/* Form Messages */
.form-messages {
    margin-bottom: 1rem;
    min-height: 1.5rem;
}

.form-messages .success {
    color: #10b981;
    font-weight: 500;
}

.form-messages .error {
    color: #ef4444;
    font-weight: 500;
}

/* Footer */
.auth-footer {
    text-align: center;
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
}

.auth-footer-text {
    color: #6b7280;
    font-size: 0.875rem;
}

.auth-footer-link {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 600;
    margin-left: 0.5rem;
    transition: color 0.2s;
}

.auth-footer-link:hover {
    color: #1d4ed8;
    text-decoration: underline;
}

/* Password Strength Indicator */
.password-strength {
    margin-top: 0.5rem;
    font-size: 0.75rem;
}

.password-strength .strength-bar {
    height: 4px;
    background-color: #e5e7eb;
    border-radius: 2px;
    margin-bottom: 0.25rem;
    overflow: hidden;
}

.password-strength .strength-fill {
    height: 100%;
    transition: all 0.3s;
    border-radius: 2px;
}

.password-strength.weak .strength-fill {
    width: 25%;
    background-color: #ef4444;
}

.password-strength.weak .strength-text {
    color: #ef4444;
}

.password-strength.fair .strength-fill {
    width: 50%;
    background-color: #f59e0b;
}

.password-strength.fair .strength-text {
    color: #f59e0b;
}

.password-strength.good .strength-fill {
    width: 75%;
    background-color: #3b82f6;
}

.password-strength.good .strength-text {
    color: #3b82f6;
}

.password-strength.strong .strength-fill {
    width: 100%;
    background-color: #10b981;
}

.password-strength.strong .strength-text {
    color: #10b981;
}

/* Responsive Design */
@media (max-width: 640px) {
    .auth-container {
        padding: 0.5rem;
    }
    
    .auth-window {
        padding: 1.5rem;
    }
    
    .auth-title {
        font-size: 1.5rem;
    }
    
    .form-options {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .checkbox-label {
        font-size: 0.8rem;
    }
}

/* Animation Classes */
.animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.text-success {
    color: #10b981;
}

.text-danger {
    color: #ef4444;
}
