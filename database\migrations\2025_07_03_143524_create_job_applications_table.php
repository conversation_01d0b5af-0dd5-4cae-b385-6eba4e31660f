<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('job_applications', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->string('reference_number')->unique();
            $table->foreignId('job_id')->constrained('career_jobs')->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');

            // Personal Information
            $table->string('first_name');
            $table->string('last_name');
            $table->string('email');
            $table->string('phone');
            $table->date('date_of_birth')->nullable();
            $table->string('nationality')->nullable();
            $table->string('id_number')->nullable();

            // Contact Information
            $table->text('address')->nullable();
            $table->string('city')->nullable();
            $table->string('province')->nullable();
            $table->string('postal_code')->nullable();
            $table->string('country')->default('South Africa');

            // Professional Information
            $table->text('cover_letter');
            $table->text('experience_summary');
            $table->string('current_position')->nullable();
            $table->string('current_company')->nullable();
            $table->decimal('current_salary', 10, 2)->nullable();
            $table->decimal('expected_salary', 10, 2)->nullable();
            $table->string('notice_period')->nullable();
            $table->date('available_start_date')->nullable();

            // Education
            $table->string('highest_qualification');
            $table->string('institution')->nullable();
            $table->string('field_of_study')->nullable();
            $table->year('graduation_year')->nullable();

            // Skills and Preferences
            $table->json('skills')->nullable();
            $table->json('languages')->nullable();
            $table->boolean('willing_to_relocate')->default(false);
            $table->boolean('willing_to_travel')->default(false);
            $table->boolean('has_drivers_license')->default(false);
            $table->boolean('has_own_transport')->default(false);

            // Application Status
            $table->enum('status', ['pending', 'reviewing', 'shortlisted', 'interviewed', 'offered', 'hired', 'rejected'])->default('pending');
            $table->text('admin_notes')->nullable();
            $table->foreignId('reviewed_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('reviewed_at')->nullable();

            // File Attachments
            $table->json('attachments')->nullable(); // CV, portfolio, certificates, etc.

            // Additional Information
            $table->text('additional_notes')->nullable();
            $table->boolean('newsletter_signup')->default(false);
            $table->boolean('terms_accepted')->default(true);

            // Tracking
            $table->string('ip_address')->nullable();
            $table->text('user_agent')->nullable();
            $table->string('referrer')->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->index(['status', 'created_at']);
            $table->index(['job_id', 'status']);
            $table->index(['email', 'job_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('job_applications');
    }
};
