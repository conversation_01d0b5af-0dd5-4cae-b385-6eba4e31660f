<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

class OrderController extends Controller
{
    /**
     * Display a listing of orders.
     */
    public function index(Request $request): View
    {
        $query = Order::with(['user', 'items.product', 'currency', 'coupon'])
                     ->where('is_deleted', false);

        // Filter by status if provided
        if ($request->filled('status') && $request->status !== 'all') {
            $query->where('status', $request->status);
        }

        // Filter by payment status if provided
        if ($request->filled('payment_status') && $request->payment_status !== 'all') {
            $query->where('payment_status', $request->payment_status);
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('order_number', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('first_name', 'like', "%{$search}%")
                               ->orWhere('last_name', 'like', "%{$search}%");
                  });
            });
        }

        // Sort by
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        
        $allowedSorts = ['created_at', 'order_number', 'total_amount', 'status', 'payment_status'];
        if (!in_array($sortBy, $allowedSorts)) {
            $sortBy = 'created_at';
        }

        $query->orderBy($sortBy, $sortDirection);

        $orders = $query->paginate(15)->withQueryString();

        // Get status counts for filter tabs
        $statusCounts = [
            'all' => Order::where('is_deleted', false)->count(),
            'pending' => Order::where('is_deleted', false)->where('status', 'pending')->count(),
            'processing' => Order::where('is_deleted', false)->where('status', 'processing')->count(),
            'shipped' => Order::where('is_deleted', false)->where('status', 'shipped')->count(),
            'delivered' => Order::where('is_deleted', false)->where('status', 'delivered')->count(),
            'cancelled' => Order::where('is_deleted', false)->where('status', 'cancelled')->count(),
        ];

        // Get payment status counts
        $paymentStatusCounts = [
            'all' => Order::where('is_deleted', false)->count(),
            'pending' => Order::where('is_deleted', false)->where('payment_status', 'pending')->count(),
            'paid' => Order::where('is_deleted', false)->where('payment_status', 'paid')->count(),
            'failed' => Order::where('is_deleted', false)->where('payment_status', 'failed')->count(),
            'refunded' => Order::where('is_deleted', false)->where('payment_status', 'refunded')->count(),
        ];

        return view('admin.orders.index', compact('orders', 'statusCounts', 'paymentStatusCounts'));
    }

    /**
     * Display the specified order.
     */
    public function show(Order $order): View
    {
        // Check if order is deleted
        if ($order->is_deleted) {
            abort(404);
        }

        // Load relationships
        $order->load(['user', 'items.product', 'items.variant', 'currency', 'coupon']);

        return view('admin.orders.show', compact('order'));
    }

    /**
     * Update the specified order status.
     */
    public function update(Request $request, Order $order): RedirectResponse
    {
        // Check if order is deleted
        if ($order->is_deleted) {
            abort(404);
        }

        $request->validate([
            'status' => 'required|in:pending,processing,shipped,delivered,cancelled',
            'payment_status' => 'nullable|in:pending,paid,failed,refunded',
            'notes' => 'nullable|string|max:1000',
            'shipped_at' => 'nullable|date',
            'delivered_at' => 'nullable|date',
        ]);

        $updateData = [
            'status' => $request->status,
        ];

        // Update payment status if provided
        if ($request->filled('payment_status')) {
            $updateData['payment_status'] = $request->payment_status;
        }

        // Update notes if provided
        if ($request->filled('notes')) {
            $updateData['notes'] = $request->notes;
        }

        // Update shipped_at if status is shipped and date is provided
        if ($request->status === 'shipped' && $request->filled('shipped_at')) {
            $updateData['shipped_at'] = $request->shipped_at;
        }

        // Update delivered_at if status is delivered and date is provided
        if ($request->status === 'delivered' && $request->filled('delivered_at')) {
            $updateData['delivered_at'] = $request->delivered_at;
        }

        $order->update($updateData);

        return back()->with('success', 'Order updated successfully.');
    }

    /**
     * Update order status via AJAX.
     */
    public function updateStatus(Request $request, Order $order)
    {
        // Check if order is deleted
        if ($order->is_deleted) {
            return response()->json(['error' => 'Order not found'], 404);
        }

        $request->validate([
            'status' => 'required|in:pending,processing,shipped,delivered,cancelled',
        ]);

        $updateData = ['status' => $request->status];

        // Auto-set timestamps based on status
        if ($request->status === 'shipped' && !$order->shipped_at) {
            $updateData['shipped_at'] = now();
        }

        if ($request->status === 'delivered' && !$order->delivered_at) {
            $updateData['delivered_at'] = now();
        }

        $order->update($updateData);

        return response()->json([
            'success' => true,
            'message' => 'Order status updated successfully.',
            'status' => $order->status,
            'status_badge' => $this->getStatusBadge($order->status),
        ]);
    }

    /**
     * Get status badge HTML.
     */
    private function getStatusBadge(string $status): string
    {
        $badges = [
            'pending' => '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Pending</span>',
            'processing' => '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Processing</span>',
            'shipped' => '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">Shipped</span>',
            'delivered' => '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Delivered</span>',
            'cancelled' => '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Cancelled</span>',
        ];

        return $badges[$status] ?? $badges['pending'];
    }
}
