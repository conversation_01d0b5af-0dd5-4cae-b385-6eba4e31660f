@extends('layouts.dashboard')

@section('title', 'Edit Product - Admin Dashboard')
@section('page_title', 'Edit Product')

@push('styles')
<style>
.tab-content {
    display: none;
}
.tab-content.active {
    display: block;
}
</style>
@endpush

@section('content')
<div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Edit Product</h1>
            <p class="text-gray-600">Update product information and settings</p>
        </div>
        <div class="flex items-center space-x-3">
            <a href="{{ route('admin.products.index') }}" 
               class="inline-flex items-center px-4 py-2 bg-neutral-600 text-white rounded-lg hover:bg-neutral-700 transition-colors duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                </svg>
                Back to Products
            </a>
            <a href="{{ route('admin.products.show', $product) }}" 
               class="inline-flex items-center px-4 py-2 bg-secondary-600 text-white rounded-lg hover:bg-secondary-700 transition-colors duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                </svg>
                View Product
            </a>
        </div>
    </div>

    <!-- Multi-Step Form -->
    <div class="bg-white rounded-xl shadow-soft border border-neutral-100">
        <!-- Tab Navigation -->
        <div class="border-b border-neutral-200">
            <nav class="flex space-x-8 px-6" aria-label="Tabs">
                <button type="button" 
                        class="tab-button active py-4 px-1 border-b-2 border-primary-500 font-medium text-sm text-primary-600"
                        data-tab="basic">
                    <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center">
                            <span class="text-xs font-semibold text-primary-600">1</span>
                        </div>
                        <span>Basic Info</span>
                    </div>
                </button>
                
                <button type="button" 
                        class="tab-button py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700"
                        data-tab="pricing">
                    <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center">
                            <span class="text-xs font-semibold text-gray-500">2</span>
                        </div>
                        <span>Pricing & Inventory</span>
                    </div>
                </button>
                
                <button type="button" 
                        class="tab-button py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700"
                        data-tab="images">
                    <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center">
                            <span class="text-xs font-semibold text-gray-500">3</span>
                        </div>
                        <span>Images</span>
                    </div>
                </button>
                
                <button type="button" 
                        class="tab-button py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700"
                        data-tab="specifications">
                    <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center">
                            <span class="text-xs font-semibold text-gray-500">4</span>
                        </div>
                        <span>Specifications</span>
                    </div>
                </button>
                
                <button type="button" 
                        class="tab-button py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700"
                        data-tab="seo">
                    <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center">
                            <span class="text-xs font-semibold text-gray-500">5</span>
                        </div>
                        <span>SEO & Settings</span>
                    </div>
                </button>
            </nav>
        </div>

        <!-- Form Content -->
        <form method="POST" action="{{ route('admin.products.update', $product) }}" enctype="multipart/form-data" id="product-form">
            @csrf
            @method('PUT')
            
            <!-- Step 1: Basic Information -->
            <div class="tab-content active" id="basic-tab">
                <div class="p-6 space-y-6">
                    <h3 class="text-lg font-semibold text-gray-900">Basic Information</h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Product Name -->
                        <div class="md:col-span-2">
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                                Product Name <span class="text-red-500">*</span>
                            </label>
                            <input type="text"
                                   id="name"
                                   name="name"
                                   value="{{ old('name', $product->name) }}"
                                   class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('name') border-red-500 @enderror"
                                   placeholder="Enter product name"
                                   required>
                            @error('name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- SKU -->
                        <div>
                            <label for="sku" class="block text-sm font-medium text-gray-700 mb-2">
                                SKU <span class="text-red-500">*</span>
                            </label>
                            <input type="text"
                                   id="sku"
                                   name="sku"
                                   value="{{ old('sku', $product->sku) }}"
                                   class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('sku') border-red-500 @enderror"
                                   placeholder="Enter SKU"
                                   required>
                            @error('sku')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Slug -->
                        <div>
                            <label for="slug" class="block text-sm font-medium text-gray-700 mb-2">
                                URL Slug
                            </label>
                            <input type="text"
                                   id="slug"
                                   name="slug"
                                   value="{{ old('slug', $product->slug) }}"
                                   class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('slug') border-red-500 @enderror"
                                   placeholder="auto-generated-from-name">
                            <p class="mt-1 text-xs text-gray-500">Leave empty to auto-generate from name</p>
                            @error('slug')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Brand -->
                        <div>
                            <label for="brand" class="block text-sm font-medium text-gray-700 mb-2">
                                Brand
                            </label>
                            <input type="text"
                                   id="brand"
                                   name="brand"
                                   value="{{ old('brand', $product->brand) }}"
                                   class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('brand') border-red-500 @enderror"
                                   placeholder="Enter brand name">
                            @error('brand')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Model Number -->
                        <div>
                            <label for="model_number" class="block text-sm font-medium text-gray-700 mb-2">
                                Model Number
                            </label>
                            <input type="text"
                                   id="model_number"
                                   name="model_number"
                                   value="{{ old('model_number', $product->model_number) }}"
                                   class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('model_number') border-red-500 @enderror"
                                   placeholder="Enter model number">
                            @error('model_number')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Short Description -->
                    <div>
                        <label for="short_description" class="block text-sm font-medium text-gray-700 mb-2">
                            Short Description
                        </label>
                        <textarea id="short_description"
                                  name="short_description"
                                  class="tinymce-editor @error('short_description') border-red-500 @enderror"
                                  placeholder="Brief product description for listings">{{ old('short_description', $product->short_description) }}</textarea>
                        @error('short_description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Full Description -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                            Full Description
                        </label>
                        <textarea id="description"
                                  name="description"
                                  class="tinymce-editor @error('description') border-red-500 @enderror"
                                  placeholder="Detailed product description">{{ old('description', $product->description) }}</textarea>
                        @error('description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Categories -->
                    <div>
                        <label for="categories" class="block text-sm font-medium text-gray-700 mb-2">
                            Categories
                        </label>
                        <select id="categories"
                                name="categories[]"
                                multiple
                                class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('categories') border-red-500 @enderror">
                            @foreach($categories as $category)
                                <option value="{{ $category->id }}" {{ in_array($category->id, old('categories', $product->categories->pluck('id')->toArray())) ? 'selected' : '' }}>
                                    {{ $category->name }}
                                </option>
                            @endforeach
                        </select>
                        <p class="mt-1 text-xs text-gray-500">Hold Ctrl/Cmd to select multiple categories</p>
                        @error('categories')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Step 2: Pricing & Inventory -->
            <div class="tab-content" id="pricing-tab">
                <div class="p-6 space-y-6">
                    <h3 class="text-lg font-semibold text-gray-900">Pricing & Inventory</h3>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Price -->
                        <div>
                            <label for="price" class="block text-sm font-medium text-gray-700 mb-2">
                                Price <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <span class="absolute left-3 top-2 text-gray-500">R</span>
                                <input type="number"
                                       id="price"
                                       name="price"
                                       value="{{ old('price', $product->price) }}"
                                       step="0.01"
                                       min="0"
                                       class="w-full pl-8 pr-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('price') border-red-500 @enderror"
                                       placeholder="0.00"
                                       required>
                            </div>
                            @error('price')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Compare Price -->
                        <div>
                            <label for="compare_price" class="block text-sm font-medium text-gray-700 mb-2">
                                Compare Price
                            </label>
                            <div class="relative">
                                <span class="absolute left-3 top-2 text-gray-500">R</span>
                                <input type="number"
                                       id="compare_price"
                                       name="compare_price"
                                       value="{{ old('compare_price', $product->compare_price) }}"
                                       step="0.01"
                                       min="0"
                                       class="w-full pl-8 pr-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('compare_price') border-red-500 @enderror"
                                       placeholder="0.00">
                            </div>
                            <p class="mt-1 text-xs text-gray-500">Original price for showing discounts</p>
                            @error('compare_price')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Cost Price -->
                        <div>
                            <label for="cost_price" class="block text-sm font-medium text-gray-700 mb-2">
                                Cost Price
                            </label>
                            <div class="relative">
                                <span class="absolute left-3 top-2 text-gray-500">R</span>
                                <input type="number"
                                       id="cost_price"
                                       name="cost_price"
                                       value="{{ old('cost_price', $product->cost_price) }}"
                                       step="0.01"
                                       min="0"
                                       class="w-full pl-8 pr-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('cost_price') border-red-500 @enderror"
                                       placeholder="0.00">
                            </div>
                            <p class="mt-1 text-xs text-gray-500">Your cost for this product</p>
                            @error('cost_price')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Inventory Tracking -->
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <input type="checkbox"
                                   id="track_inventory"
                                   name="track_inventory"
                                   value="1"
                                   {{ old('track_inventory', $product->track_inventory) ? 'checked' : '' }}
                                   class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-neutral-300 rounded">
                            <label for="track_inventory" class="ml-2 block text-sm text-gray-900">
                                Track inventory for this product
                            </label>
                        </div>

                        <div id="inventory-fields" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Inventory Quantity -->
                            <div>
                                <label for="inventory_quantity" class="block text-sm font-medium text-gray-700 mb-2">
                                    Inventory Quantity
                                </label>
                                <input type="number"
                                       id="inventory_quantity"
                                       name="inventory_quantity"
                                       value="{{ old('inventory_quantity', $product->inventory_quantity) }}"
                                       min="0"
                                       class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('inventory_quantity') border-red-500 @enderror"
                                       placeholder="0">
                                @error('inventory_quantity')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Low Stock Threshold -->
                            <div>
                                <label for="low_stock_threshold" class="block text-sm font-medium text-gray-700 mb-2">
                                    Low Stock Threshold
                                </label>
                                <input type="number"
                                       id="low_stock_threshold"
                                       name="low_stock_threshold"
                                       value="{{ old('low_stock_threshold', $product->low_stock_threshold) }}"
                                       min="0"
                                       class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('low_stock_threshold') border-red-500 @enderror"
                                       placeholder="5">
                                <p class="mt-1 text-xs text-gray-500">Alert when stock falls below this number</p>
                                @error('low_stock_threshold')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Physical Properties -->
                    <div class="space-y-4">
                        <h4 class="text-md font-medium text-gray-900">Physical Properties</h4>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Weight -->
                            <div>
                                <label for="weight" class="block text-sm font-medium text-gray-700 mb-2">
                                    Weight (grams)
                                </label>
                                <input type="number"
                                       id="weight"
                                       name="weight"
                                       value="{{ old('weight', $product->weight) }}"
                                       step="0.01"
                                       min="0"
                                       class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('weight') border-red-500 @enderror"
                                       placeholder="0.00">
                                @error('weight')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Barcode -->
                            <div>
                                <label for="barcode" class="block text-sm font-medium text-gray-700 mb-2">
                                    Barcode
                                </label>
                                <input type="text"
                                       id="barcode"
                                       name="barcode"
                                       value="{{ old('barcode', $product->barcode) }}"
                                       class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('barcode') border-red-500 @enderror"
                                       placeholder="Enter barcode">
                                @error('barcode')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 3: Images -->
            <div class="tab-content" id="images-tab">
                <div class="p-6 space-y-6">
                    <h3 class="text-lg font-semibold text-gray-900">Product Images</h3>

                    <!-- Current Featured Image -->
                    @if($product->featured_image)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Current Featured Image</label>
                            <div class="mb-4">
                                <img src="{{ asset('storage/' . $product->featured_image) }}"
                                     alt="{{ $product->name }}"
                                     class="w-32 h-32 object-cover rounded-lg border border-neutral-200">
                            </div>
                        </div>
                    @endif

                    <!-- Featured Image -->
                    <div>
                        <label for="featured_image" class="block text-sm font-medium text-gray-700 mb-2">
                            {{ $product->featured_image ? 'Replace Featured Image' : 'Featured Image' }}
                        </label>
                        <div class="border-2 border-dashed border-neutral-300 rounded-lg p-6 text-center hover:border-primary-400 transition-colors duration-200">
                            <div id="featured-image-preview" class="hidden mb-4">
                                <img id="featured-preview-img" src="" alt="Featured image preview" class="mx-auto max-h-48 rounded-lg">
                                <button type="button" id="remove-featured" class="mt-2 text-sm text-red-600 hover:text-red-800">Remove</button>
                            </div>
                            <div id="featured-upload-area">
                                <svg class="mx-auto h-12 w-12 text-neutral-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                <div class="mt-4">
                                    <label for="featured_image" class="cursor-pointer">
                                        <span class="mt-2 block text-sm font-medium text-gray-900">
                                            {{ $product->featured_image ? 'Replace featured image' : 'Upload featured image' }}
                                        </span>
                                        <span class="mt-1 block text-sm text-gray-500">
                                            PNG, JPG, GIF up to 2MB
                                        </span>
                                    </label>
                                    <input id="featured_image" name="featured_image" type="file" accept="image/*" class="sr-only">
                                </div>
                            </div>
                        </div>
                        @error('featured_image')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Current Gallery Images -->
                    @if($product->gallery && count($product->gallery) > 0)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Current Gallery Images</label>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                                @foreach($product->gallery as $image)
                                    <img src="{{ asset('storage/' . $image) }}"
                                         alt="{{ $product->name }}"
                                         class="w-full h-24 object-cover rounded-lg border border-neutral-200">
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <!-- Gallery Images -->
                    <div>
                        <label for="gallery" class="block text-sm font-medium text-gray-700 mb-2">
                            {{ $product->gallery && count($product->gallery) > 0 ? 'Replace Gallery Images' : 'Gallery Images' }}
                        </label>
                        <div class="border-2 border-dashed border-neutral-300 rounded-lg p-6 text-center hover:border-primary-400 transition-colors duration-200">
                            <div id="gallery-preview" class="hidden mb-4">
                                <div id="gallery-images" class="grid grid-cols-2 md:grid-cols-4 gap-4"></div>
                                <button type="button" id="clear-gallery" class="mt-4 text-sm text-red-600 hover:text-red-800">Clear All</button>
                            </div>
                            <div id="gallery-upload-area">
                                <svg class="mx-auto h-12 w-12 text-neutral-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                <div class="mt-4">
                                    <label for="gallery" class="cursor-pointer">
                                        <span class="mt-2 block text-sm font-medium text-gray-900">
                                            {{ $product->gallery && count($product->gallery) > 0 ? 'Replace gallery images' : 'Upload gallery images' }}
                                        </span>
                                        <span class="mt-1 block text-sm text-gray-500">
                                            Select multiple images (PNG, JPG, GIF up to 2MB each)
                                        </span>
                                    </label>
                                    <input id="gallery" name="gallery[]" type="file" accept="image/*" multiple class="sr-only">
                                </div>
                            </div>
                        </div>
                        @error('gallery')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Step 4: Specifications -->
            <div class="tab-content" id="specifications-tab">
                <div class="p-6 space-y-6">
                    <h3 class="text-lg font-semibold text-gray-900">Product Specifications</h3>

                    <!-- Dimensions -->
                    <div>
                        <h4 class="text-md font-medium text-gray-900 mb-4">Dimensions</h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label for="length" class="block text-sm font-medium text-gray-700 mb-2">Length (cm)</label>
                                <input type="number"
                                       id="length"
                                       name="dimensions[length]"
                                       value="{{ old('dimensions.length', $product->dimensions['length'] ?? '') }}"
                                       step="0.01"
                                       min="0"
                                       class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                       placeholder="0.00">
                            </div>
                            <div>
                                <label for="width" class="block text-sm font-medium text-gray-700 mb-2">Width (cm)</label>
                                <input type="number"
                                       id="width"
                                       name="dimensions[width]"
                                       value="{{ old('dimensions.width', $product->dimensions['width'] ?? '') }}"
                                       step="0.01"
                                       min="0"
                                       class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                       placeholder="0.00">
                            </div>
                            <div>
                                <label for="height" class="block text-sm font-medium text-gray-700 mb-2">Height (cm)</label>
                                <input type="number"
                                       id="height"
                                       name="dimensions[height]"
                                       value="{{ old('dimensions.height', $product->dimensions['height'] ?? '') }}"
                                       step="0.01"
                                       min="0"
                                       class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                       placeholder="0.00">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 5: SEO & Settings -->
            <div class="tab-content" id="seo-tab">
                <div class="p-6 space-y-6">
                    <h3 class="text-lg font-semibold text-gray-900">SEO & Settings</h3>

                    <!-- SEO Information -->
                    <div class="space-y-6">
                        <h4 class="text-md font-medium text-gray-900">SEO Information</h4>

                        <!-- Meta Title -->
                        <div>
                            <label for="meta_title" class="block text-sm font-medium text-gray-700 mb-2">
                                Meta Title
                            </label>
                            <input type="text"
                                   id="meta_title"
                                   name="meta_title"
                                   value="{{ old('meta_title', $product->meta_title) }}"
                                   maxlength="255"
                                   class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('meta_title') border-red-500 @enderror"
                                   placeholder="SEO title for search engines">
                            @error('meta_title')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Meta Description -->
                        <div>
                            <label for="meta_description" class="block text-sm font-medium text-gray-700 mb-2">
                                Meta Description
                            </label>
                            <textarea id="meta_description"
                                      name="meta_description"
                                      rows="3"
                                      maxlength="160"
                                      class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('meta_description') border-red-500 @enderror"
                                      placeholder="Brief description for search engines">{{ old('meta_description', $product->meta_description) }}</textarea>
                            @error('meta_description')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Meta Keywords -->
                        <div>
                            <label for="meta_keywords" class="block text-sm font-medium text-gray-700 mb-2">
                                Meta Keywords
                            </label>
                            <input type="text"
                                   id="meta_keywords"
                                   name="meta_keywords"
                                   value="{{ old('meta_keywords', $product->meta_keywords) }}"
                                   class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('meta_keywords') border-red-500 @enderror"
                                   placeholder="keyword1, keyword2, keyword3">
                            <p class="mt-1 text-xs text-gray-500">Separate keywords with commas</p>
                            @error('meta_keywords')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Product Status -->
                    <div class="space-y-4">
                        <h4 class="text-md font-medium text-gray-900">Product Status</h4>

                        <div class="flex items-center">
                            <input type="checkbox"
                                   id="is_active"
                                   name="is_active"
                                   value="1"
                                   {{ old('is_active', $product->is_active) ? 'checked' : '' }}
                                   class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-neutral-300 rounded">
                            <label for="is_active" class="ml-2 block text-sm text-gray-900">
                                Active (visible on website)
                            </label>
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox"
                                   id="is_featured"
                                   name="is_featured"
                                   value="1"
                                   {{ old('is_featured', $product->is_featured) ? 'checked' : '' }}
                                   class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-neutral-300 rounded">
                            <label for="is_featured" class="ml-2 block text-sm text-gray-900">
                                Featured (show in featured sections)
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-between p-6 border-t border-neutral-200 bg-gray-50">
                <div class="flex items-center space-x-4">
                    <button type="button" id="prev-step" class="px-6 py-2 border border-neutral-300 text-neutral-700 rounded-lg hover:bg-neutral-50 transition-colors duration-200 hidden">
                        Previous
                    </button>
                    <button type="button" id="next-step" class="px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200">
                        Next
                    </button>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="{{ route('admin.products.show', $product) }}"
                       class="px-6 py-2 border border-neutral-300 text-neutral-700 rounded-lg hover:bg-neutral-50 transition-colors duration-200">
                        Cancel
                    </a>
                    <button type="submit"
                            id="submit-btn"
                            class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 hidden">
                        Update Product
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    let currentStep = 0;
    const steps = ['basic', 'pricing', 'images', 'specifications', 'seo'];
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');
    const nextBtn = document.getElementById('next-step');
    const prevBtn = document.getElementById('prev-step');
    const submitBtn = document.getElementById('submit-btn');

    // Tab switching functionality
    function showStep(stepIndex) {
        // Hide all tabs
        tabContents.forEach(content => content.classList.remove('active'));
        tabButtons.forEach(button => {
            button.classList.remove('active');
            button.classList.add('text-gray-500', 'border-transparent');
            button.classList.remove('text-primary-600', 'border-primary-500');
            const circle = button.querySelector('div > div');
            circle.classList.remove('bg-primary-100');
            circle.classList.add('bg-gray-100');
            const number = circle.querySelector('span');
            number.classList.remove('text-primary-600');
            number.classList.add('text-gray-500');
        });

        // Show current tab
        const currentTab = document.getElementById(steps[stepIndex] + '-tab');
        const currentButton = document.querySelector(`[data-tab="${steps[stepIndex]}"]`);

        currentTab.classList.add('active');
        currentButton.classList.add('active');
        currentButton.classList.remove('text-gray-500', 'border-transparent');
        currentButton.classList.add('text-primary-600', 'border-primary-500');

        const circle = currentButton.querySelector('div > div');
        circle.classList.add('bg-primary-100');
        circle.classList.remove('bg-gray-100');
        const number = circle.querySelector('span');
        number.classList.add('text-primary-600');
        number.classList.remove('text-gray-500');

        // Update navigation buttons
        prevBtn.classList.toggle('hidden', stepIndex === 0);
        nextBtn.classList.toggle('hidden', stepIndex === steps.length - 1);
        submitBtn.classList.toggle('hidden', stepIndex !== steps.length - 1);

        currentStep = stepIndex;
    }

    // Tab button click handlers
    tabButtons.forEach((button, index) => {
        button.addEventListener('click', () => showStep(index));
    });

    // Navigation button handlers
    nextBtn.addEventListener('click', () => {
        if (currentStep < steps.length - 1) {
            showStep(currentStep + 1);
        }
    });

    prevBtn.addEventListener('click', () => {
        if (currentStep > 0) {
            showStep(currentStep - 1);
        }
    });

    // Auto-generate slug from name
    const nameInput = document.getElementById('name');
    const slugInput = document.getElementById('slug');

    nameInput.addEventListener('input', function() {
        if (!slugInput.value || slugInput.dataset.autoGenerated) {
            const slug = this.value
                .toLowerCase()
                .replace(/[^a-z0-9]+/g, '-')
                .replace(/^-+|-+$/g, '');
            slugInput.value = slug;
            slugInput.dataset.autoGenerated = 'true';
        }
    });

    slugInput.addEventListener('input', function() {
        if (this.value) {
            delete this.dataset.autoGenerated;
        }
    });

    // Inventory tracking toggle
    const trackInventoryCheckbox = document.getElementById('track_inventory');
    const inventoryFields = document.getElementById('inventory-fields');

    function toggleInventoryFields() {
        if (trackInventoryCheckbox.checked) {
            inventoryFields.style.display = 'grid';
        } else {
            inventoryFields.style.display = 'none';
        }
    }

    trackInventoryCheckbox.addEventListener('change', toggleInventoryFields);
    toggleInventoryFields(); // Initial state

    // Image preview functionality
    const featuredImageInput = document.getElementById('featured_image');
    const featuredPreview = document.getElementById('featured-image-preview');
    const featuredPreviewImg = document.getElementById('featured-preview-img');
    const featuredUploadArea = document.getElementById('featured-upload-area');
    const removeFeaturedBtn = document.getElementById('remove-featured');

    featuredImageInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                featuredPreviewImg.src = e.target.result;
                featuredPreview.classList.remove('hidden');
                featuredUploadArea.classList.add('hidden');
            };
            reader.readAsDataURL(file);
        }
    });

    removeFeaturedBtn.addEventListener('click', function() {
        featuredImageInput.value = '';
        featuredPreview.classList.add('hidden');
        featuredUploadArea.classList.remove('hidden');
    });

    // Gallery images preview
    const galleryInput = document.getElementById('gallery');
    const galleryPreview = document.getElementById('gallery-preview');
    const galleryImages = document.getElementById('gallery-images');
    const galleryUploadArea = document.getElementById('gallery-upload-area');
    const clearGalleryBtn = document.getElementById('clear-gallery');

    galleryInput.addEventListener('change', function(e) {
        const files = Array.from(e.target.files);
        if (files.length > 0) {
            galleryImages.innerHTML = '';
            files.forEach((file, index) => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.className = 'w-full h-24 object-cover rounded-lg';
                    galleryImages.appendChild(img);
                };
                reader.readAsDataURL(file);
            });
            galleryPreview.classList.remove('hidden');
            galleryUploadArea.classList.add('hidden');
        }
    });

    clearGalleryBtn.addEventListener('click', function() {
        galleryInput.value = '';
        galleryPreview.classList.add('hidden');
        galleryUploadArea.classList.remove('hidden');
    });

    // Initialize first step
    showStep(0);

    // Rich text editor is automatically initialized by the dashboard layout
});
</script>
@endpush
