<?php

namespace Tests\Feature\Admin;

use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class ProductRichTextTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $admin;
    protected $category;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create admin user
        $this->admin = User::factory()->create([
            'email_verified_at' => now(),
        ]);

        // Set admin role (assuming role_id 1 is admin)
        $this->admin->update(['role_id' => 1]);

        // Create a category
        $this->category = ProductCategory::factory()->create([
            'name' => 'Test Category',
            'slug' => 'test-category',
        ]);
    }

    /** @test */
    public function admin_can_create_product_with_rich_text_content()
    {
        $this->actingAs($this->admin);

        $productData = [
            'name' => 'Test Product with Rich Text',
            'slug' => 'test-product-rich-text',
            'short_description' => '<p>This is a <strong>bold</strong> short description with <em>italic</em> text.</p>',
            'description' => '<h2>Product Features</h2><ul><li>Feature 1</li><li>Feature 2</li></ul><p>This is a detailed description with <a href="#">links</a>.</p>',
            'sku' => 'TEST-RICH-001',
            'brand' => 'Test Brand',
            'model_number' => 'TB-001',
            'price' => 99.99,
            'inventory_quantity' => 10,
            'categories' => [$this->category->id],
        ];

        $response = $this->post(route('admin.products.store'), $productData);

        $response->assertRedirect();
        $this->assertDatabaseHas('products', [
            'name' => 'Test Product with Rich Text',
            'slug' => 'test-product-rich-text',
            'sku' => 'TEST-RICH-001',
            'brand' => 'Test Brand',
            'model_number' => 'TB-001',
            'price' => 99.99,
        ]);

        $product = Product::where('slug', 'test-product-rich-text')->first();
        $this->assertNotNull($product);
        $this->assertStringContainsString('<strong>bold</strong>', $product->short_description);
        $this->assertStringContainsString('<h2>Product Features</h2>', $product->description);
    }

    /** @test */
    public function admin_can_update_product_with_rich_text_content()
    {
        $this->actingAs($this->admin);

        $product = Product::factory()->create([
            'short_description' => 'Plain text description',
            'description' => 'Plain text content',
            'brand' => 'Old Brand',
            'model_number' => 'OLD-001',
        ]);

        $updateData = [
            'name' => $product->name,
            'slug' => $product->slug,
            'short_description' => '<p>Updated <strong>rich text</strong> description</p>',
            'description' => '<h3>Updated Content</h3><p>This is updated rich text content with <em>formatting</em>.</p>',
            'sku' => $product->sku,
            'brand' => 'Updated Brand',
            'model_number' => 'UPD-001',
            'price' => $product->price,
            'inventory_quantity' => $product->inventory_quantity,
            'categories' => [$this->category->id],
        ];

        $response = $this->put(route('admin.products.update', $product), $updateData);

        $response->assertRedirect();
        $product->refresh();
        
        $this->assertEquals('Updated Brand', $product->brand);
        $this->assertEquals('UPD-001', $product->model_number);
        $this->assertStringContainsString('<strong>rich text</strong>', $product->short_description);
        $this->assertStringContainsString('<h3>Updated Content</h3>', $product->description);
    }

    /** @test */
    public function rich_text_content_displays_correctly_on_frontend()
    {
        $product = Product::factory()->create([
            'short_description' => '<p>This is a <strong>bold</strong> short description.</p>',
            'description' => '<h2>Features</h2><ul><li>Feature 1</li><li>Feature 2</li></ul>',
            'brand' => 'Test Brand',
            'model_number' => 'TB-001',
            'is_active' => true,
        ]);

        $product->categories()->attach($this->category);

        $response = $this->get(route('shop.product', $product->slug));

        $response->assertStatus(200);
        $response->assertSee('<strong>bold</strong>', false); // false = don't escape HTML
        $response->assertSee('<h2>Features</h2>', false);
        $response->assertSee('Test Brand');
        $response->assertSee('TB-001');
    }

    /** @test */
    public function brand_and_model_number_fields_are_fillable()
    {
        $product = new Product();
        $fillable = $product->getFillable();

        $this->assertContains('brand', $fillable);
        $this->assertContains('model_number', $fillable);
    }

    /** @test */
    public function database_has_brand_and_model_number_columns()
    {
        $product = Product::factory()->create([
            'brand' => 'Test Brand',
            'model_number' => 'TEST-123',
        ]);

        $this->assertDatabaseHas('products', [
            'id' => $product->id,
            'brand' => 'Test Brand',
            'model_number' => 'TEST-123',
        ]);
    }
}
