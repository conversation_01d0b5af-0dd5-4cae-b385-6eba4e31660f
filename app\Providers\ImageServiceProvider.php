<?php

namespace App\Providers;

use App\Services\ImageService;
use Illuminate\Support\ServiceProvider;

class ImageServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register ImageService as singleton
        $this->app->singleton(ImageService::class, function ($app) {
            return new ImageService();
        });
        
        // Register alias for easier access
        $this->app->alias(ImageService::class, 'image.service');
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Publish configuration file
        $this->publishes([
            __DIR__.'/../../config/image.php' => config_path('image.php'),
        ], 'image-config');
        
        // Set memory limit for image processing
        $memoryLimit = config('image.performance.memory_limit', '256M');
        if ($memoryLimit) {
            ini_set('memory_limit', $memoryLimit);
        }
        
        // Set max execution time for image processing
        $maxExecutionTime = config('image.performance.max_execution_time', 60);
        if ($maxExecutionTime) {
            ini_set('max_execution_time', $maxExecutionTime);
        }
    }
}
