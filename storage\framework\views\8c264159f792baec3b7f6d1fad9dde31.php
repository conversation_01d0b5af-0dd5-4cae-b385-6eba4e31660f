<?php $__env->startSection('title', __('Forgot Password') . ' - ' . __('common.company_name')); ?>
<?php $__env->startSection('meta_description', __('Reset your password')); ?>

<?php $__env->startSection('content'); ?>
<div class="auth-container">
    <div class="auth-background">
        <div class="auth-window">
            <div class="auth-header">
                <h1 class="auth-title"><?php echo e(__('Forgot Password?')); ?></h1>
                <p class="auth-subtitle"><?php echo e(__('Enter your email address and we\'ll send you a link to reset your password.')); ?></p>
            </div>

            <!-- Messages Container -->
            <div id="form-messages" class="form-messages"></div>

            <form id="forgot-password-form" class="auth-form" method="POST" action="<?php echo e(route('password.email')); ?>">
                <?php echo csrf_field(); ?>

                <!-- Email Field -->
                <div class="floating-input-group">
                    <input 
                        type="email" 
                        name="email" 
                        id="email"
                        class="floating-input <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> error <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                        value="<?php echo e(old('email')); ?>"
                        required 
                        autocomplete="email"
                        placeholder=" "
                    >
                    <label for="email" class="floating-label"><?php echo e(__('auth.email')); ?></label>
                    <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <span class="error-message"><?php echo e($message); ?></span>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Submit Button -->
                <button type="submit" id="forgot-btn" class="auth-submit-btn">
                    <span class="btn-text"><?php echo e(__('Send Password Reset Link')); ?></span>
                    <span class="btn-loading" style="display: none;">
                        <svg class="loading-spinner" viewBox="0 0 24 24">
                            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none" opacity="0.25"></circle>
                            <path fill="currentColor" opacity="0.75" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <?php echo e(__('auth.sending_reset_link')); ?>

                    </span>
                </button>

                <!-- Back to Login Link -->
                <div class="auth-footer">
                    <span class="auth-footer-text"><?php echo e(__('Remember your password?')); ?></span>
                    <a href="<?php echo e(route('login')); ?>" class="auth-footer-link"><?php echo e(__('auth.login')); ?></a>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="<?php echo e(asset('js/auth-forms.js')); ?>"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize forgot password form with AJAX validation
    const forgotForm = new AuthForm('forgot-password-form', {
        submitButton: 'forgot-btn',
        loadingText: '<?php echo e(__("auth.sending_reset_link")); ?>',
        successMessage: '<?php echo e(__("passwords.sent_secure")); ?>',
        messages: {
            networkError: '<?php echo e(__("auth.network_error")); ?>',
            serverError: '<?php echo e(__("auth.server_error")); ?>',
            validationError: '<?php echo e(__("auth.validation_error")); ?>'
        }
    });

    // Initialize floating labels
    const floatingInputs = document.querySelectorAll('.floating-input');
    floatingInputs.forEach(input => {
        // Handle initial state
        updateFloatingLabel(input);
        
        // Handle focus events
        input.addEventListener('focus', function() {
            this.classList.add('focus');
            const label = this.parentElement.querySelector('.floating-label');
            if (label) label.classList.add('focus');
        });
        
        // Handle blur events
        input.addEventListener('blur', function() {
            this.classList.remove('focus');
            updateFloatingLabel(this);
        });
        
        // Handle input events
        input.addEventListener('input', function() {
            updateFloatingLabel(this);
        });
    });
    
    function updateFloatingLabel(input) {
        const label = input.parentElement.querySelector('.floating-label');
        if (!label) return;
        
        if (input.value.trim() !== '') {
            input.classList.add('has-value');
            label.classList.add('focus');
        } else {
            input.classList.remove('has-value');
            if (!input.classList.contains('focus')) {
                label.classList.remove('focus');
            }
        }
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.auth', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\files\chisolution\resources\views/auth/forgot-password.blade.php ENDPATH**/ ?>