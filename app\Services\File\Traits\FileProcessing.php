<?php

namespace App\Services\File\Traits;

use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Storage;
use ZipArchive;

trait FileProcessing
{
    /**
     * Extract and analyze archive contents
     */
    public function processArchive(string $filePath): array
    {
        if (!Config::get('file.archive_handling.enabled', false)) {
            return [
                'success' => true,
                'message' => 'Archive processing disabled',
                'extracted_files' => [],
            ];
        }
        
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        
        switch ($extension) {
            case 'zip':
                return $this->processZipArchive($filePath);
            case 'rar':
                return $this->processRarArchive($filePath);
            case '7z':
                return $this->process7zArchive($filePath);
            default:
                return [
                    'success' => false,
                    'message' => 'Unsupported archive format',
                    'extracted_files' => [],
                ];
        }
    }
    
    /**
     * Process ZIP archive
     */
    protected function processZipArchive(string $filePath): array
    {
        if (!class_exists('ZipArchive')) {
            return [
                'success' => false,
                'message' => 'ZIP extension not available',
                'extracted_files' => [],
            ];
        }
        
        $zip = new ZipArchive();
        $result = $zip->open($filePath);
        
        if ($result !== TRUE) {
            return [
                'success' => false,
                'message' => 'Cannot open ZIP archive: ' . $this->getZipError($result),
                'extracted_files' => [],
            ];
        }
        
        $extractionPath = $this->getExtractionPath();
        $extractedFiles = [];
        $maxFiles = Config::get('file.archive_handling.max_extracted_files', 100);
        $maxSize = Config::get('file.archive_handling.max_extracted_size', 500 * 1024 * 1024);
        $totalSize = 0;
        
        for ($i = 0; $i < min($zip->numFiles, $maxFiles); $i++) {
            $stat = $zip->statIndex($i);
            
            if ($stat === false) {
                continue;
            }
            
            // Check file size
            if ($totalSize + $stat['size'] > $maxSize) {
                break;
            }
            
            // Skip directories
            if (substr($stat['name'], -1) === '/') {
                continue;
            }
            
            // Check for dangerous file names
            if ($this->isDangerousFileName($stat['name'])) {
                $this->logSecurityEvent('Dangerous file name in archive', [
                    'archive' => $filePath,
                    'file_name' => $stat['name'],
                ]);
                continue;
            }
            
            // Extract file
            $extractedPath = $extractionPath . '/' . $this->sanitizeArchiveFileName($stat['name']);
            
            if ($zip->extractTo(dirname($extractedPath), $stat['name'])) {
                // Scan extracted file
                $scanResult = $this->scanForViruses($extractedPath);
                
                if (!$scanResult['clean']) {
                    unlink($extractedPath);
                    $this->logSecurityEvent('Infected file in archive', [
                        'archive' => $filePath,
                        'file_name' => $stat['name'],
                        'scan_result' => $scanResult,
                    ]);
                    continue;
                }
                
                $extractedFiles[] = [
                    'name' => $stat['name'],
                    'path' => $extractedPath,
                    'size' => $stat['size'],
                    'type' => $this->getFileTypeFromName($stat['name']),
                ];
                
                $totalSize += $stat['size'];
            }
        }
        
        $zip->close();
        
        return [
            'success' => true,
            'message' => 'Archive processed successfully',
            'extracted_files' => $extractedFiles,
            'total_files' => count($extractedFiles),
            'total_size' => $totalSize,
        ];
    }
    
    /**
     * Process RAR archive
     */
    protected function processRarArchive(string $filePath): array
    {
        if (!extension_loaded('rar')) {
            return [
                'success' => false,
                'message' => 'RAR extension not available',
                'extracted_files' => [],
            ];
        }
        
        // RAR processing would be implemented here
        // For now, return a placeholder
        return [
            'success' => false,
            'message' => 'RAR processing not yet implemented',
            'extracted_files' => [],
        ];
    }
    
    /**
     * Process 7z archive
     */
    protected function process7zArchive(string $filePath): array
    {
        // 7z processing would require external tools or libraries
        // For now, return a placeholder
        return [
            'success' => false,
            'message' => '7z processing not yet implemented',
            'extracted_files' => [],
        ];
    }
    
    /**
     * Extract text content from document
     */
    public function extractTextContent(string $filePath): array
    {
        if (!Config::get('file.content_analysis.extract_text', false)) {
            return [
                'success' => true,
                'text' => '',
                'message' => 'Text extraction disabled',
            ];
        }
        
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        
        switch ($extension) {
            case 'txt':
                return $this->extractTextFromTxt($filePath);
            case 'pdf':
                return $this->extractTextFromPdf($filePath);
            case 'doc':
            case 'docx':
                return $this->extractTextFromWord($filePath);
            case 'xls':
            case 'xlsx':
                return $this->extractTextFromExcel($filePath);
            case 'csv':
                return $this->extractTextFromCsv($filePath);
            default:
                return [
                    'success' => false,
                    'text' => '',
                    'message' => 'Text extraction not supported for this file type',
                ];
        }
    }
    
    /**
     * Extract text from plain text file
     */
    protected function extractTextFromTxt(string $filePath): array
    {
        $content = file_get_contents($filePath);
        
        if ($content === false) {
            return [
                'success' => false,
                'text' => '',
                'message' => 'Cannot read file',
            ];
        }
        
        $maxLength = Config::get('file.content_analysis.max_text_extraction', 100000);
        
        if (strlen($content) > $maxLength) {
            $content = substr($content, 0, $maxLength);
        }
        
        return [
            'success' => true,
            'text' => $content,
            'message' => 'Text extracted successfully',
        ];
    }
    
    /**
     * Extract text from PDF
     */
    protected function extractTextFromPdf(string $filePath): array
    {
        // This would require a PDF library like TCPDF, FPDF, or pdfparser
        // For now, return a placeholder
        return [
            'success' => false,
            'text' => '',
            'message' => 'PDF text extraction not yet implemented',
        ];
    }
    
    /**
     * Extract text from Word document
     */
    protected function extractTextFromWord(string $filePath): array
    {
        // This would require a library like PhpOffice/PhpWord
        // For now, return a placeholder
        return [
            'success' => false,
            'text' => '',
            'message' => 'Word text extraction not yet implemented',
        ];
    }
    
    /**
     * Extract text from Excel file
     */
    protected function extractTextFromExcel(string $filePath): array
    {
        // This would require a library like PhpOffice/PhpSpreadsheet
        // For now, return a placeholder
        return [
            'success' => false,
            'text' => '',
            'message' => 'Excel text extraction not yet implemented',
        ];
    }
    
    /**
     * Extract text from CSV file
     */
    protected function extractTextFromCsv(string $filePath): array
    {
        $handle = fopen($filePath, 'r');
        
        if (!$handle) {
            return [
                'success' => false,
                'text' => '',
                'message' => 'Cannot read CSV file',
            ];
        }
        
        $text = '';
        $maxLength = Config::get('file.content_analysis.max_text_extraction', 100000);
        
        while (($data = fgetcsv($handle)) !== FALSE && strlen($text) < $maxLength) {
            $text .= implode(' ', $data) . "\n";
        }
        
        fclose($handle);
        
        return [
            'success' => true,
            'text' => $text,
            'message' => 'CSV text extracted successfully',
        ];
    }
    
    /**
     * Get extraction path
     */
    protected function getExtractionPath(): string
    {
        $basePath = Config::get('file.archive_handling.extraction_path', storage_path('temp/extraction'));
        $uniquePath = $basePath . '/' . uniqid('extract_', true);
        
        if (!is_dir($uniquePath)) {
            mkdir($uniquePath, 0755, true);
        }
        
        return $uniquePath;
    }
    
    /**
     * Check if filename is dangerous
     */
    protected function isDangerousFileName(string $fileName): bool
    {
        // Check for path traversal
        if (strpos($fileName, '..') !== false) {
            return true;
        }
        
        // Check for absolute paths
        if (strpos($fileName, '/') === 0 || preg_match('/^[A-Za-z]:/', $fileName)) {
            return true;
        }
        
        // Check for forbidden extensions
        $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        $forbiddenExtensions = Config::get('file.forbidden_extensions', []);
        
        return in_array($extension, $forbiddenExtensions);
    }
    
    /**
     * Sanitize archive file name
     */
    protected function sanitizeArchiveFileName(string $fileName): string
    {
        // Remove path components
        $fileName = basename($fileName);
        
        // Sanitize the filename
        return $this->sanitizeFilename($fileName);
    }
    
    /**
     * Get file type from filename
     */
    protected function getFileTypeFromName(string $fileName): ?string
    {
        $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        $fileTypes = Config::get('file.file_types', []);
        
        foreach ($fileTypes as $type => $config) {
            if (in_array($extension, $config['extensions'])) {
                return $type;
            }
        }
        
        return null;
    }
    
    /**
     * Get ZIP error message
     */
    protected function getZipError(int $code): string
    {
        $errors = [
            ZipArchive::ER_OK => 'No error',
            ZipArchive::ER_MULTIDISK => 'Multi-disk zip archives not supported',
            ZipArchive::ER_RENAME => 'Renaming temporary file failed',
            ZipArchive::ER_CLOSE => 'Closing zip archive failed',
            ZipArchive::ER_SEEK => 'Seek error',
            ZipArchive::ER_READ => 'Read error',
            ZipArchive::ER_WRITE => 'Write error',
            ZipArchive::ER_CRC => 'CRC error',
            ZipArchive::ER_ZIPCLOSED => 'Containing zip archive was closed',
            ZipArchive::ER_NOENT => 'No such file',
            ZipArchive::ER_EXISTS => 'File already exists',
            ZipArchive::ER_OPEN => 'Can\'t open file',
            ZipArchive::ER_TMPOPEN => 'Failure to create temporary file',
            ZipArchive::ER_ZLIB => 'Zlib error',
            ZipArchive::ER_MEMORY => 'Memory allocation failure',
            ZipArchive::ER_CHANGED => 'Entry has been changed',
            ZipArchive::ER_COMPNOTSUPP => 'Compression method not supported',
            ZipArchive::ER_EOF => 'Premature EOF',
            ZipArchive::ER_INVAL => 'Invalid argument',
            ZipArchive::ER_NOZIP => 'Not a zip archive',
            ZipArchive::ER_INTERNAL => 'Internal error',
            ZipArchive::ER_INCONS => 'Zip archive inconsistent',
            ZipArchive::ER_REMOVE => 'Can\'t remove file',
            ZipArchive::ER_DELETED => 'Entry has been deleted',
        ];
        
        return $errors[$code] ?? 'Unknown error';
    }
}
