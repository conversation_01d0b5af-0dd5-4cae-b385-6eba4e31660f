<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Product extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'name',
        'slug',
        'short_description',
        'description',
        'sku',
        'barcode',
        'brand',
        'model_number',
        'price',
        'compare_price',
        'cost_price',
        'track_inventory',
        'inventory_quantity',
        'low_stock_threshold',
        'weight',
        'dimensions',
        'featured_image',
        'gallery',
        'is_active',
        'is_featured',
        'is_deleted',
        'meta_title',
        'meta_description',
        'meta_keywords',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'compare_price' => 'decimal:2',
        'cost_price' => 'decimal:2',
        'weight' => 'decimal:2',
        'dimensions' => 'array',
        'gallery' => 'array',
        'track_inventory' => 'boolean',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'is_deleted' => 'boolean',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($product) {
            if (empty($product->uuid)) {
                $product->uuid = Str::uuid();
            }
            if (empty($product->slug)) {
                $product->slug = Str::slug($product->name);
            }
        });

        static::updating(function ($product) {
            if ($product->isDirty('name') && empty($product->slug)) {
                $product->slug = Str::slug($product->name);
            }
        });
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Scope a query to only include active products.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)->where('is_deleted', false);
    }

    /**
     * Scope a query to only include featured products.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope a query to only include products in stock.
     */
    public function scopeInStock($query)
    {
        return $query->where(function ($q) {
            $q->where('track_inventory', false)
              ->orWhere('inventory_quantity', '>', 0);
        });
    }

    /**
     * Scope a query to search products by name or description.
     */
    public function scopeSearch($query, $term)
    {
        return $query->where(function ($q) use ($term) {
            $q->where('name', 'like', "%{$term}%")
              ->orWhere('description', 'like', "%{$term}%")
              ->orWhere('sku', 'like', "%{$term}%");
        });
    }

    /**
     * Get the categories for the product.
     */
    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(ProductCategory::class, 'product_category_relations', 'product_id', 'category_id');
    }

    /**
     * Get the product variants.
     */
    public function variants(): HasMany
    {
        return $this->hasMany(ProductVariant::class);
    }

    /**
     * Get the cart items for this product.
     */
    public function cartItems(): HasMany
    {
        return $this->hasMany(CartItem::class);
    }

    /**
     * Check if product is in stock.
     */
    public function isInStock(): bool
    {
        if (!$this->track_inventory) {
            return true;
        }

        return $this->inventory_quantity > 0;
    }

    /**
     * Check if product is low in stock.
     */
    public function isLowStock(): bool
    {
        if (!$this->track_inventory) {
            return false;
        }

        return $this->inventory_quantity <= $this->low_stock_threshold;
    }

    /**
     * Get the formatted price.
     */
    public function getFormattedPriceAttribute(): string
    {
        return 'R' . number_format($this->price, 2);
    }

    /**
     * Get the formatted compare price.
     */
    public function getFormattedComparePriceAttribute(): string
    {
        return $this->compare_price ? 'R' . number_format($this->compare_price, 2) : '';
    }

    /**
     * Get the discount percentage.
     */
    public function getDiscountPercentageAttribute(): int
    {
        if (!$this->compare_price || $this->compare_price <= $this->price) {
            return 0;
        }

        return round((($this->compare_price - $this->price) / $this->compare_price) * 100);
    }

    /**
     * Get the featured image URL.
     */
    public function getFeaturedImageUrlAttribute(): string
    {
        if (!$this->featured_image) {
            return asset('images/products/placeholder.jpg');
        }

        $imagePath = $this->featured_image;

        // Handle legacy full system paths and normalize
        $originalPath = $imagePath;
        $needsUpdate = false;

        // Handle various legacy path formats
        if (str_contains($imagePath, storage_path())) {
            // Convert full system path to relative path
            $imagePath = str_replace(storage_path('app/public/'), '', $imagePath);
            $imagePath = str_replace('\\', '/', $imagePath); // Normalize path separators
            $needsUpdate = true;
        } elseif (str_starts_with($imagePath, 'C:') || str_starts_with($imagePath, '/')) {
            // Handle other absolute paths - extract just the relative part
            $pathParts = explode('/', str_replace('\\', '/', $imagePath));
            $storageIndex = array_search('storage', $pathParts);
            if ($storageIndex !== false && isset($pathParts[$storageIndex + 1])) {
                $imagePath = implode('/', array_slice($pathParts, $storageIndex + 1));
                $needsUpdate = true;
            }
        }

        // Clean up any double slashes and ensure proper format
        $imagePath = ltrim(str_replace('//', '/', $imagePath), '/');

        // Update the database with the corrected path if needed
        if ($needsUpdate && $imagePath !== $originalPath) {
            try {
                $this->updateQuietly(['featured_image' => $imagePath]);
            } catch (\Exception $e) {
                // Log but don't fail if update fails
                \Log::warning('Failed to update product image path', [
                    'product_id' => $this->id,
                    'original_path' => $originalPath,
                    'new_path' => $imagePath,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return app(\App\Services\ImageService::class)->getImageUrl($imagePath);
    }

    /**
     * Get the primary image URL (alias for featured_image_url).
     */
    public function getPrimaryImageAttribute(): string
    {
        return $this->featured_image_url;
    }

    /**
     * Get all product images with URLs.
     */
    public function getAllImagesAttribute(): array
    {
        $images = [];

        if ($this->featured_image) {
            $images[] = $this->featured_image_url;
        }

        if ($this->gallery && is_array($this->gallery)) {
            $imageService = app(\App\Services\ImageService::class);
            foreach ($this->gallery as $imagePath) {
                $images[] = $imageService->getImageUrl($imagePath);
            }
        }

        return array_unique($images);
    }

    /**
     * Get the stock status.
     */
    public function getStockStatusAttribute(): string
    {
        if (!$this->track_inventory) {
            return 'In Stock';
        }

        if ($this->inventory_quantity <= 0) {
            return 'Out of Stock';
        }

        if ($this->isLowStock()) {
            return 'Low Stock';
        }

        return 'In Stock';
    }

    /**
     * Get the SEO title.
     */
    public function getSeoTitleAttribute(): string
    {
        return $this->meta_title ?: $this->name;
    }

    /**
     * Get the SEO description.
     */
    public function getSeoDescriptionAttribute(): string
    {
        return $this->meta_description ?: $this->short_description ?: Str::limit(strip_tags($this->description), 160);
    }

    /**
     * Decrease inventory quantity.
     */
    public function decreaseInventory(int $quantity): bool
    {
        if (!$this->track_inventory) {
            return true;
        }

        if ($this->inventory_quantity < $quantity) {
            return false;
        }

        $this->decrement('inventory_quantity', $quantity);
        return true;
    }

    /**
     * Increase inventory quantity.
     */
    public function increaseInventory(int $quantity): void
    {
        if ($this->track_inventory) {
            $this->increment('inventory_quantity', $quantity);
        }
    }
}
