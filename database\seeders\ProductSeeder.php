<?php

namespace Database\Seeders;

use App\Models\Product;
use App\Models\ProductCategory;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create categories
        $webDev = ProductCategory::firstOrCreate(
            ['slug' => 'web-development'],
            [
                'name' => 'Web Development',
                'description' => 'Professional web development services and packages',
                'is_active' => true,
                'sort_order' => 1,
            ]
        );

        $mobileDev = ProductCategory::firstOrCreate(
            ['slug' => 'mobile-development'],
            [
                'name' => 'Mobile Development',
                'description' => 'Mobile app development for iOS and Android',
                'is_active' => true,
                'sort_order' => 2,
            ]
        );

        $digitalMarketing = ProductCategory::firstOrCreate(
            ['slug' => 'digital-marketing'],
            [
                'name' => 'Digital Marketing',
                'description' => 'Digital marketing and SEO services',
                'is_active' => true,
                'sort_order' => 3,
            ]
        );

        $maintenance = ProductCategory::firstOrCreate(
            ['slug' => 'maintenance-support'],
            [
                'name' => 'Maintenance & Support',
                'description' => 'Website and application maintenance services',
                'is_active' => true,
                'sort_order' => 4,
            ]
        );

        // Create products
        $products = [
            [
                'name' => 'Basic Website Package',
                'slug' => 'basic-website-package',
                'short_description' => 'Perfect starter website for small businesses',
                'description' => 'A professional 5-page website with responsive design, contact forms, and basic SEO optimization. Perfect for small businesses looking to establish their online presence.',
                'sku' => 'WEB-BASIC-001',
                'price' => 4999.00,
                'compare_price' => 6999.00,
                'track_inventory' => false,
                'is_active' => true,
                'is_featured' => true,
                'meta_title' => 'Basic Website Package - Professional Web Development',
                'meta_description' => 'Get a professional 5-page website with responsive design and SEO optimization.',
                'categories' => [$webDev->id],
            ],
            [
                'name' => 'E-commerce Website',
                'slug' => 'ecommerce-website',
                'short_description' => 'Complete online store solution',
                'description' => 'Full-featured e-commerce website with product catalog, shopping cart, payment integration, and admin panel. Includes inventory management and order processing.',
                'sku' => 'WEB-ECOM-001',
                'price' => 15999.00,
                'compare_price' => 19999.00,
                'track_inventory' => false,
                'is_active' => true,
                'is_featured' => true,
                'meta_title' => 'E-commerce Website Development - Online Store Solution',
                'meta_description' => 'Complete e-commerce website with shopping cart, payments, and admin panel.',
                'categories' => [$webDev->id],
            ],
            [
                'name' => 'Mobile App Development',
                'slug' => 'mobile-app-development',
                'short_description' => 'Custom mobile app for iOS and Android',
                'description' => 'Custom mobile application development for both iOS and Android platforms. Includes UI/UX design, development, testing, and app store submission.',
                'sku' => 'MOB-APP-001',
                'price' => 25999.00,
                'compare_price' => 32999.00,
                'track_inventory' => false,
                'is_active' => true,
                'is_featured' => true,
                'meta_title' => 'Mobile App Development - iOS and Android Apps',
                'meta_description' => 'Custom mobile app development for iOS and Android with UI/UX design.',
                'categories' => [$mobileDev->id],
            ],
            [
                'name' => 'SEO Optimization Package',
                'slug' => 'seo-optimization-package',
                'short_description' => 'Boost your search engine rankings',
                'description' => 'Comprehensive SEO package including keyword research, on-page optimization, technical SEO audit, and 3 months of ongoing optimization.',
                'sku' => 'SEO-OPT-001',
                'price' => 2999.00,
                'compare_price' => 3999.00,
                'track_inventory' => false,
                'is_active' => true,
                'is_featured' => false,
                'meta_title' => 'SEO Optimization Package - Improve Search Rankings',
                'meta_description' => 'Comprehensive SEO package with keyword research and ongoing optimization.',
                'categories' => [$digitalMarketing->id],
            ],
            [
                'name' => 'Website Maintenance Plan',
                'slug' => 'website-maintenance-plan',
                'short_description' => 'Monthly website maintenance and updates',
                'description' => 'Monthly website maintenance including security updates, content updates, performance monitoring, and technical support.',
                'sku' => 'MAINT-MONTH-001',
                'price' => 499.00,
                'track_inventory' => false,
                'is_active' => true,
                'is_featured' => false,
                'meta_title' => 'Website Maintenance Plan - Monthly Support',
                'meta_description' => 'Monthly website maintenance with security updates and technical support.',
                'categories' => [$maintenance->id],
            ],
            [
                'name' => 'Social Media Marketing',
                'slug' => 'social-media-marketing',
                'short_description' => 'Professional social media management',
                'description' => 'Complete social media marketing package including content creation, posting schedule, engagement management, and monthly analytics reports.',
                'sku' => 'SMM-PACK-001',
                'price' => 1999.00,
                'compare_price' => 2499.00,
                'track_inventory' => false,
                'is_active' => true,
                'is_featured' => false,
                'meta_title' => 'Social Media Marketing - Professional Management',
                'meta_description' => 'Complete social media marketing with content creation and analytics.',
                'categories' => [$digitalMarketing->id],
            ],
        ];

        foreach ($products as $productData) {
            $categories = $productData['categories'];
            unset($productData['categories']);

            $product = Product::firstOrCreate(
                ['sku' => $productData['sku']],
                $productData
            );

            if (!$product->categories()->exists()) {
                $product->categories()->attach($categories);
            }
        }
    }
}
