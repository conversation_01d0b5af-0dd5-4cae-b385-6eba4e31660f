@extends('layouts.app')

@section('title', __('common.blog') . ' - ' . __('common.company_name'))
@section('meta_description', 'Stay updated with the latest insights, tutorials, and industry trends in web development, mobile apps, and digital marketing.')
@section('meta_keywords', 'blog, web development, mobile apps, digital marketing, tutorials, industry insights, technology trends')

@section('content')
<!-- Hero Section -->
<section class="relative bg-gradient-blue text-white overflow-hidden">
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></g></svg>');"></div>
    </div>
    
    <div class="container mx-auto px-4 py-20 lg:py-32 relative z-10">
        <div class="text-center max-w-4xl mx-auto">
            <h1 class="heading-1 text-white mb-6">
                Our <span class="text-blue-300">Blog</span>
            </h1>
            <p class="text-lead text-blue-100 mb-8">
                Insights, tutorials, and industry trends to help you stay ahead in the digital world. Learn from our experience and expertise.
            </p>
        </div>
    </div>
    
    <div class="absolute bottom-0 left-0 w-full">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" class="relative block w-full h-16 fill-white">
            <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"></path>
        </svg>
    </div>
</section>

<!-- Featured Post -->
<section class="py-20 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="heading-2 mb-4">
                Featured <span class="text-blue-600">Article</span>
            </h2>
        </div>
        
        <div class="max-w-4xl mx-auto">
            <article class="card-hover">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                    <div class="relative overflow-hidden rounded-lg">
                        <img src="{{ asset('images/blog/featured-post.jpg') }}" alt="Featured Article" class="w-full h-64 object-cover hover-lift">
                    </div>
                    <div>
                        <div class="flex items-center space-x-2 mb-4">
                            <span class="px-3 py-1 bg-blue-100 text-blue-600 text-sm rounded-full">Web Development</span>
                            <span class="text-gray-500 text-sm">March 15, 2024</span>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-4">
                            The Future of Web Development: Trends to Watch in 2024
                        </h3>
                        <p class="text-gray-600 mb-6 leading-relaxed">
                            Explore the latest trends shaping the web development landscape, from AI integration to progressive web apps and the rise of serverless architecture.
                        </p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <img src="{{ asset('images/team/author-1.jpg') }}" alt="Author" class="w-10 h-10 rounded-full">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">Sarah Johnson</div>
                                    <div class="text-xs text-gray-500">Lead Developer</div>
                                </div>
                            </div>
                            <a href="#" class="text-blue-600 font-semibold hover:text-blue-700 transition-colors inline-flex items-center">
                                Read More
                                <svg class="ml-1 w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </article>
        </div>
    </div>
</section>

<!-- Categories Filter -->
<section class="py-12 bg-gray-50 border-b border-gray-200">
    <div class="container mx-auto px-4">
        <div class="flex flex-wrap justify-center gap-4">
            <button class="category-btn active" data-category="all">All Posts</button>
            <button class="category-btn" data-category="web-development">Web Development</button>
            <button class="category-btn" data-category="mobile-apps">Mobile Apps</button>
            <button class="category-btn" data-category="digital-marketing">Digital Marketing</button>
            <button class="category-btn" data-category="tutorials">Tutorials</button>
            <button class="category-btn" data-category="industry-insights">Industry Insights</button>
        </div>
    </div>
</section>

<!-- Blog Posts Grid -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" id="blog-grid">
            <!-- Blog Post 1 -->
            <article class="blog-card card-hover" data-category="mobile-apps tutorials">
                <div class="relative overflow-hidden rounded-lg mb-6">
                    <img src="{{ asset('images/blog/mobile-app-ui.jpg') }}" alt="Mobile App UI Design" class="w-full h-48 object-cover hover-lift">
                </div>
                <div class="space-y-3">
                    <div class="flex items-center space-x-2">
                        <span class="px-2 py-1 bg-purple-100 text-purple-600 text-xs rounded">Mobile Apps</span>
                        <span class="text-gray-500 text-xs">March 10, 2024</span>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors">
                        <a href="#">10 Essential UI/UX Principles for Mobile App Design</a>
                    </h3>
                    <p class="text-gray-600 text-sm leading-relaxed">
                        Learn the fundamental principles that make mobile apps intuitive, engaging, and user-friendly.
                    </p>
                    <div class="flex items-center justify-between pt-3">
                        <div class="flex items-center space-x-2">
                            <img src="{{ asset('images/team/author-2.jpg') }}" alt="Author" class="w-8 h-8 rounded-full">
                            <span class="text-sm text-gray-600">Mike Chen</span>
                        </div>
                        <span class="text-xs text-gray-500">5 min read</span>
                    </div>
                </div>
            </article>
            
            <!-- Blog Post 2 -->
            <article class="blog-card card-hover" data-category="digital-marketing industry-insights">
                <div class="relative overflow-hidden rounded-lg mb-6">
                    <img src="{{ asset('images/blog/seo-trends.jpg') }}" alt="SEO Trends 2024" class="w-full h-48 object-cover hover-lift">
                </div>
                <div class="space-y-3">
                    <div class="flex items-center space-x-2">
                        <span class="px-2 py-1 bg-green-100 text-green-600 text-xs rounded">Digital Marketing</span>
                        <span class="text-gray-500 text-xs">March 8, 2024</span>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors">
                        <a href="#">SEO Trends That Will Dominate 2024</a>
                    </h3>
                    <p class="text-gray-600 text-sm leading-relaxed">
                        Stay ahead of the curve with these emerging SEO trends and strategies for better search rankings.
                    </p>
                    <div class="flex items-center justify-between pt-3">
                        <div class="flex items-center space-x-2">
                            <img src="{{ asset('images/team/author-3.jpg') }}" alt="Author" class="w-8 h-8 rounded-full">
                            <span class="text-sm text-gray-600">Emma Davis</span>
                        </div>
                        <span class="text-xs text-gray-500">7 min read</span>
                    </div>
                </div>
            </article>
            
            <!-- Blog Post 3 -->
            <article class="blog-card card-hover" data-category="web-development tutorials">
                <div class="relative overflow-hidden rounded-lg mb-6">
                    <img src="{{ asset('images/blog/laravel-tips.jpg') }}" alt="Laravel Development Tips" class="w-full h-48 object-cover hover-lift">
                </div>
                <div class="space-y-3">
                    <div class="flex items-center space-x-2">
                        <span class="px-2 py-1 bg-red-100 text-red-600 text-xs rounded">Web Development</span>
                        <span class="text-gray-500 text-xs">March 5, 2024</span>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors">
                        <a href="#">Laravel Performance Optimization: Best Practices</a>
                    </h3>
                    <p class="text-gray-600 text-sm leading-relaxed">
                        Discover proven techniques to optimize your Laravel applications for better performance and scalability.
                    </p>
                    <div class="flex items-center justify-between pt-3">
                        <div class="flex items-center space-x-2">
                            <img src="{{ asset('images/team/author-1.jpg') }}" alt="Author" class="w-8 h-8 rounded-full">
                            <span class="text-sm text-gray-600">Sarah Johnson</span>
                        </div>
                        <span class="text-xs text-gray-500">8 min read</span>
                    </div>
                </div>
            </article>
            
            <!-- Blog Post 4 -->
            <article class="blog-card card-hover" data-category="industry-insights">
                <div class="relative overflow-hidden rounded-lg mb-6">
                    <img src="{{ asset('images/blog/ai-development.jpg') }}" alt="AI in Development" class="w-full h-48 object-cover hover-lift">
                </div>
                <div class="space-y-3">
                    <div class="flex items-center space-x-2">
                        <span class="px-2 py-1 bg-yellow-100 text-yellow-600 text-xs rounded">Industry Insights</span>
                        <span class="text-gray-500 text-xs">March 3, 2024</span>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors">
                        <a href="#">How AI is Transforming Software Development</a>
                    </h3>
                    <p class="text-gray-600 text-sm leading-relaxed">
                        Explore the impact of artificial intelligence on modern software development practices and workflows.
                    </p>
                    <div class="flex items-center justify-between pt-3">
                        <div class="flex items-center space-x-2">
                            <img src="{{ asset('images/team/author-4.jpg') }}" alt="Author" class="w-8 h-8 rounded-full">
                            <span class="text-sm text-gray-600">Alex Rodriguez</span>
                        </div>
                        <span class="text-xs text-gray-500">6 min read</span>
                    </div>
                </div>
            </article>
            
            <!-- Blog Post 5 -->
            <article class="blog-card card-hover" data-category="mobile-apps industry-insights">
                <div class="relative overflow-hidden rounded-lg mb-6">
                    <img src="{{ asset('images/blog/flutter-vs-react.jpg') }}" alt="Flutter vs React Native" class="w-full h-48 object-cover hover-lift">
                </div>
                <div class="space-y-3">
                    <div class="flex items-center space-x-2">
                        <span class="px-2 py-1 bg-purple-100 text-purple-600 text-xs rounded">Mobile Apps</span>
                        <span class="text-gray-500 text-xs">February 28, 2024</span>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors">
                        <a href="#">Flutter vs React Native: Which to Choose in 2024?</a>
                    </h3>
                    <p class="text-gray-600 text-sm leading-relaxed">
                        A comprehensive comparison of the two leading cross-platform mobile development frameworks.
                    </p>
                    <div class="flex items-center justify-between pt-3">
                        <div class="flex items-center space-x-2">
                            <img src="{{ asset('images/team/author-2.jpg') }}" alt="Author" class="w-8 h-8 rounded-full">
                            <span class="text-sm text-gray-600">Mike Chen</span>
                        </div>
                        <span class="text-xs text-gray-500">10 min read</span>
                    </div>
                </div>
            </article>
            
            <!-- Blog Post 6 -->
            <article class="blog-card card-hover" data-category="digital-marketing tutorials">
                <div class="relative overflow-hidden rounded-lg mb-6">
                    <img src="{{ asset('images/blog/social-media-strategy.jpg') }}" alt="Social Media Strategy" class="w-full h-48 object-cover hover-lift">
                </div>
                <div class="space-y-3">
                    <div class="flex items-center space-x-2">
                        <span class="px-2 py-1 bg-green-100 text-green-600 text-xs rounded">Digital Marketing</span>
                        <span class="text-gray-500 text-xs">February 25, 2024</span>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors">
                        <a href="#">Building an Effective Social Media Strategy for Tech Companies</a>
                    </h3>
                    <p class="text-gray-600 text-sm leading-relaxed">
                        Learn how to create and execute a social media strategy that drives engagement and growth.
                    </p>
                    <div class="flex items-center justify-between pt-3">
                        <div class="flex items-center space-x-2">
                            <img src="{{ asset('images/team/author-3.jpg') }}" alt="Author" class="w-8 h-8 rounded-full">
                            <span class="text-sm text-gray-600">Emma Davis</span>
                        </div>
                        <span class="text-xs text-gray-500">9 min read</span>
                    </div>
                </div>
            </article>
        </div>
        
        <!-- Pagination -->
        <div class="flex justify-center mt-12">
            <nav class="flex items-center space-x-2">
                <button class="px-3 py-2 text-gray-500 hover:text-gray-700 disabled:opacity-50" disabled>
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                </button>
                <button class="px-3 py-2 bg-blue-600 text-white rounded">1</button>
                <button class="px-3 py-2 text-gray-700 hover:text-blue-600">2</button>
                <button class="px-3 py-2 text-gray-700 hover:text-blue-600">3</button>
                <span class="px-3 py-2 text-gray-500">...</span>
                <button class="px-3 py-2 text-gray-700 hover:text-blue-600">10</button>
                <button class="px-3 py-2 text-gray-500 hover:text-gray-700">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </nav>
        </div>
    </div>
</section>

<!-- Newsletter Section -->
<section class="py-20 bg-blue-600 text-white">
    <div class="container mx-auto px-4 text-center">
        <h2 class="heading-2 text-white mb-4">
            Stay Updated
        </h2>
        <p class="text-lead text-blue-100 mb-8 max-w-2xl mx-auto">
            Subscribe to our newsletter and get the latest insights, tutorials, and industry trends delivered to your inbox.
        </p>
        <form class="max-w-md mx-auto flex gap-4">
            <input type="email" placeholder="Enter your email" class="form-input flex-1 text-gray-900">
            <button type="submit" class="btn-primary bg-white text-blue-600 hover:bg-blue-50 whitespace-nowrap">
                Subscribe
            </button>
        </form>
    </div>
</section>

@push('styles')
<style>
.category-btn {
    @apply px-4 py-2 rounded-lg text-sm font-medium transition-colors;
    @apply bg-white text-gray-600 hover:bg-gray-50;
}

.category-btn.active {
    @apply bg-blue-600 text-white;
}

.blog-card {
    transition: transform 0.3s ease;
}

.blog-card:hover {
    transform: translateY(-4px);
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const categoryButtons = document.querySelectorAll('.category-btn');
    const blogCards = document.querySelectorAll('.blog-card');
    
    categoryButtons.forEach(button => {
        button.addEventListener('click', function() {
            const category = this.getAttribute('data-category');
            
            // Update active button
            categoryButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Filter blog posts
            blogCards.forEach(card => {
                const categories = card.getAttribute('data-category');
                
                if (category === 'all' || categories.includes(category)) {
                    card.style.display = 'block';
                    card.classList.add('animate-fade-in');
                } else {
                    card.style.display = 'none';
                }
            });
        });
    });
});
</script>
@endpush
@endsection
