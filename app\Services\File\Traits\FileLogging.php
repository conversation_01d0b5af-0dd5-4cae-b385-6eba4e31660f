<?php

namespace App\Services\File\Traits;

use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;

trait FileLogging
{
    /**
     * Log file processing event
     */
    public function logProcessing(string $message, array $context = []): void
    {
        if (!Config::get('file.logging.enabled', false)) {
            return;
        }
        
        $channel = Config::get('file.logging.log_channel', 'daily');
        $level = Config::get('file.logging.log_level', 'info');
        
        Log::channel($channel)->log($level, "[FileService] {$message}", $context);
    }
    
    /**
     * Log error event
     */
    public function logError(string $message, array $context = []): void
    {
        if (!Config::get('file.logging.enabled', false)) {
            return;
        }
        
        $channel = Config::get('file.logging.log_channel', 'daily');
        
        Log::channel($channel)->error("[FileService] {$message}", $context);
    }
    
    /**
     * Log security event
     */
    public function logSecurityEvent(string $message, array $context = []): void
    {
        if (!Config::get('file.logging.log_security_events', false)) {
            return;
        }
        
        $channel = Config::get('file.logging.log_channel', 'daily');
        
        Log::channel($channel)->warning("[FileService Security] {$message}", $context);
    }
    
    /**
     * Log processing time
     */
    public function logProcessingTime(string $operation, float $timeMs, array $context = []): void
    {
        if (!Config::get('file.logging.log_processing_time', false)) {
            return;
        }
        
        $channel = Config::get('file.logging.log_channel', 'daily');
        $level = Config::get('file.logging.log_level', 'info');
        
        $context['processing_time_ms'] = $timeMs;
        $context['operation'] = $operation;
        
        Log::channel($channel)->log($level, "[FileService Performance] {$operation} completed in {$timeMs}ms", $context);
    }
    
    /**
     * Log file details
     */
    public function logFileDetails(string $operation, array $fileInfo, array $context = []): void
    {
        if (!Config::get('file.logging.log_file_details', false)) {
            return;
        }
        
        $channel = Config::get('file.logging.log_channel', 'daily');
        $level = Config::get('file.logging.log_level', 'info');
        
        $context = array_merge($context, [
            'operation' => $operation,
            'file_info' => $fileInfo,
        ]);
        
        Log::channel($channel)->log($level, "[FileService] {$operation} - File: {$fileInfo['original_name']}", $context);
    }
    
    /**
     * Log validation result
     */
    public function logValidation(array $validationResult, array $fileInfo): void
    {
        if (!Config::get('file.logging.enabled', false)) {
            return;
        }
        
        $channel = Config::get('file.logging.log_channel', 'daily');
        
        if ($validationResult['valid']) {
            $level = Config::get('file.logging.log_level', 'info');
            Log::channel($channel)->log($level, "[FileService] File validation passed", [
                'file_name' => $fileInfo['original_name'],
                'file_size' => $fileInfo['size'],
                'file_type' => $fileInfo['type_category'],
            ]);
        } else {
            Log::channel($channel)->warning("[FileService] File validation failed", [
                'file_name' => $fileInfo['original_name'],
                'file_size' => $fileInfo['size'],
                'errors' => $validationResult['errors'],
            ]);
        }
    }
    
    /**
     * Log virus scan result
     */
    public function logVirusScan(array $scanResult, string $filePath): void
    {
        if (!Config::get('file.logging.log_security_events', false)) {
            return;
        }
        
        $channel = Config::get('file.logging.log_channel', 'daily');
        
        if ($scanResult['clean']) {
            $level = Config::get('file.logging.log_level', 'info');
            Log::channel($channel)->log($level, "[FileService] Virus scan passed", [
                'file_path' => $filePath,
                'scanner' => $scanResult['scanner'],
                'message' => $scanResult['message'],
            ]);
        } else {
            Log::channel($channel)->error("[FileService] Virus scan failed", [
                'file_path' => $filePath,
                'scanner' => $scanResult['scanner'],
                'message' => $scanResult['message'],
                'output' => $scanResult['output'] ?? null,
            ]);
        }
    }
    
    /**
     * Log archive processing
     */
    public function logArchiveProcessing(array $result, string $archivePath): void
    {
        if (!Config::get('file.logging.enabled', false)) {
            return;
        }
        
        $channel = Config::get('file.logging.log_channel', 'daily');
        $level = Config::get('file.logging.log_level', 'info');
        
        if ($result['success']) {
            Log::channel($channel)->log($level, "[FileService] Archive processed successfully", [
                'archive_path' => $archivePath,
                'extracted_files' => $result['total_files'] ?? 0,
                'total_size' => $result['total_size'] ?? 0,
            ]);
        } else {
            Log::channel($channel)->error("[FileService] Archive processing failed", [
                'archive_path' => $archivePath,
                'message' => $result['message'],
            ]);
        }
    }
    
    /**
     * Log text extraction
     */
    public function logTextExtraction(array $result, string $filePath): void
    {
        if (!Config::get('file.logging.enabled', false)) {
            return;
        }
        
        $channel = Config::get('file.logging.log_channel', 'daily');
        $level = Config::get('file.logging.log_level', 'info');
        
        if ($result['success']) {
            Log::channel($channel)->log($level, "[FileService] Text extraction successful", [
                'file_path' => $filePath,
                'text_length' => strlen($result['text']),
                'message' => $result['message'],
            ]);
        } else {
            Log::channel($channel)->warning("[FileService] Text extraction failed", [
                'file_path' => $filePath,
                'message' => $result['message'],
            ]);
        }
    }
    
    /**
     * Log file upload completion
     */
    public function logUploadCompletion(array $result, array $fileInfo): void
    {
        if (!Config::get('file.logging.enabled', false)) {
            return;
        }
        
        $channel = Config::get('file.logging.log_channel', 'daily');
        
        if ($result['success']) {
            $level = Config::get('file.logging.log_level', 'info');
            Log::channel($channel)->log($level, "[FileService] File upload completed successfully", [
                'original_name' => $fileInfo['original_name'],
                'stored_path' => $result['file_path'],
                'file_size' => $fileInfo['size'],
                'file_type' => $fileInfo['type_category'],
                'processing_time_ms' => $result['processing_time_ms'] ?? null,
            ]);
        } else {
            Log::channel($channel)->error("[FileService] File upload failed", [
                'original_name' => $fileInfo['original_name'],
                'file_size' => $fileInfo['size'],
                'errors' => $result['errors'],
            ]);
        }
    }
    
    /**
     * Log file deletion
     */
    public function logFileDeletion(string $filePath, bool $success): void
    {
        if (!Config::get('file.logging.enabled', false)) {
            return;
        }
        
        $channel = Config::get('file.logging.log_channel', 'daily');
        
        if ($success) {
            $level = Config::get('file.logging.log_level', 'info');
            Log::channel($channel)->log($level, "[FileService] File deleted successfully", [
                'file_path' => $filePath,
            ]);
        } else {
            Log::channel($channel)->warning("[FileService] File deletion failed", [
                'file_path' => $filePath,
            ]);
        }
    }
    
    /**
     * Log metadata removal
     */
    public function logMetadataRemoval(string $filePath, bool $success): void
    {
        if (!Config::get('file.logging.log_security_events', false)) {
            return;
        }
        
        $channel = Config::get('file.logging.log_channel', 'daily');
        
        if ($success) {
            $level = Config::get('file.logging.log_level', 'info');
            Log::channel($channel)->log($level, "[FileService] Metadata removed successfully", [
                'file_path' => $filePath,
            ]);
        } else {
            Log::channel($channel)->warning("[FileService] Metadata removal failed", [
                'file_path' => $filePath,
            ]);
        }
    }
    
    /**
     * Log content analysis
     */
    public function logContentAnalysis(string $filePath, array $analysisResult): void
    {
        if (!Config::get('file.logging.enabled', false)) {
            return;
        }
        
        $channel = Config::get('file.logging.log_channel', 'daily');
        $level = Config::get('file.logging.log_level', 'info');
        
        Log::channel($channel)->log($level, "[FileService] Content analysis completed", [
            'file_path' => $filePath,
            'analysis_result' => $analysisResult,
        ]);
    }
}
