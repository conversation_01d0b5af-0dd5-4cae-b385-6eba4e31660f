<?php

namespace App\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * @method static array processUploadedFile(\Illuminate\Http\UploadedFile $file, array $options = [])
 * @method static array quickUpload(\Illuminate\Http\UploadedFile $file, string $subdirectory = '')
 * @method static array secureUpload(\Illuminate\Http\UploadedFile $file, string $subdirectory = '')
 * @method static array validateFile(\Illuminate\Http\UploadedFile $file)
 * @method static string sanitizeFilename(string $filename)
 * @method static array scanForViruses(string $filePath)
 * @method static bool removeMetadata(string $filePath)
 * @method static array processArchive(string $filePath)
 * @method static array extractTextContent(string $filePath)
 * @method static string getFileUrl(string $filePath)
 * @method static bool deleteFile(string $filePath)
 * @method static array getFileInfo(string $filePath)
 * @method static void logProcessing(string $message, array $context = [])
 * @method static void logError(string $message, array $context = [])
 * @method static void logSecurityEvent(string $message, array $context = [])
 *
 * @see \App\Services\FileService
 */
class FileService extends Facade
{
    /**
     * Get the registered name of the component.
     */
    protected static function getFacadeAccessor(): string
    {
        return \App\Services\FileService::class;
    }
}
