1. a general service overview page that links the dedicated service pages for each. 
So we have a landing page (/) that reflects all the pages including the general services page and the various services page by links, then on the general service page we have the services briefly described with links to the dedicated service pages. use beautifull cards with animations and hover effects to showcase services

2. yes. a projects page with project details page. admin can add the projects. use beautifull cards with animations and hover effects to showcase projects

3. both about us and team pages. the teams page is only referenced from the about us page and footer. when no team member is added, the page is not accessible or links to the team page visible.

4. contact form, live chat, phone, email. admin can set (toggle) live chat to active or inactive. 

This system has users with different roles and this determines what they can access or change.  we will have admin users, staffs, customers and clients. customers who buy products and own projects with us, clients own projects with us. staffs and admins can also shop on the site with their accounts. 
The public pages are highly seo optimized and localized. 

5. Unlimited number of products with variety of categories. Seo optimized products and categories, categories have images. Categories can have children. Filters and search and sort to make finding products easy. 

6. Stripe, PayPal, local payment methods

7. Local shipping calculations only (South Africa), nationwide. Can use local delivery services or delivered by us. costs vary and can be set by admin. using google maps, charge by distance.

8. Yes. comprehensive inventory management system with alerts, stock tracking.

9. english, french, spanish to start with. Seo based url structures for the languages. like domain.com/en/, domain.com/fr/

10. ZAR, RWF, USD, EURO, Pounds. Base curency is ZAR, no external APIs needed for conversion, admin sets exchange rates. can also change the base currency(is_default), can add other currencies with the conversion rates.

All users except admins soft delete items (is_deleted) including their accounts. once an item is deleted (is_deleted = true), it is treated as not found (404), we never tell a user the item is deleted, always not found or does not exist.

Code should adhere to OOP norms, re-useable, there should be uniformity in the design and functionality. 

In the planning, I want project management (trello style), project overview, sprint planning, erd and other db designs, user stories, emphaty map, UI/UX, wireframes. 

The dashboards have a side nav and main content area. Clients and customers have the same dashboard and admin and staffs have the same dashboard but your role determines what you see. 
the auth page has tabs (login - default or active tab, sign up tab). default role signing up is customer, only admin can assign role. centered auth forms with animations to switch tabs. 

11. index the codebase for the template. it is included in the project now. 

12. product management, order management, user management, analytics, content management and more. Use KPIs with a global search for the admin, staffs, clients and customers to search their dashbaords. only results pertaining to them are returned. 

13. yes . users can register when checking out or checkout without an account, providing the address and contact information details. 

14. yes. SEO is key in this project, advanced SEO features, blog/news section for content marketing, and more. SEO is one of the services we offer, our system has to reflect this being ranking top. So, yes, advance seo implementation is key.

15. shared cpanel hosting. deployment script has to be created. it already has a github repository with just the laravel template. Include documentation for other developers to adhere to for security, design patterns, errors faced and how they were fixed and avoid them.
coding patterns. use modern industry standards. 

Do not use PWFs.

16. Google Analytics, Facebook Pixel, Google search console, and others to boost the seo of this app.

https://unsplash.com/photos/a-close-up-of-a-sign-with-the-word-hello-hello-jxelyjTrWFg

