<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class ShoppingCart extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'user_id',
        'session_id',
        'subtotal',
        'tax_amount',
        'shipping_amount',
        'discount_amount',
        'total',
        'currency',
        'coupon_id',
        'coupon_code',
        'expires_at',
    ];

    protected $casts = [
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'shipping_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total' => 'decimal:2',
        'expires_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($cart) {
            if (empty($cart->uuid)) {
                $cart->uuid = Str::uuid();
            }
            if (empty($cart->currency)) {
                $cart->currency = 'ZAR';
            }
            if (empty($cart->expires_at)) {
                $cart->expires_at = now()->addDays(30);
            }
        });
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'uuid';
    }

    /**
     * Scope a query to only include active carts (not expired).
     */
    public function scopeActive($query)
    {
        return $query->where('expires_at', '>', now());
    }

    /**
     * Get the user that owns the cart.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the cart items.
     */
    public function items(): HasMany
    {
        return $this->hasMany(CartItem::class, 'cart_id');
    }

    /**
     * Get the applied coupon.
     */
    public function coupon(): BelongsTo
    {
        return $this->belongsTo(Coupon::class);
    }

    /**
     * Add a product to the cart.
     */
    public function addProduct(Product $product, int $quantity = 1, ?ProductVariant $variant = null): CartItem
    {
        $price = $variant ? $variant->price : $product->price;
        
        // Check if item already exists in cart
        $existingItem = $this->items()
            ->where('product_id', $product->id)
            ->where('product_variant_id', $variant?->id)
            ->first();

        if ($existingItem) {
            $existingItem->quantity += $quantity;
            $existingItem->total = $existingItem->price * $existingItem->quantity;
            $existingItem->save();
            
            $this->recalculateTotal();
            return $existingItem;
        }

        $cartItem = $this->items()->create([
            'product_id' => $product->id,
            'product_variant_id' => $variant?->id,
            'quantity' => $quantity,
            'price' => $price,
            'total' => $price * $quantity,
        ]);

        $this->recalculateTotal();
        return $cartItem;
    }

    /**
     * Remove a product from the cart.
     */
    public function removeProduct(Product $product, ?ProductVariant $variant = null): bool
    {
        $item = $this->items()
            ->where('product_id', $product->id)
            ->where('product_variant_id', $variant?->id)
            ->first();

        if ($item) {
            $item->delete();
            $this->recalculateTotal();
            return true;
        }

        return false;
    }

    /**
     * Update product quantity in cart.
     */
    public function updateProductQuantity(Product $product, int $quantity, ?ProductVariant $variant = null): bool
    {
        $item = $this->items()
            ->where('product_id', $product->id)
            ->where('product_variant_id', $variant?->id)
            ->first();

        if ($item) {
            if ($quantity <= 0) {
                $item->delete();
            } else {
                $item->quantity = $quantity;
                $item->total = $item->price * $quantity;
                $item->save();
            }
            
            $this->recalculateTotal();
            return true;
        }

        return false;
    }

    /**
     * Clear all items from the cart.
     */
    public function clear(): void
    {
        $this->items()->delete();
        $this->recalculateTotal();
    }

    /**
     * Recalculate cart totals.
     */
    public function recalculateTotal(): void
    {
        $this->load('items');
        
        $this->subtotal = $this->items->sum('total');
        $this->tax_amount = $this->calculateTax();
        $this->total = $this->subtotal + $this->tax_amount + $this->shipping_amount - $this->discount_amount;
        
        $this->save();
    }

    /**
     * Calculate tax amount.
     */
    protected function calculateTax(): float
    {
        // South Africa VAT is 15%
        $taxRate = 0.15;
        return round($this->subtotal * $taxRate, 2);
    }

    /**
     * Get the total number of items in the cart.
     */
    public function getItemCountAttribute(): int
    {
        return $this->items->sum('quantity');
    }

    /**
     * Get formatted subtotal.
     */
    public function getFormattedSubtotalAttribute(): string
    {
        return 'R' . number_format($this->subtotal, 2);
    }

    /**
     * Get formatted tax amount.
     */
    public function getFormattedTaxAmountAttribute(): string
    {
        return 'R' . number_format($this->tax_amount, 2);
    }

    /**
     * Get formatted shipping amount.
     */
    public function getFormattedShippingAmountAttribute(): string
    {
        return 'R' . number_format($this->shipping_amount, 2);
    }

    /**
     * Get formatted discount amount.
     */
    public function getFormattedDiscountAmountAttribute(): string
    {
        return 'R' . number_format($this->discount_amount, 2);
    }

    /**
     * Get formatted total.
     */
    public function getFormattedTotalAttribute(): string
    {
        return 'R' . number_format($this->total, 2);
    }

    /**
     * Check if cart is empty.
     */
    public function isEmpty(): bool
    {
        return $this->items->isEmpty();
    }

    /**
     * Check if cart has expired.
     */
    public function hasExpired(): bool
    {
        return $this->expires_at < now();
    }

    /**
     * Extend cart expiration.
     */
    public function extendExpiration(int $days = 30): void
    {
        $this->expires_at = now()->addDays($days);
        $this->save();
    }

    /**
     * Check if all items are available.
     */
    public function allItemsAvailable(): bool
    {
        return $this->items->every(function ($item) {
            return $item->isAvailable();
        });
    }

    /**
     * Get unavailable items.
     */
    public function getUnavailableItems()
    {
        return $this->items->filter(function ($item) {
            return !$item->isAvailable();
        });
    }

    /**
     * Update all item prices to current prices.
     */
    public function updatePrices(): void
    {
        $this->items->each(function ($item) {
            $item->updatePrice();
        });
        
        $this->recalculateTotal();
    }
}
