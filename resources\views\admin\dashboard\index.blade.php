@extends('layouts.dashboard')

@section('title', __('Admin Dashboard') . ' - ' . __('common.company_name'))
@section('page_title', __('Admin Dashboard'))

@section('content')
<div class="p-6 space-y-6">
    <!-- Welcome Section -->
    <div class="bg-gradient-to-r from-purple-600 to-purple-700 rounded-lg p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-2xl font-bold mb-2">{{ __('Welcome to Admin Dashboard, :name', ['name' => auth()->user()->first_name]) }}</h2>
                <p class="text-purple-100">{{ __('Manage the system and monitor performance') }}</p>
            </div>
            <div class="hidden md:block">
                <svg class="w-16 h-16 text-purple-200" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8z" clip-rule="evenodd"></path>
                </svg>
            </div>
        </div>
    </div>

    <!-- Admin Stats Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Users -->
        <div class="dashboard-card">
            <div class="dashboard-stat">
                <div class="dashboard-stat-content">
                    <h3>{{ $stats['total_users'] ?? 0 }}</h3>
                    <p>{{ __('Total Users') }}</p>
                </div>
                <div class="dashboard-stat-icon bg-blue-100 text-blue-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Total Orders -->
        <div class="dashboard-card">
            <div class="dashboard-stat">
                <div class="dashboard-stat-content">
                    <h3>{{ $stats['total_orders'] ?? 0 }}</h3>
                    <p>{{ __('Total Orders') }}</p>
                </div>
                <div class="dashboard-stat-icon bg-green-100 text-green-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Total Products -->
        <div class="dashboard-card">
            <div class="dashboard-stat">
                <div class="dashboard-stat-content">
                    <h3>{{ $stats['total_products'] ?? 0 }}</h3>
                    <p>{{ __('Total Products') }}</p>
                </div>
                <div class="dashboard-stat-icon bg-yellow-100 text-yellow-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- System Status -->
        <div class="dashboard-card">
            <div class="dashboard-stat">
                <div class="dashboard-stat-content">
                    <h3>{{ __('Online') }}</h3>
                    <p>{{ __('System Status') }}</p>
                </div>
                <div class="dashboard-stat-icon bg-green-100 text-green-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Recent Activity -->
        <div class="dashboard-card">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">{{ __('Recent Activity') }}</h3>
                <a href="#" class="text-blue-600 hover:text-blue-700 text-sm font-medium">{{ __('View All') }}</a>
            </div>
            
            <div class="space-y-3">
                <div class="activity-item">
                    <div class="activity-icon bg-blue-100 text-blue-600">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                    </div>
                    <div class="activity-content flex-1">
                        <h4>{{ __('New user registered') }}</h4>
                        <p>{{ __('2 minutes ago') }}</p>
                    </div>
                </div>
                
                <div class="activity-item">
                    <div class="activity-icon bg-green-100 text-green-600">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                        </svg>
                    </div>
                    <div class="activity-content flex-1">
                        <h4>{{ __('New order received') }}</h4>
                        <p>{{ __('5 minutes ago') }}</p>
                    </div>
                </div>
                
                <div class="activity-item">
                    <div class="activity-icon bg-yellow-100 text-yellow-600">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                    </div>
                    <div class="activity-content flex-1">
                        <h4>{{ __('Product updated') }}</h4>
                        <p>{{ __('10 minutes ago') }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="dashboard-card">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">{{ __('Quick Actions') }}</h3>
            
            <div class="grid grid-cols-2 gap-4">
                <a href="#" class="quick-action">
                    <div class="quick-action-icon bg-blue-100 text-blue-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                    </div>
                    <h3>{{ __('Manage Users') }}</h3>
                    <p>{{ __('Add, edit, or remove users') }}</p>
                </a>

                <a href="{{ route('admin.products.index') }}" class="quick-action">
                    <div class="quick-action-icon bg-green-100 text-green-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                    </div>
                    <h3>{{ __('Manage Products') }}</h3>
                    <p>{{ __('Add or update products') }}</p>
                </a>

                <a href="#" class="quick-action">
                    <div class="quick-action-icon bg-purple-100 text-purple-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                        </svg>
                    </div>
                    <h3>{{ __('View Orders') }}</h3>
                    <p>{{ __('Process and manage orders') }}</p>
                </a>

                <a href="#" class="quick-action">
                    <div class="quick-action-icon bg-yellow-100 text-yellow-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                    </div>
                    <h3>{{ __('Settings') }}</h3>
                    <p>{{ __('Configure system settings') }}</p>
                </a>
            </div>
        </div>
    </div>
</div>
@endsection
