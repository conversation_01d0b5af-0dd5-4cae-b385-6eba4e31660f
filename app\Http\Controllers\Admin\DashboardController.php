<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Order;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\ActivityLog;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use App\Services\DashboardCacheService;

class DashboardController extends Controller
{
    protected DashboardCacheService $cacheService;

    /**
     * Create a new controller instance.
     */
    public function __construct(DashboardCacheService $cacheService)
    {
        $this->middleware('auth');
        $this->middleware('role:admin,staff');
        $this->cacheService = $cacheService;
    }

    /**
     * Show the admin dashboard.
     */
    public function index(): View
    {
        // Use cache service for optimized data retrieval
        $stats = $this->cacheService->getAdminStats();
        $recent_orders = $this->cacheService->getRecentOrders();
        $low_stock_products = $this->cacheService->getLowStockProducts();
        $recent_activities = $this->cacheService->getRecentActivities();

        return view('admin.dashboard.index', compact('stats', 'recent_orders', 'low_stock_products', 'recent_activities'));
    }
}
