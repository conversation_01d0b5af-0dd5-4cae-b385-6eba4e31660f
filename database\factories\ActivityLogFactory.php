<?php

namespace Database\Factories;

use App\Models\ActivityLog;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ActivityLog>
 */
class ActivityLogFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = ActivityLog::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $activityTypes = [
            'password_reset_request',
            'password_reset_success',
            'password_reset_failed',
        ];

        $statuses = ['success', 'failed', 'blocked', 'pending'];
        $browsers = ['Chrome 120.0', 'Firefox 121.0', 'Safari 17.2', 'Edge 120.0'];
        $platforms = ['Windows 11', 'macOS 14.2', 'Ubuntu 22.04', 'iOS 17.2', 'Android 14'];
        $deviceTypes = ['desktop', 'mobile', 'tablet'];

        $activityType = fake()->randomElement($activityTypes);
        $status = fake()->randomElement($statuses);
        $riskScore = fake()->numberBetween(0, 100);

        return [
            'uuid' => Str::uuid(),
            'user_id' => fake()->optional(0.8)->randomElement(User::pluck('id')->toArray()),
            'user_email' => fake()->safeEmail(),
            'user_name' => fake()->name(),
            'activity_type' => $activityType,
            'activity_description' => $this->getActivityDescription($activityType, $status),
            'status' => $status,
            'failure_reason' => $status === 'failed' ? fake()->randomElement([
                'Invalid token',
                'Expired token',
                'Rate limited',
                'Invalid email',
                'Validation failed'
            ]) : null,
            'ip_address' => fake()->ipv4(),
            'user_agent' => fake()->userAgent(),
            'device_type' => fake()->randomElement($deviceTypes),
            'browser' => fake()->randomElement($browsers),
            'platform' => fake()->randomElement($platforms),
            'country' => fake()->country(),
            'region' => fake()->state(),
            'city' => fake()->city(),
            'latitude' => fake()->latitude(),
            'longitude' => fake()->longitude(),
            'url' => fake()->url(),
            'method' => fake()->randomElement(['GET', 'POST', 'PUT', 'DELETE']),
            'request_data' => [
                'email' => fake()->safeEmail(),
                'form_data' => [
                    'email' => fake()->safeEmail(),
                    'password' => '***REDACTED***',
                    'token' => '***REDACTED***',
                ],
                'email_exists' => fake()->boolean(),
                'was_rate_limited' => fake()->boolean(0.1),
            ],
            'response_data' => [
                'public_status' => 'success',
                'actual_email_exists' => fake()->boolean(),
                'email_sent' => fake()->boolean(),
            ],
            'session_id' => Str::random(40),
            'request_id' => Str::uuid(),
            'is_suspicious' => $riskScore >= 60,
            'security_notes' => fake()->optional(0.3)->sentence(),
            'risk_score' => $riskScore,
            'occurred_at' => fake()->dateTimeBetween('-30 days', 'now'),
        ];
    }

    /**
     * Get activity description based on type and status.
     */
    protected function getActivityDescription(string $activityType, string $status): string
    {
        return match ($activityType) {
            'password_reset_request' => $status === 'success' 
                ? 'Password reset link requested'
                : 'Failed password reset request',
            'password_reset_success' => 'Password successfully reset using valid token',
            'password_reset_failed' => 'Password reset failed: invalid or expired token',
            default => 'Unknown activity',
        };
    }

    /**
     * Create a suspicious activity log.
     */
    public function suspicious(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_suspicious' => true,
            'risk_score' => fake()->numberBetween(60, 100),
            'security_notes' => 'Flagged as suspicious due to high risk score',
            'status' => 'failed',
            'failure_reason' => fake()->randomElement([
                'Multiple failed attempts',
                'Suspicious IP address',
                'Bot-like behavior detected',
                'Unusual device fingerprint'
            ]),
        ]);
    }

    /**
     * Create a password reset request activity log.
     */
    public function passwordResetRequest(): static
    {
        return $this->state(fn (array $attributes) => [
            'activity_type' => 'password_reset_request',
            'activity_description' => 'Password reset link requested',
            'status' => 'success',
            'request_data' => [
                'email' => fake()->safeEmail(),
                'form_data' => [
                    'email' => fake()->safeEmail(),
                ],
                'email_exists' => fake()->boolean(),
                'was_rate_limited' => false,
            ],
            'response_data' => [
                'public_status' => 'success',
                'actual_email_exists' => fake()->boolean(),
                'email_sent' => fake()->boolean(),
            ],
        ]);
    }

    /**
     * Create a successful password reset activity log.
     */
    public function passwordResetSuccess(): static
    {
        return $this->state(fn (array $attributes) => [
            'activity_type' => 'password_reset_success',
            'activity_description' => 'Password successfully reset using valid token',
            'status' => 'success',
            'risk_score' => fake()->numberBetween(0, 30),
            'is_suspicious' => false,
            'request_data' => [
                'token_used' => substr(Str::random(64), 0, 8) . '...',
                'form_data' => [
                    'email' => fake()->safeEmail(),
                    'password' => '***REDACTED***',
                    'password_confirmation' => '***REDACTED***',
                    'token' => '***REDACTED***',
                ],
            ],
            'response_data' => [
                'password_changed' => true,
                'sessions_cleared' => true,
                'remember_tokens_cleared' => true,
            ],
        ]);
    }

    /**
     * Create a failed password reset activity log.
     */
    public function passwordResetFailed(): static
    {
        return $this->state(fn (array $attributes) => [
            'activity_type' => 'password_reset_failed',
            'activity_description' => 'Password reset failed: invalid or expired token',
            'status' => 'failed',
            'risk_score' => fake()->numberBetween(30, 80),
            'failure_reason' => fake()->randomElement([
                'Invalid or expired token',
                'Invalid email address',
                'Validation failed',
                'Token already used'
            ]),
            'request_data' => [
                'token_provided' => substr(Str::random(64), 0, 8) . '...',
                'failure_type' => 'Invalid token',
                'form_data' => [
                    'email' => fake()->safeEmail(),
                    'password' => '***REDACTED***',
                    'password_confirmation' => '***REDACTED***',
                    'token' => '***REDACTED***',
                ],
            ],
        ]);
    }

    /**
     * Create a high-risk activity log.
     */
    public function highRisk(): static
    {
        return $this->state(fn (array $attributes) => [
            'risk_score' => fake()->numberBetween(80, 100),
            'is_suspicious' => true,
            'status' => 'failed',
            'security_notes' => 'High-risk activity detected',
        ]);
    }

    /**
     * Create a low-risk activity log.
     */
    public function lowRisk(): static
    {
        return $this->state(fn (array $attributes) => [
            'risk_score' => fake()->numberBetween(0, 20),
            'is_suspicious' => false,
            'status' => 'success',
        ]);
    }
}
