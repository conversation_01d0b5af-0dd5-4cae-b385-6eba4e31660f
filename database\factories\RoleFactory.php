<?php

namespace Database\Factories;

use App\Models\Role;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Role>
 */
class RoleFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Role::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $roles = ['admin', 'staff', 'customer', 'client'];
        $role = $this->faker->randomElement($roles);
        
        return [
            'name' => $role,
            'slug' => $role,
            'description' => ucfirst($role) . ' role with appropriate permissions',
            'permissions' => $this->getPermissionsForRole($role),
            'is_active' => true,
        ];
    }

    /**
     * Get permissions for a specific role.
     */
    private function getPermissionsForRole(string $role): array
    {
        switch ($role) {
            case 'admin':
                return [
                    'users' => ['create', 'read', 'update', 'delete'],
                    'roles' => ['create', 'read', 'update', 'delete'],
                    'projects' => ['create', 'read', 'update', 'delete'],
                    'orders' => ['create', 'read', 'update', 'delete'],
                    'services' => ['create', 'read', 'update', 'delete'],
                    'activity_logs' => ['read', 'delete'],
                    'dashboard' => ['access'],
                ];
            case 'staff':
                return [
                    'projects' => ['read', 'update'],
                    'orders' => ['read', 'update'],
                    'services' => ['read'],
                    'activity_logs' => ['read'],
                    'dashboard' => ['access'],
                ];
            case 'customer':
                return [
                    'profile' => ['read', 'update'],
                    'projects' => ['create', 'read'],
                    'orders' => ['create', 'read'],
                    'dashboard' => ['access'],
                ];
            case 'client':
                return [
                    'profile' => ['read', 'update'],
                    'projects' => ['create', 'read'],
                    'orders' => ['create', 'read'],
                    'dashboard' => ['access'],
                ];
            default:
                return [];
        }
    }

    /**
     * Create an admin role.
     */
    public function admin(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'admin',
            'slug' => 'admin',
            'description' => 'Administrator role with full permissions',
            'permissions' => $this->getPermissionsForRole('admin'),
        ]);
    }

    /**
     * Create a staff role.
     */
    public function staff(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'staff',
            'slug' => 'staff',
            'description' => 'Staff role with limited permissions',
            'permissions' => $this->getPermissionsForRole('staff'),
        ]);
    }

    /**
     * Create a customer role.
     */
    public function customer(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'customer',
            'slug' => 'customer',
            'description' => 'Customer role with basic permissions',
            'permissions' => $this->getPermissionsForRole('customer'),
        ]);
    }

    /**
     * Create a client role.
     */
    public function client(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'client',
            'slug' => 'client',
            'description' => 'Client role with basic permissions',
            'permissions' => $this->getPermissionsForRole('client'),
        ]);
    }

    /**
     * Create an inactive role.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }
}
