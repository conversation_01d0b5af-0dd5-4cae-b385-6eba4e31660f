<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\View\View;
use Illuminate\Validation\ValidationException;

class LoginController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
        $this->middleware('auth')->only('logout');
    }

    /**
     * Show the application's login form.
     */
    public function showLoginForm(): View
    {
        return view('auth.login');
    }

    /**
     * Handle a login request to the application.
     */
    public function login(LoginRequest $request): JsonResponse|RedirectResponse
    {
        $this->checkTooManyFailedAttempts($request);

        if ($this->attemptLogin($request)) {
            $request->session()->regenerate();
            $this->clearLoginAttempts($request);

            // Update last login timestamp
            $request->user()->update([
                'last_login_at' => now()
            ]);

            return $this->sendLoginResponse($request);
        }

        $this->incrementLoginAttempts($request);

        return $this->sendFailedLoginResponse($request);
    }

    /**
     * Attempt to log the user into the application.
     */
    protected function attemptLogin(LoginRequest $request): bool
    {
        return Auth::attempt(
            $this->credentials($request),
            $request->boolean('remember')
        );
    }

    /**
     * Get the needed authorization credentials from the request.
     */
    protected function credentials(LoginRequest $request): array
    {
        $credentials = $request->only('email', 'password');
        
        // Add additional constraints for active users
        $credentials['is_active'] = true;
        $credentials['is_deleted'] = false;
        
        return $credentials;
    }

    /**
     * Send the response after the user was authenticated.
     */
    protected function sendLoginResponse(Request $request): JsonResponse|RedirectResponse
    {
        // Get the intended URL or determine appropriate dashboard
        $intended = $request->session()->pull('url.intended');
        $redirectPath = $intended ?: $this->getRedirectPathByRole($request->user());

        // Handle AJAX requests
        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => __('auth.login_success'),
                'redirect' => $redirectPath,
                'user' => [
                    'id' => $request->user()->id,
                    'name' => $request->user()->full_name,
                    'email' => $request->user()->email,
                    'role' => $request->user()->role->name,
                ]
            ]);
        }

        // Handle regular form submissions
        return redirect()->to($redirectPath)->with('success', __('auth.login_success'));
    }

    /**
     * Get the appropriate redirect path based on user role.
     */
    protected function getRedirectPathByRole($user): string
    {
        $role = $user->role->name;

        switch ($role) {
            case 'admin':
            case 'staff':
                return route('admin.dashboard');
            case 'client':
            case 'customer':
            default:
                return route('dashboard');
        }
    }

    /**
     * Get the failed login response instance.
     */
    protected function sendFailedLoginResponse(Request $request): JsonResponse|RedirectResponse
    {
        // Handle AJAX requests
        if ($request->expectsJson()) {
            return response()->json([
                'success' => false,
                'message' => __('auth.failed'),
                'errors' => [
                    'email' => [__('auth.failed')]
                ]
            ], 422);
        }

        // Handle regular form submissions
        throw ValidationException::withMessages([
            'email' => [__('auth.failed')],
        ]);
    }

    /**
     * Determine if the user has too many failed login attempts.
     */
    protected function hasTooManyLoginAttempts(Request $request): bool
    {
        return RateLimiter::tooManyAttempts(
            $this->throttleKey($request), 5
        );
    }

    /**
     * Increment the login attempts for the user.
     */
    protected function incrementLoginAttempts(Request $request): void
    {
        RateLimiter::hit(
            $this->throttleKey($request), 300 // 5 minutes
        );
    }

    /**
     * Clear the login locks for the given user credentials.
     */
    protected function clearLoginAttempts(Request $request): void
    {
        RateLimiter::clear($this->throttleKey($request));
    }

    /**
     * Check if the user has too many failed login attempts.
     */
    protected function checkTooManyFailedAttempts(Request $request): void
    {
        if ($this->hasTooManyLoginAttempts($request)) {
            $seconds = RateLimiter::availableIn($this->throttleKey($request));

            // Handle AJAX requests
            if ($request->expectsJson()) {
                $response = response()->json([
                    'success' => false,
                    'message' => __('auth.throttle', ['seconds' => $seconds]),
                    'errors' => [
                        'email' => [__('auth.throttle', ['seconds' => $seconds])]
                    ]
                ], 429);

                throw new \Illuminate\Http\Exceptions\HttpResponseException($response);
            }

            // Handle regular form submissions
            throw ValidationException::withMessages([
                'email' => [__('auth.throttle', ['seconds' => $seconds])],
            ]);
        }
    }

    /**
     * Get the throttle key for the given request.
     */
    protected function throttleKey(Request $request): string
    {
        return strtolower($request->input('email')).'|'.$request->ip();
    }

    /**
     * Where to redirect users after login.
     */
    protected function redirectPath(): string
    {
        $user = auth()->user();

        // Role-based redirection
        if ($user->isAdminOrStaff()) {
            return route('admin.dashboard');
        }

        // Customers and clients go to the same dashboard
        return route('dashboard');
    }

    /**
     * Log the user out of the application.
     */
    public function logout(Request $request): RedirectResponse
    {
        Auth::logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('home')->with('success', __('auth.logout_success'));
    }
}
