<p align="center"><a href="#" target="_blank"><img src="https://raw.githubusercontent.com/laravel/art/master/logo-lockup/5%20SVG/2%20CMYK/1%20Full%20Color/laravel-logolockup-cmyk-red.svg" width="400" alt="ChiSolution Logo"></a></p>

<p align="center">
<a href="#"><img src="https://img.shields.io/badge/Laravel-12.x-FF2D20?style=flat&logo=laravel" alt="Laravel Version"></a>
<a href="#"><img src="https://img.shields.io/badge/PHP-8.2+-777BB4?style=flat&logo=php" alt="PHP Version"></a>
<a href="#"><img src="https://img.shields.io/badge/Tailwind-4.x-38B2AC?style=flat&logo=tailwind-css" alt="Tailwind CSS"></a>
<a href="#"><img src="https://img.shields.io/badge/License-MIT-green.svg" alt="License"></a>
</p>

# ChiSolution - Digital Agency Platform

ChiSolution is a comprehensive digital agency platform built with Laravel 12, featuring integrated e-commerce capabilities, project management, career portal, and advanced security monitoring. Designed for modern digital agencies that provide web development services and sell technology products.

## ✨ Key Features

### 🏢 **Multi-Role System**
- **Admin Dashboard**: Complete system management and analytics
- **Staff Portal**: Role-based access for team members
- **Client Area**: Project tracking and service requests
- **Customer Portal**: E-commerce and order management

### 🚀 **Project Management**
- Comprehensive project application system
- Multi-step forms with file upload support
- Public and authenticated application flows
- Real-time status tracking and notifications
- Admin review and approval workflow

### 💼 **Career Portal**
- Dynamic job posting management
- Advanced job application system
- Resume and portfolio upload with security scanning
- Application tracking for candidates
- Comprehensive admin tools for HR management

### 🛒 **E-Commerce Platform**
- Product catalog with hierarchical categories
- Advanced shopping cart functionality
- Secure checkout with Stripe integration
- Order management and tracking
- Coupon and discount system
- Multi-currency support

### 🔒 **Enterprise Security**
- Comprehensive activity logging and monitoring
- Advanced file upload security with virus scanning
- Risk scoring and suspicious activity detection
- Device fingerprinting and geolocation tracking
- Rate limiting and enumeration attack protection

### 📁 **File Management**
- Global ImageService with optimization and WebP conversion
- Comprehensive FileService with security scanning
- Support for documents, images, and archives
- Metadata removal and content analysis
- Quarantine system for infected files

## 🛠️ **Technology Stack**

- **Framework**: Laravel 12.19.3
- **PHP**: 8.2+
- **Frontend**: Tailwind CSS 4.x (exclusively)
- **Database**: MySQL with comprehensive migrations
- **Payment**: Stripe integration
- **Image Processing**: Intervention Image with Spatie optimization
- **Security**: Laravel Sanctum with custom monitoring
- **Development**: Vite, Concurrently for optimal workflow

## 🏗️ **Architecture Highlights**

### **Service-Oriented Design**
- Modular service architecture with clear separation of concerns
- Comprehensive ImageService and FileService for media handling
- ActivityLogger for security monitoring and audit trails
- Reusable components and traits for maintainability

### **Security-First Approach**
- Multi-layered security validation
- Comprehensive activity logging with risk assessment
- Advanced file upload protection
- Real-time threat detection and response

### **Modern UI/UX**
- Responsive design with Tailwind CSS
- Floating label form system
- Consistent component library
- Mobile-first approach with desktop optimization

## 🚀 **Quick Start**

### Prerequisites
- PHP 8.2 or higher
- Composer
- Node.js & NPM
- MySQL database

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd chisolution
   ```

2. **Install dependencies**
   ```bash
   composer install
   npm install
   ```

3. **Environment setup**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

4. **Database setup**
   ```bash
   php artisan migrate
   php artisan db:seed
   ```

5. **Build assets**
   ```bash
   npm run build
   ```

6. **Start development server**
   ```bash
   composer run dev
   ```

This will start the Laravel server, queue worker, logs, and Vite development server concurrently.

## 📚 **Documentation**

Comprehensive documentation is available in the `/docs` directory:

- **ImageService**: Complete guide to image processing and optimization
- **FileService**: File upload and security documentation
- **API Reference**: RESTful API endpoints and usage
- **Security Guide**: Security features and best practices

## 🔧 **Configuration**

### **Image Processing**
Configure image optimization, WebP conversion, and security scanning in `config/image.php`.

### **File Security**
Customize file upload restrictions, virus scanning, and content analysis in `config/file.php`.

### **Activity Logging**
Security monitoring and activity tracking settings are managed through the ActivityLogger service.

## 🧪 **Testing**

Run the comprehensive test suite:

```bash
# Run all tests
php artisan test

# Run specific test suites
php artisan test --testsuite=Feature
php artisan test --testsuite=Unit

# Run with coverage
php artisan test --coverage
```

## 🤝 **Contributing**

We welcome contributions to ChiSolution! Please read our contributing guidelines and ensure all tests pass before submitting pull requests.

## 🔒 **Security**

Security is a top priority for ChiSolution. If you discover any security vulnerabilities, please report them responsibly by contacting our security team.

## 📄 **More Activity Loggin**

Activity Logging: All email sends logged with details
Error Tracking: Failed emails logged with error details
Performance Metrics: Queue processing statistics
