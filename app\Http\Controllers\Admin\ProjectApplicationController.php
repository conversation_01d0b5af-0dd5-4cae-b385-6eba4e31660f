<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ProjectApplication;
use App\Models\Service;
use App\Models\User;
use App\Services\ActivityLogger;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class ProjectApplicationController extends Controller
{
    protected ActivityLogger $activityLogger;

    /**
     * Create a new controller instance.
     */
    public function __construct(ActivityLogger $activityLogger)
    {
        $this->middleware(['auth', 'role:admin,staff']);
        $this->activityLogger = $activityLogger;
    }

    /**
     * Display a listing of project applications.
     */
    public function index(Request $request): View
    {
        $query = ProjectApplication::with(['user', 'service', 'reviewedBy', 'project'])
            ->latest();

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        if ($request->filled('service_id')) {
            $query->where('service_id', $request->service_id);
        }

        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('first_name', 'like', "%{$search}%")
                               ->orWhere('last_name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $applications = $query->paginate(20)->withQueryString();

        // Get filter options
        $services = Service::active()->ordered()->get();
        $users = User::whereHas('projectApplications')->get();
        $statuses = ProjectApplication::getStatusOptions();
        $priorities = ProjectApplication::getPriorityOptions();

        // Log admin access
        $this->activityLogger->logCustomerActivity(
            'admin_project_applications_list',
            'Admin accessed project applications list',
            'success',
            null,
            [
                'filters_applied' => $request->only(['status', 'priority', 'service_id', 'user_id', 'search', 'date_from', 'date_to']),
                'total_results' => $applications->total(),
                'admin_user' => Auth::user()->email,
            ]
        );

        return view('admin.project-applications.index', compact(
            'applications', 
            'services', 
            'users', 
            'statuses', 
            'priorities'
        ));
    }

    /**
     * Display the specified project application.
     */
    public function show(ProjectApplication $projectApplication): View
    {
        $projectApplication->load(['user', 'service', 'reviewedBy', 'project']);

        // Log admin view
        $this->activityLogger->logCustomerActivity(
            'admin_project_application_view',
            "Admin viewed project application: {$projectApplication->title} (ID: {$projectApplication->id})",
            'success',
            null,
            [
                'application_id' => $projectApplication->id,
                'application_status' => $projectApplication->status,
                'application_user' => $projectApplication->user->email,
                'admin_user' => Auth::user()->email,
            ]
        );

        return view('admin.project-applications.show', compact('projectApplication'));
    }

    /**
     * Show the form for editing the specified project application.
     */
    public function edit(ProjectApplication $projectApplication): View
    {
        $projectApplication->load(['user', 'service']);
        $services = Service::active()->ordered()->get();
        $statuses = ProjectApplication::getStatusOptions();
        $priorities = ProjectApplication::getPriorityOptions();

        return view('admin.project-applications.edit', compact(
            'projectApplication', 
            'services', 
            'statuses', 
            'priorities'
        ));
    }

    /**
     * Update the specified project application.
     */
    public function update(Request $request, ProjectApplication $projectApplication): RedirectResponse
    {
        $originalData = $projectApplication->toArray();
        
        $validated = $request->validate([
            'service_id' => 'nullable|exists:services,id',
            'title' => 'required|string|max:255',
            'description' => 'required|string|max:10000',
            'requirements' => 'nullable|string|max:10000',
            'budget_range' => 'nullable|string|max:100',
            'timeline' => 'nullable|string|max:100',
            'priority' => 'required|in:low,medium,high,urgent',
            'status' => 'required|in:pending,under_review,approved,rejected,in_progress,completed,cancelled',
            'admin_notes' => 'nullable|string|max:5000',
            'estimated_cost' => 'nullable|numeric|min:0|max:999999.99',
            'estimated_hours' => 'nullable|integer|min:0|max:10000',
        ]);

        // Track status change
        $statusChanged = $originalData['status'] !== $validated['status'];
        $oldStatus = $originalData['status'];

        // Update reviewed information if status changed
        if ($statusChanged) {
            $validated['reviewed_by'] = Auth::id();
            $validated['reviewed_at'] = now();
        }

        $projectApplication->update($validated);

        // Log the update
        $changes = array_diff_assoc($validated, $originalData);
        $this->activityLogger->logCustomerActivity(
            'admin_project_application_update',
            "Admin updated project application: {$projectApplication->title} (ID: {$projectApplication->id})",
            'success',
            null,
            [
                'application_id' => $projectApplication->id,
                'changes_made' => array_keys($changes),
                'status_changed' => $statusChanged,
                'old_status' => $oldStatus,
                'new_status' => $validated['status'],
                'admin_user' => Auth::user()->email,
                'application_user' => $projectApplication->user->email,
            ]
        );

        $message = 'Project application updated successfully.';
        if ($statusChanged) {
            $message .= " Status changed from '{$oldStatus}' to '{$validated['status']}'.";
        }

        return redirect()->route('admin.project-applications.show', $projectApplication)
                        ->with('success', $message);
    }

    /**
     * Remove the specified project application.
     */
    public function destroy(ProjectApplication $projectApplication): RedirectResponse
    {
        $applicationData = [
            'id' => $projectApplication->id,
            'title' => $projectApplication->title,
            'user_email' => $projectApplication->user->email,
            'status' => $projectApplication->status,
        ];

        // Delete associated files
        if (!empty($projectApplication->attachments)) {
            foreach ($projectApplication->attachments as $attachment) {
                if (isset($attachment['file_path']) && Storage::disk('public')->exists($attachment['file_path'])) {
                    Storage::disk('public')->delete($attachment['file_path']);
                }
            }
        }

        $projectApplication->delete();

        // Log the deletion
        $this->activityLogger->logCustomerActivity(
            'admin_project_application_delete',
            "Admin deleted project application: {$applicationData['title']} (ID: {$applicationData['id']})",
            'success',
            null,
            [
                'deleted_application' => $applicationData,
                'admin_user' => Auth::user()->email,
                'files_deleted' => count($projectApplication->attachments ?? []),
            ]
        );

        return redirect()->route('admin.project-applications.index')
                        ->with('success', 'Project application deleted successfully.');
    }

    /**
     * Update application status via AJAX.
     */
    public function updateStatus(Request $request, ProjectApplication $projectApplication): JsonResponse
    {
        try {
            $validated = $request->validate([
                'status' => 'required|in:pending,under_review,approved,rejected,in_progress,completed,cancelled',
                'admin_notes' => 'nullable|string|max:5000',
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        }

        $oldStatus = $projectApplication->status;
        
        $projectApplication->update([
            'status' => $validated['status'],
            'admin_notes' => $validated['admin_notes'] ?? $projectApplication->admin_notes,
            'reviewed_by' => Auth::id(),
            'reviewed_at' => now(),
        ]);

        // Log the status change
        $this->activityLogger->logCustomerActivity(
            'admin_project_application_status_update',
            'Updated project application status',
            'success',
            null,
            [
                'application_id' => $projectApplication->id,
                'old_status' => $oldStatus,
                'new_status' => $validated['status'],
                'admin_notes_updated' => !empty($validated['admin_notes']),
                'admin_user' => Auth::user()->email,
                'application_user' => $projectApplication->user->email,
            ]
        );

        return response()->json([
            'success' => true,
            'message' => "Status updated to '{$validated['status']}' successfully.",
            'new_status' => $validated['status'],
            'status_label' => ucfirst(str_replace('_', ' ', $validated['status'])),
        ]);
    }

    /**
     * Download application attachment.
     */
    public function downloadAttachment(ProjectApplication $projectApplication, int $attachmentIndex): mixed
    {
        $attachments = $projectApplication->attachments ?? [];
        
        if (!isset($attachments[$attachmentIndex])) {
            abort(404, 'Attachment not found.');
        }

        $attachment = $attachments[$attachmentIndex];
        $filePath = $attachment['file_path'] ?? null;

        if (!$filePath || !Storage::disk('public')->exists($filePath)) {
            abort(404, 'File not found.');
        }

        // Log the download
        $this->activityLogger->logCustomerActivity(
            'admin_project_application_file_download',
            "Admin downloaded attachment: {$attachment['original_name']} from application: {$projectApplication->title}",
            'success',
            null,
            [
                'application_id' => $projectApplication->id,
                'file_name' => $attachment['original_name'],
                'file_size' => $attachment['file_size'] ?? 0,
                'admin_user' => Auth::user()->email,
                'application_user' => $projectApplication->user->email,
            ]
        );

        return Storage::disk('public')->download($filePath, $attachment['original_name']);
    }

    /**
     * Get application statistics for dashboard.
     */
    public function getStatistics(): JsonResponse
    {
        $stats = [
            'total' => ProjectApplication::count(),
            'pending' => ProjectApplication::where('status', 'pending')->count(),
            'under_review' => ProjectApplication::where('status', 'under_review')->count(),
            'approved' => ProjectApplication::where('status', 'approved')->count(),
            'rejected' => ProjectApplication::where('status', 'rejected')->count(),
            'in_progress' => ProjectApplication::where('status', 'in_progress')->count(),
            'completed' => ProjectApplication::where('status', 'completed')->count(),
            'cancelled' => ProjectApplication::where('status', 'cancelled')->count(),
            'this_month' => ProjectApplication::whereMonth('created_at', now()->month)
                                            ->whereYear('created_at', now()->year)
                                            ->count(),
            'this_week' => ProjectApplication::whereBetween('created_at', [
                now()->startOfWeek(),
                now()->endOfWeek()
            ])->count(),
        ];

        return response()->json($stats);
    }
}
