<?php

namespace App\Providers;

use App\Services\FileService;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Config;

class FileServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(FileService::class, function ($app) {
            return new FileService();
        });
        
        $this->app->alias(FileService::class, 'file-service');
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Publish configuration
        $this->publishes([
            __DIR__.'/../../config/file.php' => config_path('file.php'),
        ], 'file-config');
        
        // Set performance parameters
        $this->setPerformanceParameters();
        
        // Create required directories
        $this->createRequiredDirectories();
    }
    
    /**
     * Set performance parameters
     */
    protected function setPerformanceParameters(): void
    {
        $memoryLimit = Config::get('file.performance.memory_limit', '512M');
        $executionTime = Config::get('file.performance.max_execution_time', 300);
        
        if (php_sapi_name() !== 'cli') {
            ini_set('memory_limit', $memoryLimit);
            set_time_limit($executionTime);
        }
    }
    
    /**
     * Create required directories
     */
    protected function createRequiredDirectories(): void
    {
        $directories = [
            Config::get('file.storage.temp_path', 'temp/files'),
            Config::get('file.virus_scan.quarantine_path', storage_path('quarantine')),
            Config::get('file.archive_handling.extraction_path', storage_path('temp/extraction')),
        ];
        
        foreach ($directories as $directory) {
            if (!is_dir($directory)) {
                mkdir($directory, 0755, true);
            }
        }
    }
}
