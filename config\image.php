<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Image Processing Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains all the configuration options for the global
    | ImageService that handles optimization, sanitization, virus scanning,
    | and format conversion for uploaded images.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Default Settings
    |--------------------------------------------------------------------------
    */
    'default_quality' => env('IMAGE_DEFAULT_QUALITY', 85),
    'default_format' => env('IMAGE_DEFAULT_FORMAT', 'jpg'),
    'enable_webp_conversion' => env('IMAGE_ENABLE_WEBP', true),
    'enable_optimization' => env('IMAGE_ENABLE_OPTIMIZATION', true),
    'enable_virus_scan' => env('IMAGE_ENABLE_VIRUS_SCAN', true),

    /*
    |--------------------------------------------------------------------------
    | Allowed File Types
    |--------------------------------------------------------------------------
    */
    'allowed_mimes' => [
        'image/jpeg',
        'image/jpg', 
        'image/png',
        'image/gif',
        'image/webp',
        'image/bmp',
        'image/svg+xml',
    ],

    'allowed_extensions' => [
        'jpg',
        'jpeg',
        'png',
        'gif',
        'webp',
        'bmp',
        'svg',
    ],

    /*
    |--------------------------------------------------------------------------
    | File Size Limits
    |--------------------------------------------------------------------------
    */
    'max_file_size' => env('IMAGE_MAX_FILE_SIZE', 10 * 1024 * 1024), // 10MB
    'max_dimensions' => [
        'width' => env('IMAGE_MAX_WIDTH', 4000),
        'height' => env('IMAGE_MAX_HEIGHT', 4000),
    ],

    /*
    |--------------------------------------------------------------------------
    | Image Sizes and Variants
    |--------------------------------------------------------------------------
    */
    'sizes' => [
        'thumbnail' => [
            'width' => 150,
            'height' => 150,
            'quality' => 80,
            'crop' => true,
        ],
        'small' => [
            'width' => 300,
            'height' => 300,
            'quality' => 85,
            'crop' => false,
        ],
        'medium' => [
            'width' => 600,
            'height' => 600,
            'quality' => 85,
            'crop' => false,
        ],
        'large' => [
            'width' => 1200,
            'height' => 1200,
            'quality' => 90,
            'crop' => false,
        ],
        'hero' => [
            'width' => 1920,
            'height' => 1080,
            'quality' => 90,
            'crop' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Storage Configuration
    |--------------------------------------------------------------------------
    */
    'storage' => [
        'disk' => env('IMAGE_STORAGE_DISK', 'public'),
        'path' => env('IMAGE_STORAGE_PATH', 'images'),
        'temp_path' => env('IMAGE_TEMP_PATH', 'temp/images'),
        'processed_path' => env('IMAGE_PROCESSED_PATH', 'images/processed'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Optimization Settings
    |--------------------------------------------------------------------------
    */
    'optimization' => [
        'jpeg_quality' => env('IMAGE_JPEG_QUALITY', 85),
        'png_compression' => env('IMAGE_PNG_COMPRESSION', 6),
        'webp_quality' => env('IMAGE_WEBP_QUALITY', 80),
        'strip_metadata' => env('IMAGE_STRIP_METADATA', true),
        'progressive_jpeg' => env('IMAGE_PROGRESSIVE_JPEG', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    */
    'security' => [
        'scan_for_malware' => env('IMAGE_SCAN_MALWARE', true),
        'validate_image_content' => env('IMAGE_VALIDATE_CONTENT', true),
        'sanitize_filename' => env('IMAGE_SANITIZE_FILENAME', true),
        'remove_exif_data' => env('IMAGE_REMOVE_EXIF', true),
        'check_file_signature' => env('IMAGE_CHECK_SIGNATURE', true),
        'max_scan_time' => env('IMAGE_MAX_SCAN_TIME', 30), // seconds
    ],

    /*
    |--------------------------------------------------------------------------
    | Virus Scanning Configuration
    |--------------------------------------------------------------------------
    */
    'virus_scan' => [
        'enabled' => env('VIRUS_SCAN_ENABLED', false),
        'method' => env('VIRUS_SCAN_METHOD', 'clamav'), // clamav, windows_defender, custom
        'clamav' => [
            'socket' => env('CLAMAV_SOCKET', '/var/run/clamav/clamd.ctl'),
            'host' => env('CLAMAV_HOST', 'localhost'),
            'port' => env('CLAMAV_PORT', 3310),
            'timeout' => env('CLAMAV_TIMEOUT', 30),
        ],
        'custom_command' => env('VIRUS_SCAN_COMMAND', null),
        'quarantine_path' => env('VIRUS_QUARANTINE_PATH', storage_path('quarantine')),
    ],

    /*
    |--------------------------------------------------------------------------
    | Watermark Settings
    |--------------------------------------------------------------------------
    */
    'watermark' => [
        'enabled' => env('IMAGE_WATERMARK_ENABLED', false),
        'image_path' => env('IMAGE_WATERMARK_PATH', null),
        'text' => env('IMAGE_WATERMARK_TEXT', null),
        'position' => env('IMAGE_WATERMARK_POSITION', 'bottom-right'), // top-left, top-right, bottom-left, bottom-right, center
        'opacity' => env('IMAGE_WATERMARK_OPACITY', 50),
        'margin' => env('IMAGE_WATERMARK_MARGIN', 10),
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging and Monitoring
    |--------------------------------------------------------------------------
    */
    'logging' => [
        'enabled' => env('IMAGE_LOGGING_ENABLED', true),
        'log_channel' => env('IMAGE_LOG_CHANNEL', 'daily'),
        'log_level' => env('IMAGE_LOG_LEVEL', 'info'),
        'log_processing_time' => env('IMAGE_LOG_PROCESSING_TIME', true),
        'log_file_details' => env('IMAGE_LOG_FILE_DETAILS', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Settings
    |--------------------------------------------------------------------------
    */
    'performance' => [
        'memory_limit' => env('IMAGE_MEMORY_LIMIT', '256M'),
        'max_execution_time' => env('IMAGE_MAX_EXECUTION_TIME', 60),
        'enable_cache' => env('IMAGE_ENABLE_CACHE', true),
        'cache_ttl' => env('IMAGE_CACHE_TTL', 3600), // 1 hour
        'parallel_processing' => env('IMAGE_PARALLEL_PROCESSING', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Error Handling
    |--------------------------------------------------------------------------
    */
    'error_handling' => [
        'throw_exceptions' => env('IMAGE_THROW_EXCEPTIONS', true),
        'fallback_image' => env('IMAGE_FALLBACK_PATH', null),
        'retry_attempts' => env('IMAGE_RETRY_ATTEMPTS', 3),
        'retry_delay' => env('IMAGE_RETRY_DELAY', 1000), // milliseconds
    ],
];
