<?php $__env->startSection('title', 'Reset Your Password - ChiSolution'); ?>

<?php $__env->startSection('content'); ?>
<h1 class="greeting">Reset Your Password</h1>

<p class="content-text">
    Hello <?php echo e($user->first_name); ?>,
</p>

<p class="content-text">
    We received a request to reset the password for your ChiSolution account. If you made this request, click the button below to set a new password:
</p>

<div class="button-container">
    <a href="<?php echo e($resetUrl); ?>" class="btn">Reset Password</a>
</div>

<div class="info-box">
    <p>
        <strong>Security Notice:</strong> This password reset link will expire in <?php echo e($expireMinutes); ?> minutes for your security.
    </p>
</div>

<p class="content-text">
    If the button above doesn't work, you can copy and paste the following link into your browser:
</p>

<p class="content-text" style="word-break: break-all; background-color: #f7fafc; padding: 15px; border-radius: 6px; font-family: monospace; font-size: 14px;">
    <?php echo e($resetUrl); ?>

</p>

<div class="warning-box">
    <p>
        <strong>Important:</strong> If you didn't request a password reset, please ignore this email. Your password will remain unchanged, and no further action is required.
    </p>
</div>

<p class="content-text">
    For your security, we recommend:
</p>

<ul style="margin-left: 20px; margin-bottom: 20px; color: #4a5568;">
    <li>Using a strong, unique password</li>
    <li>Not sharing your password with anyone</li>
    <li>Enabling two-factor authentication when available</li>
</ul>

<p class="content-text">
    If you have any questions or concerns, please don't hesitate to contact our support team.
</p>

<p class="content-text">
    Best regards,<br>
    <strong>The ChiSolution Team</strong>
</p>

<hr style="border: none; border-top: 1px solid #e2e8f0; margin: 30px 0;">

<p style="font-size: 12px; color: #a0aec0; text-align: center;">
    This email was sent to <?php echo e($user->email); ?>. If you believe this was sent in error, please contact us immediately.
</p>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('emails.layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\files\chisolution\resources\views/emails/auth/reset-password.blade.php ENDPATH**/ ?>