@extends('emails.layout')

@section('title', 'Project Application Received - ' . $application->reference_number)

@section('content')
<div class="greeting">
    Thank you for your project inquiry, {{ $customer_name }}!
</div>

<p class="content-text">
    We've successfully received your project application and are excited to learn more about your requirements.
</p>

<!-- Application Summary Box -->
<div style="background-color: #f0fff4; border: 1px solid #9ae6b4; border-radius: 8px; padding: 20px; margin: 20px 0;">
    <div style="text-align: center;">
        <div style="font-size: 48px; margin-bottom: 10px;">🚀</div>
        <h3 style="margin: 0 0 10px 0; color: #2f855a; font-size: 20px;">Project Application Received</h3>
        <p style="margin: 0; color: #38a169; font-size: 16px;">
            Reference Number: <strong>{{ $application->reference_number }}</strong>
        </p>
    </div>
</div>

<!-- Application Details -->
<div style="background-color: #f7fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px; margin: 20px 0;">
    <h3 style="margin: 0 0 15px 0; color: #2d3748; font-size: 18px;">Application Details</h3>
    
    <table style="width: 100%; border-collapse: collapse;">
        <tr>
            <td style="padding: 8px 0; color: #4a5568; font-weight: 600;">Reference Number:</td>
            <td style="padding: 8px 0; color: #2d3748; text-align: right; font-family: monospace;">{{ $application->reference_number }}</td>
        </tr>
        @if($application->project_type)
        <tr>
            <td style="padding: 8px 0; color: #4a5568; font-weight: 600;">Project Type:</td>
            <td style="padding: 8px 0; color: #2d3748; text-align: right;">{{ $application->project_type }}</td>
        </tr>
        @endif
        <tr>
            <td style="padding: 8px 0; color: #4a5568; font-weight: 600;">Submission Date:</td>
            <td style="padding: 8px 0; color: #2d3748; text-align: right;">{{ $application->created_at->format('M d, Y \a\t g:i A') }}</td>
        </tr>
        <tr>
            <td style="padding: 8px 0; color: #4a5568; font-weight: 600;">Email:</td>
            <td style="padding: 8px 0; color: #2d3748; text-align: right;">{{ $application->email }}</td>
        </tr>
        @if($application->phone)
        <tr>
            <td style="padding: 8px 0; color: #4a5568; font-weight: 600;">Phone:</td>
            <td style="padding: 8px 0; color: #2d3748; text-align: right;">{{ $application->phone }}</td>
        </tr>
        @endif
        @if($application->company)
        <tr>
            <td style="padding: 8px 0; color: #4a5568; font-weight: 600;">Company:</td>
            <td style="padding: 8px 0; color: #2d3748; text-align: right;">{{ $application->company }}</td>
        </tr>
        @endif
        @if($application->budget_range)
        <tr>
            <td style="padding: 8px 0; color: #4a5568; font-weight: 600;">Budget Range:</td>
            <td style="padding: 8px 0; color: #2d3748; text-align: right;">{{ $application->budget_range }}</td>
        </tr>
        @endif
        @if($application->timeline)
        <tr>
            <td style="padding: 8px 0; color: #4a5568; font-weight: 600;">Timeline:</td>
            <td style="padding: 8px 0; color: #2d3748; text-align: right;">{{ $application->timeline }}</td>
        </tr>
        @endif
    </table>
</div>

<!-- Project Description -->
@if($application->project_description)
<h3 style="color: #2d3748; margin: 30px 0 15px 0;">Project Description</h3>
<div style="border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; margin-bottom: 15px; background-color: #ffffff;">
    <p style="margin: 0; color: #4a5568; line-height: 1.6; white-space: pre-wrap;">{{ $application->project_description }}</p>
</div>
@endif

<!-- Requirements -->
@if($application->requirements)
<h3 style="color: #2d3748; margin: 30px 0 15px 0;">Requirements</h3>
<div style="border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; margin-bottom: 15px; background-color: #ffffff;">
    <p style="margin: 0; color: #4a5568; line-height: 1.6; white-space: pre-wrap;">{{ $application->requirements }}</p>
</div>
@endif

<!-- Additional Information -->
@if($application->additional_info)
<h3 style="color: #2d3748; margin: 30px 0 15px 0;">Additional Information</h3>
<div style="border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; margin-bottom: 15px; background-color: #ffffff;">
    <p style="margin: 0; color: #4a5568; line-height: 1.6; white-space: pre-wrap;">{{ $application->additional_info }}</p>
</div>
@endif

<!-- Attached Files -->
@if($application->attachments && count($application->attachments) > 0)
<h3 style="color: #2d3748; margin: 30px 0 15px 0;">Attached Files</h3>
<div style="border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; margin-bottom: 15px; background-color: #ffffff;">
    @foreach($application->attachments as $attachment)
    <p style="margin: 0 0 5px 0; color: #4a5568;">
        📎 {{ basename($attachment) }}
    </p>
    @endforeach
</div>
@endif

<!-- Next Steps -->
<div class="info-box">
    <p><strong>What Happens Next?</strong></p>
    <p>
        Our project team will review your requirements and get back to you with next steps:
    </p>
    <ul style="margin: 10px 0; padding-left: 20px; color: #2c5282;">
        <li><strong>Initial Review:</strong> 1-2 business days</li>
        <li><strong>Project Assessment:</strong> Our team will analyze your requirements and scope</li>
        <li><strong>Consultation Call:</strong> We'll schedule a call to discuss your project in detail</li>
        <li><strong>Proposal:</strong> You'll receive a detailed proposal with timeline and pricing</li>
        <li><strong>Project Kickoff:</strong> Once approved, we'll begin your project</li>
    </ul>
</div>

<!-- Our Services -->
<div style="background-color: #ebf8ff; border-left: 4px solid #3182ce; padding: 20px; margin: 20px 0; border-radius: 0 8px 8px 0;">
    <p style="margin: 0 0 10px 0; color: #2c5282; font-weight: 600;">
        Our Services Include:
    </p>
    <ul style="margin: 0; padding-left: 20px; color: #2c5282; font-size: 14px;">
        <li>Custom Web Development</li>
        <li>Mobile App Development</li>
        <li>E-commerce Solutions</li>
        <li>Digital Marketing</li>
        <li>UI/UX Design</li>
        <li>Ongoing Support & Maintenance</li>
    </ul>
</div>

<!-- Important Information -->
<div style="background-color: #fffbeb; border-left: 4px solid #f59e0b; padding: 20px; margin: 20px 0; border-radius: 0 8px 8px 0;">
    <p style="margin: 0 0 10px 0; color: #92400e; font-weight: 600;">
        Important Information:
    </p>
    <ul style="margin: 0; padding-left: 20px; color: #92400e; font-size: 14px;">
        <li>Please keep your reference number <strong>{{ $application->reference_number }}</strong> for all future communications</li>
        <li>We'll contact you using the email address provided: {{ $application->email }}</li>
        <li>If you need to provide additional information, please reply to this email with your reference number</li>
        <li>All project discussions will be confidential and professional</li>
    </ul>
</div>

<!-- Action Buttons -->
<div class="button-container">
    <a href="{{ route('services') }}" class="btn">
        View Our Services
    </a>
    
    <a href="{{ route('portfolio') }}" class="btn btn-secondary" style="margin-left: 10px;">
        See Our Work
    </a>
</div>

<p class="content-text">
    Thank you for considering {{ $company_name }} for your project. We're excited about the opportunity to work with you and bring your vision to life!
</p>

<p class="content-text">
    Our team of experienced developers and designers is ready to help you achieve your goals. We'll be in touch soon to discuss your project in more detail.
</p>

<p class="content-text">
    If you have any immediate questions or need to provide additional information, please don't hesitate to contact us at 
    <a href="mailto:{{ config('mail.from.address') }}" style="color: #667eea;">{{ config('mail.from.address') }}</a>.
</p>

<p class="content-text">
    Best regards,<br>
    The {{ $company_name }} Project Team
</p>
@endsection
