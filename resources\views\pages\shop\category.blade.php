@extends('layouts.app')

@section('title', ($category->meta_title ?: $category->name) . ' - ' . __('common.company_name'))
@section('meta_description', $category->meta_description ?: $category->description ?: 'Browse products in ' . $category->name . ' category')
@section('meta_keywords', $category->meta_keywords ?: $category->name . ', products, shop')

@section('og_title', ($category->meta_title ?: $category->name) . ' - ' . __('common.company_name'))
@section('og_description', $category->meta_description ?: $category->description ?: 'Browse products in ' . $category->name . ' category')
@section('og_image', $category->image ? asset('storage/' . $category->image) : asset('images/og-image.jpg'))

@section('twitter_title', ($category->meta_title ?: $category->name) . ' - ' . __('common.company_name'))
@section('twitter_description', $category->meta_description ?: $category->description ?: 'Browse products in ' . $category->name . ' category')
@section('twitter_image', $category->image ? asset('storage/' . $category->image) : asset('images/twitter-image.jpg'))

@section('content')
<!-- Category Hero -->
<section class="relative bg-gradient-blue text-white overflow-hidden">
    @if($category->image)
        <div class="absolute inset-0 opacity-20">
            <img src="{{ asset('storage/' . $category->image) }}" alt="{{ $category->name }}" class="w-full h-full object-cover">
        </div>
        <div class="absolute inset-0 bg-gradient-to-r from-blue-900/80 to-blue-600/80"></div>
    @else
        <div class="absolute inset-0 opacity-10">
            <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></g></svg>');"></div>
        </div>
    @endif

    <div class="container mx-auto px-4 py-16 relative z-10">
        <div class="text-center max-w-4xl mx-auto">
            @if($category->is_featured)
                <div class="inline-flex items-center px-4 py-2 bg-yellow-500/20 border border-yellow-400/30 rounded-full text-yellow-200 text-sm font-medium mb-4">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"/>
                    </svg>
                    Featured Category
                </div>
            @endif
            <!-- Breadcrumbs -->
            <nav class="mb-6">
                <ol class="flex items-center justify-center space-x-2 text-blue-200">
                    <li><a href="{{ route('shop.index') }}" class="hover:text-white transition-colors">Shop</a></li>
                    @foreach($category->breadcrumbs as $breadcrumb)
                    <li class="flex items-center">
                        <svg class="w-4 h-4 mx-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        @if($loop->last)
                        <span class="text-white font-medium">{{ $breadcrumb['name'] }}</span>
                        @else
                        <a href="{{ $breadcrumb['url'] }}" class="hover:text-white transition-colors">{{ $breadcrumb['name'] }}</a>
                        @endif
                    </li>
                    @endforeach
                </ol>
            </nav>
            
            <h1 class="heading-1 text-white mb-4">{{ $category->name }}</h1>
            @if($category->description)
            <p class="text-lead text-blue-100">{{ $category->description }}</p>
            @endif
        </div>
    </div>
    
    <div class="absolute bottom-0 left-0 w-full">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" class="relative block w-full h-16 fill-white">
            <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"></path>
        </svg>
    </div>
</section>

<!-- Subcategories -->
@if($subcategories->count() > 0)
<section class="py-12 bg-gray-50">
    <div class="container mx-auto px-4">
        <h2 class="text-xl font-semibold text-gray-900 mb-6">Browse Subcategories</h2>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            @foreach($subcategories as $subcategory)
            <a href="{{ route('shop.category', $subcategory->slug) }}" 
               class="bg-white rounded-lg p-4 text-center hover:shadow-md transition-shadow">
                <h3 class="font-medium text-gray-900 mb-1">{{ $subcategory->name }}</h3>
                <p class="text-sm text-gray-500">({{ $subcategory->products_count }} products)</p>
            </a>
            @endforeach
        </div>
    </div>
</section>
@endif

<!-- Products -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- Sidebar Filters -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow-sm p-6 sticky top-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">Filters</h3>
                    
                    <form action="{{ route('shop.category', $category->slug) }}" method="GET" id="filter-form">
                        @if(request('search'))
                            <input type="hidden" name="search" value="{{ request('search') }}">
                        @endif
                        
                        <!-- Price Range -->
                        @if($priceRange && $priceRange->min_price !== $priceRange->max_price)
                        <div class="mb-8">
                            <h4 class="font-medium text-gray-900 mb-4">Price Range</h4>
                            <div class="space-y-4">
                                <div class="grid grid-cols-2 gap-2">
                                    <div>
                                        <label class="block text-xs text-gray-600 mb-1">Min Price</label>
                                        <input type="number" name="min_price" value="{{ request('min_price') }}" 
                                               min="{{ $priceRange->min_price }}" max="{{ $priceRange->max_price }}"
                                               placeholder="R{{ number_format($priceRange->min_price, 0) }}"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500">
                                    </div>
                                    <div>
                                        <label class="block text-xs text-gray-600 mb-1">Max Price</label>
                                        <input type="number" name="max_price" value="{{ request('max_price') }}" 
                                               min="{{ $priceRange->min_price }}" max="{{ $priceRange->max_price }}"
                                               placeholder="R{{ number_format($priceRange->max_price, 0) }}"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500">
                                    </div>
                                </div>
                                <div class="text-xs text-gray-500">
                                    Range: R{{ number_format($priceRange->min_price, 0) }} - R{{ number_format($priceRange->max_price, 0) }}
                                </div>
                            </div>
                        </div>
                        @endif
                        
                        <!-- Filter Actions -->
                        <div class="space-y-3">
                            <button type="submit" class="w-full btn-primary">
                                Apply Filters
                            </button>
                            <a href="{{ route('shop.category', $category->slug) }}" class="w-full btn-outline text-center block">
                                Clear Filters
                            </a>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Products Grid -->
            <div class="lg:col-span-3">
                <!-- Sort and Results Info -->
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
                    <div class="text-gray-600 mb-4 sm:mb-0">
                        Showing {{ $products->firstItem() ?? 0 }}-{{ $products->lastItem() ?? 0 }} of {{ $products->total() }} products
                        @if(request('search'))
                            for "<strong>{{ request('search') }}</strong>"
                        @endif
                        in {{ $category->name }}
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <label class="text-sm text-gray-600">Sort by:</label>
                        <select name="sort" onchange="updateSort(this)" class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500">
                            <option value="name" {{ request('sort') === 'name' ? 'selected' : '' }}>Name</option>
                            <option value="price" {{ request('sort') === 'price' ? 'selected' : '' }}>Price</option>
                            <option value="created_at" {{ request('sort') === 'created_at' ? 'selected' : '' }}>Newest</option>
                            <option value="featured" {{ request('sort') === 'featured' ? 'selected' : '' }}>Featured</option>
                        </select>
                    </div>
                </div>
                
                @if($products->count() > 0)
                <!-- Products Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
                    @foreach($products as $product)
                    <div class="product-card bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                        <div class="relative">
                            <a href="{{ route('shop.product', $product->slug) }}">
                                <img src="{{ $product->primary_image }}" alt="{{ $product->name }}" 
                                     class="w-full h-48 object-cover hover-lift">
                            </a>
                            
                            @if($product->discount_percentage > 0)
                            <div class="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-medium">
                                -{{ $product->discount_percentage }}%
                            </div>
                            @endif
                            
                            @if($product->is_featured)
                            <div class="absolute top-2 right-2 bg-blue-600 text-white px-2 py-1 rounded text-xs font-medium">
                                Featured
                            </div>
                            @endif
                        </div>
                        
                        <div class="p-4">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                <a href="{{ route('shop.product', $product->slug) }}" class="hover:text-blue-600 transition-colors">
                                    {{ $product->name }}
                                </a>
                            </h3>
                            
                            @if($product->short_description)
                            <div class="text-gray-600 text-sm mb-3 line-clamp-2 prose prose-sm max-w-none">
                                {!! $product->short_description !!}
                            </div>
                            @endif
                            
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <span class="text-lg font-bold text-gray-900">{{ $product->formatted_price }}</span>
                                    @if($product->compare_price)
                                    <span class="text-sm text-gray-500 line-through">{{ $product->formatted_compare_price }}</span>
                                    @endif
                                </div>
                                
                                <div class="text-xs text-gray-500">
                                    {{ $product->stock_status }}
                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <a href="{{ route('shop.product', $product->slug) }}" class="w-full btn-primary text-center block">
                                    View Details
                                </a>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
                
                <!-- Pagination -->
                <div class="flex justify-center">
                    {{ $products->links() }}
                </div>
                @else
                <!-- No Products Found -->
                <div class="text-center py-12">
                    <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-12 h-12 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No products found</h3>
                    <p class="text-gray-600 mb-4">
                        No products match your current filters in this category.
                    </p>
                    <a href="{{ route('shop.index') }}" class="btn-primary">
                        Browse All Products
                    </a>
                </div>
                @endif
            </div>
        </div>
    </div>
</section>

@push('scripts')
<script>
function updateSort(select) {
    const url = new URL(window.location);
    url.searchParams.set('sort', select.value);
    window.location.href = url.toString();
}

// Auto-submit form when filters change
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('filter-form');
    const inputs = form.querySelectorAll('input[type="number"]');
    
    inputs.forEach(input => {
        input.addEventListener('change', function() {
            // Add a small delay to allow user to finish typing
            setTimeout(() => {
                form.submit();
            }, 500);
        });
    });
});
</script>
@endpush

@push('structured_data')
<script type="application/ld+json">
{
    "@context": "https://schema.org/",
    "@type": "CollectionPage",
    "name": "{{ $category->name }}",
    "description": "{{ $category->meta_description ?: $category->description ?: 'Browse products in ' . $category->name . ' category' }}",
    "url": "{{ route('shop.category', $category->slug) }}",
    @if($category->image)
    "image": "{{ asset('storage/' . $category->image) }}",
    @endif
    "mainEntity": {
        "@type": "ItemList",
        "numberOfItems": "{{ $products->total() }}",
        "itemListElement": [
            @foreach($products->take(10) as $index => $product)
            {
                "@type": "Product",
                "position": {{ $index + 1 }},
                "name": "{{ $product->name }}",
                "url": "{{ route('shop.product', $product->slug) }}",
                "image": "{{ $product->featured_image ? asset('storage/' . $product->featured_image) : $product->primary_image }}",
                "offers": {
                    "@type": "Offer",
                    "price": "{{ $product->price }}",
                    "priceCurrency": "ZAR",
                    "availability": "{{ $product->isInStock() ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock' }}"
                }
            }@if(!$loop->last),@endif
            @endforeach
        ]
    },
    "breadcrumb": {
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                "position": 1,
                "name": "Shop",
                "item": "{{ route('shop.index') }}"
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": "{{ $category->name }}",
                "item": "{{ route('shop.category', $category->slug) }}"
            }
        ]
    }
}
</script>
@endpush

@endsection
