<?php $__env->startSection('title', __('Reset Password')); ?>

<?php $__env->startSection('content'); ?>
<div class="auth-container">
    <div class="auth-card">
        <div class="auth-header">
            <h1 class="auth-title"><?php echo e(__('Reset Password')); ?></h1>
            <p class="auth-subtitle"><?php echo e(__('Enter your new password below')); ?></p>
        </div>

        <!-- Messages Container -->
        <div id="form-messages" class="form-messages"></div>

        <form id="reset-password-form" class="auth-form" method="POST" action="<?php echo e(route('password.update')); ?>">
            <?php echo csrf_field(); ?>

            <!-- Hidden Token Field -->
            <input type="hidden" name="token" value="<?php echo e($token); ?>">

            <!-- Email <PERSON> (readonly) -->
            <div class="floating-input-group">
                <input 
                    type="email" 
                    name="email" 
                    id="email"
                    class="floating-input <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> error <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                    value="<?php echo e($email ?? old('email')); ?>"
                    required 
                    readonly
                    placeholder=" "
                >
                <label for="email" class="floating-label"><?php echo e(__('auth.email')); ?></label>
                <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <span class="error-message"><?php echo e($message); ?></span>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Password Field -->
            <div class="floating-input-group">
                <input 
                    type="password" 
                    name="password" 
                    id="password"
                    class="floating-input <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> error <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                    required 
                    autocomplete="new-password"
                    placeholder=" "
                    minlength="8"
                >
                <label for="password" class="floating-label"><?php echo e(__('auth.new_password')); ?></label>
                <button type="button" class="password-toggle" onclick="togglePassword('password')">
                    <svg class="eye-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                </button>
                <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <span class="error-message"><?php echo e($message); ?></span>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Password Strength Indicator -->
            <div id="password-strength" class="password-strength">
                <div class="strength-bar">
                    <div class="strength-fill"></div>
                </div>
                <div class="strength-text"><?php echo e(__('auth.password_strength')); ?>: <span class="strength-level"><?php echo e(__('auth.weak')); ?></span></div>
            </div>

            <!-- Password Confirmation Field -->
            <div class="floating-input-group">
                <input 
                    type="password" 
                    name="password_confirmation" 
                    id="password_confirmation"
                    class="floating-input <?php $__errorArgs = ['password_confirmation'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> error <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                    required 
                    autocomplete="new-password"
                    placeholder=" "
                    minlength="8"
                >
                <label for="password_confirmation" class="floating-label"><?php echo e(__('auth.confirm_password')); ?></label>
                <button type="button" class="password-toggle" onclick="togglePassword('password_confirmation')">
                    <svg class="eye-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                </button>
                <?php $__errorArgs = ['password_confirmation'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <span class="error-message"><?php echo e($message); ?></span>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Password Requirements -->
            <div class="password-requirements">
                <p class="requirements-title"><?php echo e(__('auth.password_requirements')); ?>:</p>
                <ul class="requirements-list">
                    <li id="req-length" class="requirement"><?php echo e(__('auth.password_req_length')); ?></li>
                    <li id="req-lowercase" class="requirement"><?php echo e(__('auth.password_req_lowercase')); ?></li>
                    <li id="req-uppercase" class="requirement"><?php echo e(__('auth.password_req_uppercase')); ?></li>
                    <li id="req-number" class="requirement"><?php echo e(__('auth.password_req_number')); ?></li>
                    <li id="req-special" class="requirement"><?php echo e(__('auth.password_req_special')); ?></li>
                </ul>
            </div>

            <!-- Submit Button -->
            <button type="submit" id="reset-btn" class="auth-submit-btn">
                <span class="btn-text"><?php echo e(__('Reset Password')); ?></span>
                <span class="btn-loading" style="display: none;">
                    <svg class="loading-spinner" viewBox="0 0 24 24">
                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none" opacity="0.25"></circle>
                        <path fill="currentColor" opacity="0.75" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <?php echo e(__('auth.resetting_password')); ?>

                </span>
            </button>

            <!-- Back to Login -->
            <div class="auth-links">
                <a href="<?php echo e(route('login')); ?>" class="auth-link">
                    <svg class="link-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    <?php echo e(__('auth.back_to_login')); ?>

                </a>
            </div>
        </form>
    </div>
</div>

<!-- Success/Error Messages -->
<?php if(session('success')): ?>
    <div class="alert alert-success">
        <?php echo e(session('success')); ?>

    </div>
<?php endif; ?>

<?php if(session('error')): ?>
    <div class="alert alert-error">
        <?php echo e(session('error')); ?>

    </div>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="<?php echo e(asset('js/auth-forms.js')); ?>"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize reset password form with AJAX validation
    const resetForm = new AuthForm('reset-password-form', {
        submitButton: 'reset-btn',
        loadingText: '<?php echo e(__("auth.resetting_password")); ?>',
        successRedirect: '<?php echo e(route("login")); ?>',
        messages: {
            networkError: '<?php echo e(__("auth.network_error")); ?>',
            serverError: '<?php echo e(__("auth.server_error")); ?>',
            validationError: '<?php echo e(__("auth.validation_error")); ?>'
        }
    });

    // Initialize password strength checker
    const passwordStrengthChecker = new PasswordStrength('password', 'password-strength');

    // Password confirmation matching
    const password = document.getElementById('password');
    const passwordConfirmation = document.getElementById('password_confirmation');
    
    function checkPasswordMatch() {
        if (passwordConfirmation.value && password.value !== passwordConfirmation.value) {
            passwordConfirmation.setCustomValidity('<?php echo e(__("auth.password_mismatch")); ?>');
            passwordConfirmation.classList.add('error');
        } else {
            passwordConfirmation.setCustomValidity('');
            passwordConfirmation.classList.remove('error');
        }
    }

    password.addEventListener('input', checkPasswordMatch);
    passwordConfirmation.addEventListener('input', checkPasswordMatch);
});

// Password toggle function
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.nextElementSibling.querySelector('.password-toggle');
    
    if (field.type === 'password') {
        field.type = 'text';
        button.setAttribute('aria-label', '<?php echo e(__("auth.hide_password")); ?>');
    } else {
        field.type = 'password';
        button.setAttribute('aria-label', '<?php echo e(__("auth.show_password")); ?>');
    }
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.auth', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\files\chisolution\resources\views/auth/reset-password.blade.php ENDPATH**/ ?>