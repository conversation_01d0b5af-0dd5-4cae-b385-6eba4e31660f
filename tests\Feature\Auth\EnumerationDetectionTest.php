<?php

namespace Tests\Feature\Auth;

use App\Models\ActivityLog;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class EnumerationDetectionTest extends TestCase
{
    use RefreshDatabase;

    protected $seed = true; // Use seeders to populate required data

    /** @test */
    public function password_reset_request_for_existing_email_is_logged_correctly()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        $response = $this->postJson('/forgot-password', [
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => __('passwords.sent_secure'),
                ]);

        $log = ActivityLog::where('activity_type', 'password_reset_request')->first();
        $this->assertNotNull($log);
        
        // Check admin-specific logging
        $this->assertEquals('<EMAIL>', $log->user_email);
        $this->assertTrue($log->request_data['email_exists_in_system']);
        $this->assertFalse($log->request_data['enumeration_attempt']);
        $this->assertEquals('Valid email - reset link sent to existing user', $log->request_data['admin_notes']);
        $this->assertEquals('legitimate_request', $log->response_data['security_status']);
        $this->assertStringContainsString('EXISTING user', $log->activity_description);
    }

    /** @test */
    public function password_reset_request_for_non_existing_email_is_flagged_as_enumeration()
    {
        $response = $this->postJson('/forgot-password', [
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => __('passwords.sent_secure'),
                ]);

        $log = ActivityLog::where('activity_type', 'password_reset_request')->first();
        $this->assertNotNull($log);
        
        // Check enumeration detection
        $this->assertEquals('<EMAIL>', $log->user_email);
        $this->assertFalse($log->request_data['email_exists_in_system']);
        $this->assertTrue($log->request_data['enumeration_attempt']);
        $this->assertEquals('ENUMERATION ATTEMPT - Email does not exist in system', $log->request_data['admin_notes']);
        $this->assertEquals('potential_enumeration', $log->response_data['security_status']);
        $this->assertStringContainsString('NON-EXISTENT email', $log->activity_description);
        $this->assertStringContainsString('ENUMERATION ATTEMPT', $log->activity_description);
        
        // Check risk scoring
        $this->assertGreaterThan(25, $log->risk_score); // Should have increased risk
    }

    /** @test */
    public function user_always_gets_same_success_message_regardless_of_email_existence()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        // Test with existing email
        $response1 = $this->postJson('/forgot-password', [
            'email' => '<EMAIL>',
        ]);

        // Test with non-existing email
        $response2 = $this->postJson('/forgot-password', [
            'email' => '<EMAIL>',
        ]);

        // Both should return the same response to the user
        $response1->assertStatus(200)
                 ->assertJson([
                     'success' => true,
                     'message' => __('passwords.sent_secure'),
                 ]);

        $response2->assertStatus(200)
                 ->assertJson([
                     'success' => true,
                     'message' => __('passwords.sent_secure'),
                 ]);

        // But admin logs should show the difference
        $logs = ActivityLog::where('activity_type', 'password_reset_request')->get();
        $this->assertCount(2, $logs);
        
        $existingEmailLog = $logs->where('user_email', '<EMAIL>')->first();
        $nonExistentEmailLog = $logs->where('user_email', '<EMAIL>')->first();
        
        $this->assertFalse($existingEmailLog->request_data['enumeration_attempt']);
        $this->assertTrue($nonExistentEmailLog->request_data['enumeration_attempt']);
    }

    /** @test */
    public function repeated_enumeration_attempts_increase_risk_score()
    {
        // First enumeration attempt
        $this->postJson('/forgot-password', [
            'email' => '<EMAIL>',
        ]);

        // Second enumeration attempt from same IP
        $this->postJson('/forgot-password', [
            'email' => '<EMAIL>',
        ]);

        // Third enumeration attempt from same IP
        $response = $this->postJson('/forgot-password', [
            'email' => '<EMAIL>',
        ]);

        $logs = ActivityLog::where('activity_type', 'password_reset_request')
                          ->orderBy('occurred_at', 'desc')
                          ->get();

        $latestLog = $logs->first();
        
        // Latest log should have higher risk score due to repeated attempts
        $this->assertTrue($latestLog->is_suspicious);

        // Check if security notes contain enumeration-related information
        $this->assertNotNull($latestLog->security_notes);
        $this->assertStringContainsString('EMAIL ENUMERATION', $latestLog->security_notes);
    }

    /** @test */
    public function suspicious_email_patterns_are_detected()
    {
        $suspiciousEmails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
        ];

        foreach ($suspiciousEmails as $email) {
            $this->postJson('/forgot-password', [
                'email' => $email,
            ]);
        }

        $logs = ActivityLog::where('activity_type', 'password_reset_request')->get();
        
        // All should be flagged as enumeration attempts with suspicious patterns
        foreach ($logs as $log) {
            $this->assertTrue($log->request_data['enumeration_attempt']);
            $this->assertGreaterThan(30, $log->risk_score);
        }
    }

    /** @test */
    public function admin_can_filter_enumeration_attempts()
    {
        $adminRole = \App\Models\Role::where('name', 'admin')->first();
        $admin = User::factory()->create(['role_id' => $adminRole->id]);

        // Create some logs with enumeration attempts
        $user = User::factory()->create(['email' => '<EMAIL>']);
        
        $this->postJson('/forgot-password', ['email' => '<EMAIL>']); // Valid
        $this->postJson('/forgot-password', ['email' => '<EMAIL>']); // Enumeration
        $this->postJson('/forgot-password', ['email' => '<EMAIL>']); // Enumeration

        // Test filter
        $response = $this->actingAs($admin)->get('/admin/activity-logs?enumeration_only=1');
        
        $response->assertStatus(200);
        $response->assertViewIs('admin.activity-logs.index');
    }

    /** @test */
    public function security_notes_provide_detailed_admin_insights()
    {
        // Create multiple enumeration attempts to trigger security notes
        $this->postJson('/forgot-password', ['email' => '<EMAIL>']);
        $this->postJson('/forgot-password', ['email' => '<EMAIL>']);
        $this->postJson('/forgot-password', ['email' => '<EMAIL>']); // Suspicious pattern

        $logs = ActivityLog::where('activity_type', 'password_reset_request')
                          ->orderBy('occurred_at', 'desc')
                          ->get();

        $latestLog = $logs->first();
        
        $this->assertNotNull($latestLog->security_notes);
        $this->assertStringContainsString('EMAIL ENUMERATION', $latestLog->security_notes);
        $this->assertStringContainsString('SUSPICIOUS PATTERN', $latestLog->security_notes);

        // Check if repeated enumeration is detected (may not always be present)
        if (strpos($latestLog->security_notes, 'REPEATED') !== false) {
            $this->assertStringContainsString('REPEATED', $latestLog->security_notes);
        }
    }

    /** @test */
    public function admin_logs_show_actual_vs_public_response()
    {
        $response = $this->postJson('/forgot-password', [
            'email' => '<EMAIL>',
        ]);

        $log = ActivityLog::where('activity_type', 'password_reset_request')->first();
        
        // Public response (what user sees)
        $this->assertEquals('success', $log->response_data['public_response']);
        $this->assertStringContainsString('If an account with that email address exists', $log->response_data['public_message']);
        
        // Admin reality (what actually happened)
        $this->assertFalse($log->response_data['actual_email_exists']);
        $this->assertFalse($log->response_data['actual_email_sent']);
        $this->assertEquals('No email sent', $log->response_data['admin_action_taken']);
        $this->assertEquals('potential_enumeration', $log->response_data['security_status']);
    }
}
