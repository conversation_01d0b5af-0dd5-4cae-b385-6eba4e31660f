<?php

namespace Tests\Unit\Models;

use App\Models\ProjectApplication;
use App\Models\User;
use App\Models\Service;
use App\Models\Project;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ProjectApplicationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Seed roles for User factory
        $this->seed(\Database\Seeders\RoleSeeder::class);
    }

    public function test_project_application_can_be_created()
    {
        $user = User::factory()->create();
        $service = Service::factory()->create();

        $application = ProjectApplication::create([
            'user_id' => $user->id,
            'service_id' => $service->id,
            'title' => 'E-commerce Website',
            'description' => 'Need a modern e-commerce platform with payment integration',
            'requirements' => 'Laravel, Vue.js, Stripe integration',
            'budget_range' => '15k-50k',
            'timeline' => '2-3-months',
            'priority' => 'high',
        ]);

        $this->assertInstanceOf(ProjectApplication::class, $application);
        $this->assertEquals('E-commerce Website', $application->title);
        $this->assertEquals($user->id, $application->user_id);
        $this->assertEquals($service->id, $application->service_id);
        $this->assertNotNull($application->uuid);
        $this->assertEquals('pending', $application->status);
    }

    public function test_project_application_generates_uuid_and_sets_default_status()
    {
        $user = User::factory()->create();

        $application = ProjectApplication::create([
            'user_id' => $user->id,
            'title' => 'Test Project',
            'description' => 'Test description',
        ]);

        $this->assertNotNull($application->uuid);
        $this->assertIsString((string) $application->uuid);
        $this->assertEquals('pending', $application->status);
    }

    public function test_project_application_casts_attributes_correctly()
    {
        $user = User::factory()->create();
        $attachments = [
            [
                'original_name' => 'requirements.pdf',
                'stored_name' => 'requirements_123.pdf',
                'path' => 'project-applications/1/2025/07/requirements_123.pdf',
                'size' => 2048,
                'mime_type' => 'application/pdf',
            ]
        ];

        $application = ProjectApplication::create([
            'user_id' => $user->id,
            'title' => 'Test Project',
            'description' => 'Test description',
            'attachments' => $attachments,
            'reviewed_at' => now(),
        ]);

        $this->assertIsArray($application->attachments);
        $this->assertEquals($attachments, $application->attachments);
        $this->assertInstanceOf(\Carbon\Carbon::class, $application->reviewed_at);
    }

    public function test_project_application_belongs_to_user()
    {
        $user = User::factory()->create();
        
        $application = ProjectApplication::create([
            'user_id' => $user->id,
            'title' => 'Test Project',
            'description' => 'Test description',
        ]);

        $this->assertInstanceOf(User::class, $application->user);
        $this->assertEquals($user->id, $application->user->id);
    }

    public function test_project_application_belongs_to_service()
    {
        $user = User::factory()->create();
        $service = Service::factory()->create();
        
        $application = ProjectApplication::create([
            'user_id' => $user->id,
            'service_id' => $service->id,
            'title' => 'Test Project',
            'description' => 'Test description',
        ]);

        $this->assertInstanceOf(Service::class, $application->service);
        $this->assertEquals($service->id, $application->service->id);
    }

    public function test_project_application_belongs_to_reviewed_by_user()
    {
        $user = User::factory()->create();
        $admin = User::factory()->create();
        
        $application = ProjectApplication::create([
            'user_id' => $user->id,
            'title' => 'Test Project',
            'description' => 'Test description',
            'reviewed_by' => $admin->id,
        ]);

        $this->assertInstanceOf(User::class, $application->reviewedBy);
        $this->assertEquals($admin->id, $application->reviewedBy->id);
    }

    public function test_project_application_belongs_to_project()
    {
        $user = User::factory()->create();
        $project = Project::factory()->create();
        
        $application = ProjectApplication::create([
            'user_id' => $user->id,
            'title' => 'Test Project',
            'description' => 'Test description',
            'project_id' => $project->id,
        ]);

        $this->assertInstanceOf(Project::class, $application->project);
        $this->assertEquals($project->id, $application->project->id);
    }

    public function test_pending_scope_filters_pending_applications()
    {
        $user = User::factory()->create();

        ProjectApplication::create([
            'user_id' => $user->id,
            'title' => 'Pending Project',
            'description' => 'Description',
            'status' => 'pending',
        ]);

        ProjectApplication::create([
            'user_id' => $user->id,
            'title' => 'Approved Project',
            'description' => 'Description',
            'status' => 'approved',
        ]);

        $pendingApplications = ProjectApplication::pending()->get();

        $this->assertCount(1, $pendingApplications);
        $this->assertEquals('Pending Project', $pendingApplications->first()->title);
    }

    public function test_approved_scope_filters_approved_applications()
    {
        $user = User::factory()->create();

        ProjectApplication::create([
            'user_id' => $user->id,
            'title' => 'Pending Project',
            'description' => 'Description',
            'status' => 'pending',
        ]);

        ProjectApplication::create([
            'user_id' => $user->id,
            'title' => 'Approved Project',
            'description' => 'Description',
            'status' => 'approved',
        ]);

        $approvedApplications = ProjectApplication::approved()->get();

        $this->assertCount(1, $approvedApplications);
        $this->assertEquals('Approved Project', $approvedApplications->first()->title);
    }

    public function test_rejected_scope_filters_rejected_applications()
    {
        $user = User::factory()->create();

        ProjectApplication::create([
            'user_id' => $user->id,
            'title' => 'Pending Project',
            'description' => 'Description',
            'status' => 'pending',
        ]);

        ProjectApplication::create([
            'user_id' => $user->id,
            'title' => 'Rejected Project',
            'description' => 'Description',
            'status' => 'rejected',
        ]);

        $rejectedApplications = ProjectApplication::rejected()->get();

        $this->assertCount(1, $rejectedApplications);
        $this->assertEquals('Rejected Project', $rejectedApplications->first()->title);
    }

    public function test_mark_as_reviewed_updates_review_fields()
    {
        $user = User::factory()->create();
        $admin = User::factory()->create();
        
        $application = ProjectApplication::create([
            'user_id' => $user->id,
            'title' => 'Test Project',
            'description' => 'Test description',
            'status' => 'pending',
        ]);

        $application->markAsReviewed($admin, 'approved', 'Great project idea!');

        $fresh = $application->fresh();
        $this->assertEquals('approved', $fresh->status);
        $this->assertEquals('Great project idea!', $fresh->admin_notes);
        $this->assertNotNull($fresh->reviewed_at);
        $this->assertEquals($admin->id, $fresh->reviewed_by);
    }

    public function test_get_status_color_attribute_returns_correct_colors()
    {
        $user = User::factory()->create();

        $pendingApp = ProjectApplication::create([
            'user_id' => $user->id,
            'title' => 'Test',
            'description' => 'Test',
            'status' => 'pending',
        ]);

        $approvedApp = ProjectApplication::create([
            'user_id' => $user->id,
            'title' => 'Test',
            'description' => 'Test',
            'status' => 'approved',
        ]);

        $rejectedApp = ProjectApplication::create([
            'user_id' => $user->id,
            'title' => 'Test',
            'description' => 'Test',
            'status' => 'rejected',
        ]);

        $this->assertEquals('yellow', $pendingApp->status_color);
        $this->assertEquals('green', $approvedApp->status_color);
        $this->assertEquals('red', $rejectedApp->status_color);
    }

    public function test_project_application_with_attachments()
    {
        $user = User::factory()->create();
        $attachments = [
            [
                'original_name' => 'project_spec.docx',
                'stored_name' => 'project_spec_20250702_123456.docx',
                'path' => 'project-applications/1/2025/07/project_spec_20250702_123456.docx',
                'size' => 1048576, // 1MB
                'mime_type' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'scan_results' => [
                    'virus_scan' => 'clean',
                    'content_scan' => 'safe',
                    'scanned_at' => now()->toISOString(),
                ]
            ],
            [
                'original_name' => 'mockup.jpg',
                'stored_name' => 'mockup_20250702_123457.webp',
                'path' => 'project-applications/1/2025/07/mockup_20250702_123457.webp',
                'size' => 256000, // 250KB
                'mime_type' => 'image/jpeg',
                'scan_results' => [
                    'virus_scan' => 'clean',
                    'optimized' => true,
                    'converted_to_webp' => true,
                ]
            ]
        ];

        $application = ProjectApplication::create([
            'user_id' => $user->id,
            'title' => 'Mobile App Development',
            'description' => 'Need a cross-platform mobile app for our business',
            'requirements' => 'React Native, Firebase, Push notifications',
            'budget_range' => '50k-100k',
            'timeline' => '3-6-months',
            'priority' => 'high',
            'attachments' => $attachments,
        ]);

        $this->assertCount(2, $application->attachments);
        $this->assertEquals('project_spec.docx', $application->attachments[0]['original_name']);
        $this->assertEquals('mockup.jpg', $application->attachments[1]['original_name']);
        $this->assertArrayHasKey('scan_results', $application->attachments[0]);
        $this->assertArrayHasKey('scan_results', $application->attachments[1]);
    }
}
