<?php

namespace App\Providers;

use App\Models\ShoppingCart;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\View;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Share cart count with all views
        View::composer('*', function ($view) {
            $cartCount = $this->getCartCount();
            $view->with('globalCartCount', $cartCount);
        });
    }

    /**
     * Get cart count for both authenticated and guest users.
     */
    private function getCartCount(): int
    {
        try {
            if (Auth::check()) {
                // For authenticated users
                $cart = ShoppingCart::active()
                    ->where('user_id', Auth::id())
                    ->first();
            } else {
                // For guest users
                $sessionId = Session::getId();
                $cart = ShoppingCart::active()
                    ->where('session_id', $sessionId)
                    ->first();
            }

            return $cart ? $cart->items()->sum('quantity') : 0;
        } catch (\Exception $e) {
            // Return 0 if there's any error (e.g., during migrations)
            return 0;
        }
    }
}
