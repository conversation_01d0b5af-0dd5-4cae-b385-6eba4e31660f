<?php

namespace Tests\Unit\Factories;

use App\Models\Project;
use App\Models\Service;
use App\Models\User;
use App\Models\Role;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ProjectFactoryTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function project_factory_creates_valid_project()
    {
        $project = Project::factory()->create();

        $this->assertInstanceOf(Project::class, $project);
        $this->assertNotNull($project->uuid);
        $this->assertNotNull($project->title);
        $this->assertNotNull($project->slug);
        $this->assertNotNull($project->description);
        $this->assertNotNull($project->status);
        $this->assertIsBool($project->is_featured);
        $this->assertIsBool($project->is_published);
        $this->assertIsBool($project->is_deleted);
    }

    /** @test */
    public function project_factory_creates_project_with_client()
    {
        $clientRole = Role::factory()->create(['name' => 'client']);
        $client = User::factory()->create(['role_id' => $clientRole->id]);
        
        $project = Project::factory()->create(['client_id' => $client->id]);

        $this->assertEquals($client->id, $project->client_id);
        $this->assertInstanceOf(User::class, $project->client);
    }

    /** @test */
    public function project_factory_creates_project_with_service()
    {
        $service = Service::factory()->create();
        
        $project = Project::factory()->create(['service_id' => $service->id]);

        $this->assertEquals($service->id, $project->service_id);
        $this->assertInstanceOf(Service::class, $project->service);
    }

    /** @test */
    public function project_factory_can_override_attributes()
    {
        $customTitle = 'Custom Project Title';
        $customStatus = 'completed';
        $customPriority = 'urgent';

        $project = Project::factory()->create([
            'title' => $customTitle,
            'status' => $customStatus,
            'priority' => $customPriority,
            'is_featured' => true,
            'is_published' => true,
        ]);

        $this->assertEquals($customTitle, $project->title);
        $this->assertEquals($customStatus, $project->status);
        $this->assertEquals($customPriority, $project->priority);
        $this->assertTrue($project->is_featured);
        $this->assertTrue($project->is_published);
    }

    /** @test */
    public function project_factory_creates_unique_slugs()
    {
        $project1 = Project::factory()->create();
        $project2 = Project::factory()->create();

        $this->assertNotEquals($project1->slug, $project2->slug);
    }

    /** @test */
    public function project_factory_creates_valid_numeric_fields()
    {
        $project = Project::factory()->create([
            'estimated_hours' => 100.50,
            'actual_hours' => 95.25,
            'hourly_rate' => 75.00,
            'total_amount' => 7143.75,
        ]);

        $this->assertEquals('100.50', $project->estimated_hours);
        $this->assertEquals('95.25', $project->actual_hours);
        $this->assertEquals('75.00', $project->hourly_rate);
        $this->assertEquals('7143.75', $project->total_amount);
    }

    /** @test */
    public function project_factory_creates_valid_dates()
    {
        $startDate = now()->subDays(30);
        $endDate = now();

        $project = Project::factory()->create([
            'start_date' => $startDate,
            'end_date' => $endDate,
        ]);

        $this->assertInstanceOf(\Carbon\Carbon::class, $project->start_date);
        $this->assertInstanceOf(\Carbon\Carbon::class, $project->end_date);
        $this->assertEquals($startDate->format('Y-m-d'), $project->start_date->format('Y-m-d'));
        $this->assertEquals($endDate->format('Y-m-d'), $project->end_date->format('Y-m-d'));
    }

    /** @test */
    public function project_factory_creates_valid_gallery_array()
    {
        $galleryData = ['image1.jpg', 'image2.jpg', 'image3.jpg'];
        
        $project = Project::factory()->create(['gallery' => $galleryData]);

        $this->assertIsArray($project->gallery);
        $this->assertEquals($galleryData, $project->gallery);
    }

    /** @test */
    public function project_factory_creates_valid_meta_fields()
    {
        $project = Project::factory()->create([
            'meta_title' => 'Custom Meta Title',
            'meta_description' => 'Custom meta description for SEO',
            'meta_keywords' => 'custom, meta, keywords',
        ]);

        $this->assertEquals('Custom Meta Title', $project->meta_title);
        $this->assertEquals('Custom meta description for SEO', $project->meta_description);
        $this->assertEquals('custom, meta, keywords', $project->meta_keywords);
    }

    /** @test */
    public function project_factory_can_create_multiple_projects()
    {
        $projects = Project::factory()->count(5)->create();

        $this->assertCount(5, $projects);
        
        foreach ($projects as $project) {
            $this->assertInstanceOf(Project::class, $project);
            $this->assertNotNull($project->title);
            $this->assertNotNull($project->slug);
        }
    }

    /** @test */
    public function project_factory_creates_projects_with_different_statuses()
    {
        $statuses = ['planning', 'in_progress', 'review', 'completed', 'on_hold', 'cancelled'];
        
        foreach ($statuses as $status) {
            $project = Project::factory()->create(['status' => $status]);
            $this->assertEquals($status, $project->status);
        }
    }

    /** @test */
    public function project_factory_creates_projects_with_different_priorities()
    {
        $priorities = ['low', 'medium', 'high', 'urgent'];
        
        foreach ($priorities as $priority) {
            $project = Project::factory()->create(['priority' => $priority]);
            $this->assertEquals($priority, $project->priority);
        }
    }

    /** @test */
    public function project_factory_handles_null_values_correctly()
    {
        $project = Project::factory()->create([
            'client_id' => null,
            'service_id' => null,
            'featured_image' => null,
            'gallery' => null,
            'project_url' => null,
            'start_date' => null,
            'end_date' => null,
            'estimated_hours' => null,
            'actual_hours' => null,
            'hourly_rate' => null,
            'total_amount' => null,
            'meta_title' => null,
            'meta_description' => null,
            'meta_keywords' => null,
        ]);

        $this->assertNull($project->client_id);
        $this->assertNull($project->service_id);
        $this->assertNull($project->featured_image);
        $this->assertNull($project->gallery);
        $this->assertNull($project->project_url);
        $this->assertNull($project->start_date);
        $this->assertNull($project->end_date);
        $this->assertNull($project->estimated_hours);
        $this->assertNull($project->actual_hours);
        $this->assertNull($project->hourly_rate);
        $this->assertNull($project->total_amount);
        $this->assertNull($project->meta_title);
        $this->assertNull($project->meta_description);
        $this->assertNull($project->meta_keywords);
    }
}
