<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class ProductVariant extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id',
        'name',
        'sku',
        'barcode',
        'price',
        'compare_price',
        'cost_price',
        'inventory_quantity',
        'weight',
        'dimensions',
        'image',
        'attributes',
        'is_active',
        'is_deleted',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'compare_price' => 'decimal:2',
        'cost_price' => 'decimal:2',
        'weight' => 'decimal:2',
        'dimensions' => 'array',
        'attributes' => 'array',
        'is_active' => 'boolean',
        'is_deleted' => 'boolean',
    ];

    /**
     * Scope a query to only include active variants.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)->where('is_deleted', false);
    }

    /**
     * Scope a query to only include variants in stock.
     */
    public function scopeInStock($query)
    {
        return $query->where('inventory_quantity', '>', 0);
    }

    /**
     * Get the product that owns the variant.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the cart items for this variant.
     */
    public function cartItems(): HasMany
    {
        return $this->hasMany(CartItem::class);
    }

    /**
     * Check if variant is in stock.
     */
    public function isInStock(): bool
    {
        return $this->inventory_quantity > 0;
    }

    /**
     * Get the formatted price.
     */
    public function getFormattedPriceAttribute(): string
    {
        return 'R' . number_format($this->price, 2);
    }

    /**
     * Get the formatted compare price.
     */
    public function getFormattedComparePriceAttribute(): string
    {
        return $this->compare_price ? 'R' . number_format($this->compare_price, 2) : '';
    }

    /**
     * Get the discount percentage.
     */
    public function getDiscountPercentageAttribute(): int
    {
        if (!$this->compare_price || $this->compare_price <= $this->price) {
            return 0;
        }

        return round((($this->compare_price - $this->price) / $this->compare_price) * 100);
    }

    /**
     * Get the variant image URL.
     */
    public function getImageUrlAttribute(): string
    {
        return $this->image ? asset('storage/' . $this->image) : $this->product->primary_image;
    }

    /**
     * Get the full variant name.
     */
    public function getFullNameAttribute(): string
    {
        return $this->product->name . ($this->name ? ' - ' . $this->name : '');
    }

    /**
     * Get the stock status.
     */
    public function getStockStatusAttribute(): string
    {
        if ($this->inventory_quantity <= 0) {
            return 'Out of Stock';
        }

        if ($this->inventory_quantity <= 5) {
            return 'Low Stock';
        }

        return 'In Stock';
    }

    /**
     * Decrease inventory quantity.
     */
    public function decreaseInventory(int $quantity): bool
    {
        if ($this->inventory_quantity < $quantity) {
            return false;
        }

        $this->decrement('inventory_quantity', $quantity);
        return true;
    }

    /**
     * Increase inventory quantity.
     */
    public function increaseInventory(int $quantity): void
    {
        $this->increment('inventory_quantity', $quantity);
    }



    /**
     * Get formatted attributes for display.
     */
    public function getFormattedAttributesAttribute(): array
    {
        if (!$this->attributes || !is_array($this->attributes)) {
            return [];
        }

        $formatted = [];
        foreach ($this->attributes as $key => $value) {
            $formatted[] = [
                'name' => ucfirst(str_replace('_', ' ', $key)),
                'value' => $value,
            ];
        }

        return $formatted;
    }
}
