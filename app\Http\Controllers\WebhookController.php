<?php

namespace App\Http\Controllers;

use App\Services\PaymentService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;

class WebhookController extends Controller
{
    /**
     * Handle Stripe webhook events
     */
    public function stripe(Request $request): Response
    {
        $payload = $request->getContent();
        $signature = $request->header('Stripe-Signature');

        $paymentService = new PaymentService();

        // Verify webhook signature
        if (!$paymentService->verifyWebhookSignature($payload, $signature)) {
            Log::warning('Invalid Stripe webhook signature');
            return response('Invalid signature', 400);
        }

        try {
            $event = json_decode($payload, true);

            Log::info('Stripe webhook received', [
                'type' => $event['type'],
                'id' => $event['id'],
            ]);

            // Handle the event
            $paymentService->handleWebhookEvent($event);

            return response('Webhook handled', 200);

        } catch (\Exception $e) {
            Log::error('Stripe webhook processing error', [
                'error' => $e->getMessage(),
                'payload' => $payload,
            ]);

            return response('Webhook processing failed', 500);
        }
    }

    /**
     * Handle PayPal webhook events (placeholder)
     */
    public function paypal(Request $request): Response
    {
        // TODO: Implement PayPal webhook handling
        Log::info('PayPal webhook received', $request->all());

        return response('PayPal webhook not implemented', 200);
    }
}
