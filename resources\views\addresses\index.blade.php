@extends('layouts.dashboard')

@section('title', 'Address Book')

@section('content')
<div class="max-w-6xl mx-auto">
    <!-- Page Header -->
    <div class="flex justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Address Book</h1>
            <p class="mt-2 text-gray-600">Manage your billing and shipping addresses</p>
        </div>
        <a href="{{ route('addresses.create') }}" 
           class="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors">
            Add New Address
        </a>
    </div>

    <!-- Success Message -->
    @if(session('success'))
        <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
            {{ session('success') }}
        </div>
    @endif

    @if($addresses->count() > 0)
        <!-- Addresses Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @foreach($addresses as $address)
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 relative">
                    <!-- Default Badge -->
                    @if($address->is_default)
                        <div class="absolute top-4 right-4">
                            <span class="bg-primary-100 text-primary-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                                Default
                            </span>
                        </div>
                    @endif

                    <div class="p-6">
                        <!-- Address Type -->
                        <div class="flex items-center mb-4">
                            <div class="flex-shrink-0">
                                @if($address->type === 'billing')
                                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"/>
                                    </svg>
                                @elseif($address->type === 'shipping')
                                    <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>
                                    </svg>
                                @else
                                    <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                                    </svg>
                                @endif
                            </div>
                            <div class="ml-3">
                                <h3 class="text-lg font-semibold text-gray-900 capitalize">{{ $address->type }} Address</h3>
                            </div>
                        </div>

                        <!-- Address Details -->
                        <div class="space-y-2 text-sm text-gray-600">
                            <div class="font-medium text-gray-900">{{ $address->full_name }}</div>
                            @if($address->company)
                                <div>{{ $address->company }}</div>
                            @endif
                            <div>{{ $address->address_line_1 }}</div>
                            @if($address->address_line_2)
                                <div>{{ $address->address_line_2 }}</div>
                            @endif
                            <div>{{ $address->city }}, {{ $address->state }} {{ $address->postal_code }}</div>
                            <div>{{ strtoupper($address->country) }}</div>
                            @if($address->phone)
                                <div class="flex items-center mt-2">
                                    <svg class="w-4 h-4 text-gray-400 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                                    </svg>
                                    {{ $address->phone }}
                                </div>
                            @endif
                        </div>

                        <!-- Actions -->
                        <div class="mt-6 flex items-center justify-between">
                            <div class="flex space-x-2">
                                <a href="{{ route('addresses.edit', $address) }}" 
                                   class="text-primary-600 hover:text-primary-800 text-sm font-medium">
                                    Edit
                                </a>
                                @if(!$address->is_default)
                                    <form action="{{ route('addresses.set-default', $address) }}" method="POST" class="inline">
                                        @csrf
                                        @method('PATCH')
                                        <button type="submit" 
                                                class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                            Set as Default
                                        </button>
                                    </form>
                                @endif
                            </div>
                            <form action="{{ route('addresses.destroy', $address) }}" method="POST" class="inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" 
                                        class="text-red-600 hover:text-red-800 text-sm font-medium"
                                        onclick="return confirm('Are you sure you want to delete this address?')">
                                    Delete
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    @else
        <!-- Empty State -->
        <div class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No addresses</h3>
            <p class="mt-1 text-sm text-gray-500">Get started by adding your first address.</p>
            <div class="mt-6">
                <a href="{{ route('addresses.create') }}" 
                   class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                    </svg>
                    Add Address
                </a>
            </div>
        </div>
    @endif

    <!-- Address Tips -->
    <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800">Address Management Tips</h3>
                <div class="mt-2 text-sm text-blue-700">
                    <ul class="list-disc list-inside space-y-1">
                        <li>Set a default address for faster checkout</li>
                        <li>Keep your addresses up to date for accurate shipping</li>
                        <li>You can have separate billing and shipping addresses</li>
                        <li>Deleted addresses cannot be recovered</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
