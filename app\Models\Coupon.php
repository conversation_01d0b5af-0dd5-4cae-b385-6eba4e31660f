<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;
use Carbon\Carbon;

class Coupon extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'code',
        'name',
        'description',
        'type',
        'value',
        'minimum_amount',
        'maximum_discount',
        'usage_limit',
        'usage_limit_per_customer',
        'used_count',
        'is_active',
        'starts_at',
        'expires_at',
        'applicable_products',
        'applicable_categories',
        'exclude_products',
        'exclude_categories',
        'is_deleted',
    ];

    protected $casts = [
        'value' => 'decimal:2',
        'minimum_amount' => 'decimal:2',
        'maximum_discount' => 'decimal:2',
        'usage_limit' => 'integer',
        'usage_limit_per_customer' => 'integer',
        'used_count' => 'integer',
        'is_active' => 'boolean',
        'is_deleted' => 'boolean',
        'starts_at' => 'datetime',
        'expires_at' => 'datetime',
        'applicable_products' => 'array',
        'applicable_categories' => 'array',
        'exclude_products' => 'array',
        'exclude_categories' => 'array',
    ];

    // Coupon types
    const TYPE_PERCENTAGE = 'percentage';
    const TYPE_FIXED_AMOUNT = 'fixed_amount';
    const TYPE_FREE_SHIPPING = 'free_shipping';

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($coupon) {
            if (empty($coupon->uuid)) {
                $coupon->uuid = Str::uuid();
            }
            if (empty($coupon->code)) {
                $coupon->code = strtoupper(Str::random(8));
            }
        });
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'uuid';
    }

    /**
     * Scope a query to only include active coupons.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
                    ->where('is_deleted', false)
                    ->where(function ($q) {
                        $q->whereNull('starts_at')
                          ->orWhere('starts_at', '<=', now());
                    })
                    ->where(function ($q) {
                        $q->whereNull('expires_at')
                          ->orWhere('expires_at', '>=', now());
                    });
    }

    /**
     * Scope a query to only include valid coupons.
     */
    public function scopeValid($query)
    {
        return $query->active()
                    ->where(function ($q) {
                        $q->whereNull('usage_limit')
                          ->orWhereRaw('used_count < usage_limit');
                    });
    }

    /**
     * Get the orders that used this coupon.
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Check if the coupon is valid.
     */
    public function isValid(): bool
    {
        if (!$this->is_active || $this->is_deleted) {
            return false;
        }

        // Check date range
        if ($this->starts_at && $this->starts_at->isFuture()) {
            return false;
        }

        if ($this->expires_at && $this->expires_at->isPast()) {
            return false;
        }

        // Check usage limit
        if ($this->usage_limit && $this->used_count >= $this->usage_limit) {
            return false;
        }

        return true;
    }

    /**
     * Check if the coupon can be used by a specific user.
     */
    public function canBeUsedBy(User $user): bool
    {
        if (!$this->isValid()) {
            return false;
        }

        // Check per-customer usage limit
        if ($this->usage_limit_per_customer) {
            $userUsageCount = Order::where('user_id', $user->id)
                                  ->where('coupon_id', $this->id)
                                  ->count();
            
            if ($userUsageCount >= $this->usage_limit_per_customer) {
                return false;
            }
        }

        return true;
    }

    /**
     * Calculate discount for a given amount.
     */
    public function calculateDiscount(float $amount, array $cartItems = []): float
    {
        if (!$this->isValid()) {
            return 0;
        }

        // Check minimum amount
        if ($this->minimum_amount && $amount < $this->minimum_amount) {
            return 0;
        }

        // Check product/category restrictions
        if (!$this->isApplicableToCart($cartItems)) {
            return 0;
        }

        $discount = 0;

        switch ($this->type) {
            case self::TYPE_PERCENTAGE:
                $discount = ($amount * $this->value) / 100;
                break;
            
            case self::TYPE_FIXED_AMOUNT:
                $discount = $this->value;
                break;
            
            case self::TYPE_FREE_SHIPPING:
                // This would be handled in shipping calculation
                $discount = 0;
                break;
        }

        // Apply maximum discount limit
        if ($this->maximum_discount && $discount > $this->maximum_discount) {
            $discount = $this->maximum_discount;
        }

        // Don't exceed the total amount
        return min($discount, $amount);
    }

    /**
     * Check if coupon is applicable to cart items.
     */
    private function isApplicableToCart(array $cartItems): bool
    {
        if (empty($cartItems)) {
            return true;
        }

        $hasApplicableItems = false;

        foreach ($cartItems as $item) {
            $productId = $item['product_id'] ?? null;
            $categoryIds = $item['category_ids'] ?? [];

            // Check if product is excluded
            if ($this->exclude_products && in_array($productId, $this->exclude_products)) {
                continue;
            }

            // Check if any category is excluded
            if ($this->exclude_categories && array_intersect($categoryIds, $this->exclude_categories)) {
                continue;
            }

            // Check if product is specifically included
            if ($this->applicable_products && !in_array($productId, $this->applicable_products)) {
                continue;
            }

            // Check if any category is specifically included
            if ($this->applicable_categories && !array_intersect($categoryIds, $this->applicable_categories)) {
                continue;
            }

            $hasApplicableItems = true;
            break;
        }

        return $hasApplicableItems;
    }

    /**
     * Mark coupon as used.
     */
    public function markAsUsed(): void
    {
        $this->increment('used_count');
    }

    /**
     * Get formatted discount value.
     */
    public function getFormattedValueAttribute(): string
    {
        switch ($this->type) {
            case self::TYPE_PERCENTAGE:
                return $this->value . '%';
            case self::TYPE_FIXED_AMOUNT:
                return 'R' . number_format($this->value, 2);
            case self::TYPE_FREE_SHIPPING:
                return 'Free Shipping';
            default:
                return '';
        }
    }

    /**
     * Get coupon status.
     */
    public function getStatusAttribute(): string
    {
        if (!$this->is_active || $this->is_deleted) {
            return 'Inactive';
        }

        if ($this->starts_at && $this->starts_at->isFuture()) {
            return 'Scheduled';
        }

        if ($this->expires_at && $this->expires_at->isPast()) {
            return 'Expired';
        }

        if ($this->usage_limit && $this->used_count >= $this->usage_limit) {
            return 'Used Up';
        }

        return 'Active';
    }
}
