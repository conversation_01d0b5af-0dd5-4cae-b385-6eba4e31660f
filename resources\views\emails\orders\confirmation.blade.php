@extends('emails.layout')

@section('title', 'Order Confirmation - ' . $order->order_number)

@section('content')
<div class="greeting">
    Thank you for your order, {{ $customer_name }}!
</div>

<p class="content-text">
    We've received your order and are preparing it for processing. Here are the details:
</p>

<!-- Order Summary Box -->
<div style="background-color: #f7fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px; margin: 20px 0;">
    <h3 style="margin: 0 0 15px 0; color: #2d3748; font-size: 18px;">Order Summary</h3>
    
    <table style="width: 100%; border-collapse: collapse;">
        <tr>
            <td style="padding: 8px 0; color: #4a5568; font-weight: 600;">Order Number:</td>
            <td style="padding: 8px 0; color: #2d3748; text-align: right;">#{{ $order->order_number }}</td>
        </tr>
        <tr>
            <td style="padding: 8px 0; color: #4a5568; font-weight: 600;">Order Date:</td>
            <td style="padding: 8px 0; color: #2d3748; text-align: right;">{{ $order->created_at->format('M d, Y') }}</td>
        </tr>
        <tr>
            <td style="padding: 8px 0; color: #4a5568; font-weight: 600;">Payment Status:</td>
            <td style="padding: 8px 0; text-align: right;">
                <span style="background-color: {{ $order->payment_status === 'paid' ? '#48bb78' : '#ed8936' }}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; text-transform: uppercase;">
                    {{ ucfirst($order->payment_status) }}
                </span>
            </td>
        </tr>
        <tr style="border-top: 1px solid #e2e8f0;">
            <td style="padding: 12px 0 8px 0; color: #2d3748; font-weight: 700; font-size: 16px;">Total Amount:</td>
            <td style="padding: 12px 0 8px 0; color: #2d3748; font-weight: 700; font-size: 16px; text-align: right;">{{ $order->formatted_total }}</td>
        </tr>
    </table>
</div>

<!-- Order Items -->
<h3 style="color: #2d3748; margin: 30px 0 15px 0;">Items Ordered</h3>

@foreach($order->items as $item)
<div style="border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; margin-bottom: 10px; background-color: #ffffff;">
    <table style="width: 100%;">
        <tr>
            <td style="width: 70%;">
                <h4 style="margin: 0 0 5px 0; color: #2d3748; font-size: 16px;">{{ $item->product_name }}</h4>
                @if($item->variant_name)
                <p style="margin: 0 0 5px 0; color: #718096; font-size: 14px;">{{ $item->variant_name }}</p>
                @endif
                <p style="margin: 0; color: #4a5568; font-size: 14px;">Quantity: {{ $item->quantity }}</p>
            </td>
            <td style="width: 30%; text-align: right; vertical-align: top;">
                <p style="margin: 0; color: #2d3748; font-weight: 600; font-size: 16px;">{{ $item->formatted_total_price }}</p>
                <p style="margin: 5px 0 0 0; color: #718096; font-size: 14px;">{{ $item->formatted_unit_price }} each</p>
            </td>
        </tr>
    </table>
</div>
@endforeach

<!-- Order Totals -->
<div style="background-color: #f7fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px; margin: 20px 0;">
    <table style="width: 100%; border-collapse: collapse;">
        <tr>
            <td style="padding: 5px 0; color: #4a5568;">Subtotal:</td>
            <td style="padding: 5px 0; color: #2d3748; text-align: right;">{{ $order->formatted_subtotal }}</td>
        </tr>
        
        @if($order->discount_amount > 0)
        <tr>
            <td style="padding: 5px 0; color: #38a169;">
                Discount @if($order->coupon_code)({{ $order->coupon_code }})@endif:
            </td>
            <td style="padding: 5px 0; color: #38a169; text-align: right;">-{{ $order->formatted_discount_amount }}</td>
        </tr>
        @endif
        
        <tr>
            <td style="padding: 5px 0; color: #4a5568;">Shipping:</td>
            <td style="padding: 5px 0; color: #2d3748; text-align: right;">{{ $order->formatted_shipping_amount }}</td>
        </tr>
        
        <tr>
            <td style="padding: 5px 0; color: #4a5568;">Tax:</td>
            <td style="padding: 5px 0; color: #2d3748; text-align: right;">{{ $order->formatted_tax_amount }}</td>
        </tr>
        
        <tr style="border-top: 2px solid #2d3748;">
            <td style="padding: 10px 0 5px 0; color: #2d3748; font-weight: 700; font-size: 18px;">Total:</td>
            <td style="padding: 10px 0 5px 0; color: #2d3748; font-weight: 700; font-size: 18px; text-align: right;">{{ $order->formatted_total }}</td>
        </tr>
    </table>
</div>

<!-- Shipping Address -->
<h3 style="color: #2d3748; margin: 30px 0 15px 0;">Shipping Address</h3>
<div style="background-color: #f7fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
    <p style="margin: 0; color: #2d3748; line-height: 1.5;">
        <strong>{{ $order->shipping_first_name }} {{ $order->shipping_last_name }}</strong><br>
        {{ $order->shipping_address_line_1 }}<br>
        @if($order->shipping_address_line_2)
        {{ $order->shipping_address_line_2 }}<br>
        @endif
        {{ $order->shipping_city }}, {{ $order->shipping_state }} {{ $order->shipping_postal_code }}<br>
        {{ $order->shipping_country }}
        @if($order->shipping_phone)
        <br><br><strong>Phone:</strong> {{ $order->shipping_phone }}
        @endif
    </p>
</div>

<!-- Action Buttons -->
<div class="button-container">
    @if($order->user_id)
    <a href="{{ route('orders.show', $order->uuid) }}" class="btn">
        View Order Details
    </a>
    @endif
    
    <a href="{{ route('shop.index') }}" class="btn btn-secondary" style="margin-left: 10px;">
        Continue Shopping
    </a>
</div>

<!-- Next Steps -->
<div class="info-box">
    <p><strong>What's Next?</strong></p>
    <p>
        @if($order->payment_status === 'pending')
        Please complete your payment to begin processing your order. You can do this by clicking the "View Order Details" button above.
        @else
        Your order is now being processed and will be shipped within 1-2 business days. You'll receive a tracking number via email once your order ships.
        @endif
    </p>
</div>

<p class="content-text">
    If you have any questions about your order, please don't hesitate to contact our support team. We're here to help!
</p>

<p class="content-text">
    Thank you for choosing {{ $company_name }}!
</p>
@endsection
