<?php

namespace Tests\Feature;

use App\Models\Coupon;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\ShoppingCart;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class CouponSystemTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $product;
    protected $category;
    protected $cart;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create();

        // Create test category
        $this->category = ProductCategory::factory()->create([
            'name' => 'Test Category',
            'is_active' => true,
        ]);

        // Create test product
        $this->product = Product::factory()->create([
            'name' => 'Test Product',
            'price' => 100.00,
            'is_active' => true,
            'track_inventory' => false,
        ]);

        // Attach product to category
        $this->product->categories()->attach($this->category);

        // Create shopping cart
        $this->cart = ShoppingCart::factory()->create([
            'user_id' => $this->user->id,
            'subtotal' => 100.00,
            'total' => 100.00,
        ]);

        // Add item to cart
        $this->cart->items()->create([
            'product_id' => $this->product->id,
            'quantity' => 1,
            'unit_price' => 100.00,
            'total' => 100.00,
        ]);
    }

    /** @test */
    public function it_can_apply_percentage_coupon()
    {
        $coupon = Coupon::factory()->create([
            'code' => 'TEST10',
            'type' => 'percentage',
            'value' => 10,
            'is_active' => true,
        ]);

        $this->actingAs($this->user)
            ->postJson('/cart/coupon', ['coupon_code' => 'TEST10'])
            ->assertJson([
                'success' => true,
                'message' => 'Coupon applied successfully!',
            ]);

        $this->cart->refresh();
        $this->assertEquals('TEST10', $this->cart->coupon_code);
        $this->assertEquals(10.00, $this->cart->discount_amount);
        $this->assertEquals(90.00, $this->cart->total);
    }

    /** @test */
    public function it_can_apply_fixed_amount_coupon()
    {
        $coupon = Coupon::factory()->create([
            'code' => 'SAVE20',
            'type' => 'fixed_amount',
            'value' => 20,
            'is_active' => true,
        ]);

        $this->actingAs($this->user)
            ->postJson('/cart/coupon', ['coupon_code' => 'SAVE20'])
            ->assertJson([
                'success' => true,
                'message' => 'Coupon applied successfully!',
            ]);

        $this->cart->refresh();
        $this->assertEquals('SAVE20', $this->cart->coupon_code);
        $this->assertEquals(20.00, $this->cart->discount_amount);
        $this->assertEquals(80.00, $this->cart->total);
    }

    /** @test */
    public function it_respects_minimum_order_amount()
    {
        $coupon = Coupon::factory()->create([
            'code' => 'MIN150',
            'type' => 'percentage',
            'value' => 15,
            'minimum_amount' => 150.00,
            'is_active' => true,
        ]);

        $this->actingAs($this->user)
            ->postJson('/cart/coupon', ['coupon_code' => 'MIN150'])
            ->assertJson([
                'success' => false,
                'message' => 'This coupon is not applicable to items in your cart.',
            ]);

        $this->cart->refresh();
        $this->assertNull($this->cart->coupon_code);
        $this->assertEquals(0, $this->cart->discount_amount);
    }

    /** @test */
    public function it_respects_maximum_discount_for_percentage_coupons()
    {
        // Update cart to have higher value
        $this->cart->update(['subtotal' => 500.00, 'total' => 500.00]);
        $this->cart->items()->first()->update(['total' => 500.00, 'unit_price' => 500.00]);

        $coupon = Coupon::factory()->create([
            'code' => 'BIG50',
            'type' => 'percentage',
            'value' => 50,
            'maximum_discount' => 100.00,
            'is_active' => true,
        ]);

        $this->actingAs($this->user)
            ->postJson('/cart/coupon', ['coupon_code' => 'BIG50'])
            ->assertJson([
                'success' => true,
                'message' => 'Coupon applied successfully!',
            ]);

        $this->cart->refresh();
        $this->assertEquals('BIG50', $this->cart->coupon_code);
        $this->assertEquals(100.00, $this->cart->discount_amount); // Capped at max discount
        $this->assertEquals(400.00, $this->cart->total);
    }

    /** @test */
    public function it_rejects_invalid_coupon_codes()
    {
        $this->actingAs($this->user)
            ->postJson('/cart/coupon', ['coupon_code' => 'INVALID'])
            ->assertJson([
                'success' => false,
                'message' => 'Invalid coupon code.',
            ]);

        $this->cart->refresh();
        $this->assertNull($this->cart->coupon_code);
        $this->assertEquals(0, $this->cart->discount_amount);
    }

    /** @test */
    public function it_rejects_inactive_coupons()
    {
        $coupon = Coupon::factory()->create([
            'code' => 'INACTIVE',
            'type' => 'percentage',
            'value' => 10,
            'is_active' => false,
        ]);

        $this->actingAs($this->user)
            ->postJson('/cart/coupon', ['coupon_code' => 'INACTIVE'])
            ->assertJson([
                'success' => false,
                'message' => 'Invalid coupon code.',
            ]);
    }

    /** @test */
    public function it_rejects_expired_coupons()
    {
        $coupon = Coupon::factory()->create([
            'code' => 'EXPIRED',
            'type' => 'percentage',
            'value' => 10,
            'is_active' => true,
            'expires_at' => now()->subDay(),
        ]);

        $this->actingAs($this->user)
            ->postJson('/cart/coupon', ['coupon_code' => 'EXPIRED'])
            ->assertJson([
                'success' => false,
                'message' => 'This coupon is no longer valid.',
            ]);
    }

    /** @test */
    public function it_can_remove_applied_coupon()
    {
        $coupon = Coupon::factory()->create([
            'code' => 'REMOVE10',
            'type' => 'percentage',
            'value' => 10,
            'is_active' => true,
        ]);

        // Apply coupon first
        $this->cart->update([
            'coupon_id' => $coupon->uuid,
            'coupon_code' => $coupon->code,
            'discount_amount' => 10.00,
            'total' => 90.00,
        ]);

        $this->actingAs($this->user)
            ->deleteJson('/cart/coupon')
            ->assertJson([
                'success' => true,
                'message' => 'Coupon removed successfully.',
            ]);

        $this->cart->refresh();
        $this->assertNull($this->cart->coupon_code);
        $this->assertEquals(0, $this->cart->discount_amount);
        $this->assertEquals(100.00, $this->cart->total);
    }

    /** @test */
    public function it_rejects_coupon_application_on_empty_cart()
    {
        // Clear cart items
        $this->cart->items()->delete();

        $coupon = Coupon::factory()->create([
            'code' => 'EMPTY10',
            'type' => 'percentage',
            'value' => 10,
            'is_active' => true,
        ]);

        $this->actingAs($this->user)
            ->postJson('/cart/coupon', ['coupon_code' => 'EMPTY10'])
            ->assertJson([
                'success' => false,
                'message' => 'Your cart is empty.',
            ]);
    }

    /** @test */
    public function it_validates_coupon_code_format()
    {
        $this->actingAs($this->user)
            ->postJson('/cart/coupon', ['coupon_code' => ''])
            ->assertJsonValidationErrors(['coupon_code']);

        $this->actingAs($this->user)
            ->postJson('/cart/coupon', ['coupon_code' => str_repeat('A', 51)])
            ->assertJsonValidationErrors(['coupon_code']);
    }
}
