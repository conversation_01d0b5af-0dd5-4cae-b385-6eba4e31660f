<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\Language;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;

class LocalizationMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $locale = $this->getLocale($request);

        // Set the application locale
        App::setLocale($locale);

        // Store in session for persistence
        Session::put('locale', $locale);

        return $next($request);
    }

    /**
     * Get the locale from various sources.
     */
    private function getLocale(Request $request): string
    {
        // 1. Check URL prefix (e.g., /fr/page, /es/page)
        $segments = $request->segments();
        if (!empty($segments)) {
            $firstSegment = $segments[0];
            if (in_array($firstSegment, ['en', 'fr', 'es'])) {
                $language = Language::findByCode($firstSegment);
                if ($language) {
                    return $firstSegment;
                }
            }
        }

        // 2. Check query parameter (?lang=fr)
        if ($request->has('lang')) {
            $langCode = $request->get('lang');
            $language = Language::findByCode($langCode);
            if ($language) {
                return $langCode;
            }
        }

        // 3. Check session
        if (Session::has('locale')) {
            $sessionLocale = Session::get('locale');
            $language = Language::findByCode($sessionLocale);
            if ($language) {
                return $sessionLocale;
            }
        }

        // 4. Check user preference (if authenticated)
        if ($request->user() && isset($request->user()->preferred_language)) {
            $userLocale = $request->user()->preferred_language;
            $language = Language::findByCode($userLocale);
            if ($language) {
                return $userLocale;
            }
        }

        // 5. Check Accept-Language header
        $acceptLanguage = $request->header('Accept-Language');
        if ($acceptLanguage) {
            $preferredLanguages = $this->parseAcceptLanguage($acceptLanguage);
            foreach ($preferredLanguages as $langCode) {
                $language = Language::findByCode($langCode);
                if ($language) {
                    return $langCode;
                }
            }
        }

        // 6. Default to English
        return 'en';
    }

    /**
     * Parse Accept-Language header.
     */
    private function parseAcceptLanguage(string $acceptLanguage): array
    {
        $languages = [];
        $parts = explode(',', $acceptLanguage);

        foreach ($parts as $part) {
            $part = trim($part);
            if (strpos($part, ';') !== false) {
                $part = explode(';', $part)[0];
            }
            if (strpos($part, '-') !== false) {
                $part = explode('-', $part)[0];
            }
            $languages[] = strtolower($part);
        }

        return array_unique($languages);
    }
}
