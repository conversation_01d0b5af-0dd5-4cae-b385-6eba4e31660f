@extends('layouts.app')

@section('title', ($product->meta_title ?: $product->name) . ' - ' . __('common.company_name'))
@section('meta_description', $product->meta_description ?: $product->short_description ?: Str::limit(strip_tags($product->description), 160))
@section('meta_keywords', $product->meta_keywords ?: $product->categories->pluck('name')->implode(', '))

@section('og_title', ($product->meta_title ?: $product->name) . ' - ' . __('common.company_name'))
@section('og_description', $product->meta_description ?: $product->short_description ?: Str::limit(strip_tags($product->description), 160))
@section('og_image', $product->featured_image ? asset('storage/' . $product->featured_image) : asset('images/og-image.jpg'))

@section('twitter_title', ($product->meta_title ?: $product->name) . ' - ' . __('common.company_name'))
@section('twitter_description', $product->meta_description ?: $product->short_description ?: Str::limit(strip_tags($product->description), 160))
@section('twitter_image', $product->featured_image ? asset('storage/' . $product->featured_image) : asset('images/twitter-image.jpg'))

@section('content')
<!-- Product Detail -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <!-- Product Images -->
            <div>
                <div class="bg-white rounded-lg shadow-sm p-8">
                    <!-- Main Image -->
                    <div class="mb-4">
                        <img id="main-image" src="{{ $product->featured_image ? asset('storage/' . $product->featured_image) : $product->primary_image }}"
                             alt="{{ $product->name }}"
                             class="w-full h-96 object-cover rounded-lg">
                    </div>

                    <!-- Image Gallery -->
                    @if($product->gallery && count($product->gallery) > 0)
                        <div class="grid grid-cols-4 gap-2">
                            <!-- Featured Image Thumbnail -->
                            @if($product->featured_image)
                                <button type="button"
                                        class="gallery-thumb border-2 border-blue-500 rounded-lg overflow-hidden"
                                        onclick="changeMainImage('{{ asset('storage/' . $product->featured_image) }}', this)">
                                    <img src="{{ asset('storage/' . $product->featured_image) }}"
                                         alt="{{ $product->name }}"
                                         class="w-full h-20 object-cover">
                                </button>
                            @endif

                            <!-- Gallery Images Thumbnails -->
                            @foreach($product->gallery as $image)
                                <button type="button"
                                        class="gallery-thumb border-2 border-gray-200 hover:border-blue-500 rounded-lg overflow-hidden transition-colors"
                                        onclick="changeMainImage('{{ asset('storage/' . $image) }}', this)">
                                    <img src="{{ asset('storage/' . $image) }}"
                                         alt="{{ $product->name }}"
                                         class="w-full h-20 object-cover">
                                </button>
                            @endforeach
                        </div>
                    @endif
                </div>

                <!-- Product Features -->
                @if($product->is_featured)
                    <div class="mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-yellow-500 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"/>
                            </svg>
                            <span class="text-yellow-800 font-medium">Featured Product</span>
                        </div>
                    </div>
                @endif
            </div>
            
            <!-- Product Info -->
            <div>
                <!-- Breadcrumb -->
                <nav class="mb-4">
                    <ol class="flex items-center space-x-2 text-sm text-gray-500">
                        <li><a href="{{ route('shop.index') }}" class="hover:text-blue-600">Shop</a></li>
                        @if($product->categories->first())
                            <li><span class="mx-2">/</span></li>
                            <li><a href="{{ route('shop.category', $product->categories->first()->slug) }}" class="hover:text-blue-600">{{ $product->categories->first()->name }}</a></li>
                        @endif
                        <li><span class="mx-2">/</span></li>
                        <li class="text-gray-900">{{ $product->name }}</li>
                    </ol>
                </nav>

                <!-- Categories -->
                <div class="mb-4">
                    @foreach($product->categories as $category)
                    <a href="{{ route('shop.category', $category->slug) }}"
                       class="inline-block px-3 py-1 bg-blue-100 text-blue-600 text-sm rounded-full mr-2 hover:bg-blue-200 transition-colors">
                        {{ $category->name }}
                    </a>
                    @endforeach
                </div>

                <!-- Product Title -->
                <h1 class="heading-1 text-gray-900 mb-4">{{ $product->name }}</h1>

                <!-- Product Meta Info -->
                <div class="flex items-center space-x-4 mb-6 text-sm text-gray-600">
                    @if($product->sku)
                        <span><strong>SKU:</strong> {{ $product->sku }}</span>
                    @endif
                    @if($product->brand)
                        <span><strong>Brand:</strong> {{ $product->brand }}</span>
                    @endif
                    @if($product->model_number)
                        <span><strong>Model:</strong> {{ $product->model_number }}</span>
                    @endif
                </div>
                
                <div class="flex items-center space-x-4 mb-6">
                    <span class="text-3xl font-bold text-gray-900">{{ $product->formatted_price }}</span>
                    @if($product->compare_price)
                    <span class="text-xl text-gray-500 line-through">{{ $product->formatted_compare_price }}</span>
                    <span class="px-2 py-1 bg-red-100 text-red-600 text-sm rounded">
                        Save {{ $product->discount_percentage }}%
                    </span>
                    @endif
                </div>
                
                @if($product->short_description)
                <div class="text-lead text-gray-600 mb-6 prose prose-gray max-w-none">
                    {!! $product->short_description !!}
                </div>
                @endif

                <div class="mb-8">
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Description</h3>
                    <div class="prose prose-gray max-w-none">
                        {!! $product->description !!}
                    </div>
                </div>
                
                <!-- Add to Cart Form -->
                <form id="add-to-cart-form" class="space-y-6">
                    @csrf
                    <input type="hidden" name="product_id" value="{{ $product->id }}">
                    
                    @if($product->variants->count() > 0)
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Select Option</label>
                        <select name="variant_id" class="form-input">
                            @foreach($product->variants as $variant)
                            <option value="{{ $variant->id }}" {{ !$variant->isInStock() ? 'disabled' : '' }}>
                                {{ $variant->name }} - {{ $variant->formatted_price }}
                                @if(!$variant->isInStock()) (Out of Stock) @endif
                            </option>
                            @endforeach
                        </select>
                    </div>
                    @endif
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Quantity</label>
                        <div class="flex items-center space-x-3">
                            <button type="button" class="quantity-btn decrease-qty px-3 py-2 border border-gray-300 rounded-l-lg hover:bg-gray-50">-</button>
                            <input type="number" name="quantity" value="1" min="1" max="10" 
                                   class="quantity-input w-20 px-3 py-2 border-t border-b border-gray-300 text-center focus:ring-0 focus:border-gray-300">
                            <button type="button" class="quantity-btn increase-qty px-3 py-2 border border-gray-300 rounded-r-lg hover:bg-gray-50">+</button>
                        </div>
                    </div>
                    
                    <div class="space-y-3">
                        @if($product->isInStock())
                        <button type="submit" class="w-full btn-primary">
                            Add to Cart
                            <svg class="ml-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"></path>
                            </svg>
                        </button>
                        @else
                        <div class="w-full bg-gray-300 text-gray-500 px-6 py-3 rounded-lg text-center">
                            Out of Stock
                        </div>
                        @endif
                        
                        <div class="text-center">
                            <span class="text-sm text-gray-500">
                                Stock Status: <span class="font-medium">{{ $product->stock_status }}</span>
                            </span>
                        </div>
                    </div>
                </form>
                
                <!-- Product Details -->
                <div class="mt-8 pt-8 border-t border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Product Details</h3>
                    <dl class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        @if($product->sku)
                            <div class="flex flex-col">
                                <dt class="text-sm font-medium text-gray-500 mb-1">SKU</dt>
                                <dd class="text-sm text-gray-900 font-mono bg-gray-50 px-2 py-1 rounded">{{ $product->sku }}</dd>
                            </div>
                        @endif

                        @if($product->brand)
                            <div class="flex flex-col">
                                <dt class="text-sm font-medium text-gray-500 mb-1">Brand</dt>
                                <dd class="text-sm text-gray-900">{{ $product->brand }}</dd>
                            </div>
                        @endif

                        @if($product->model_number)
                            <div class="flex flex-col">
                                <dt class="text-sm font-medium text-gray-500 mb-1">Model</dt>
                                <dd class="text-sm text-gray-900">{{ $product->model_number }}</dd>
                            </div>
                        @endif

                        @if($product->barcode)
                            <div class="flex flex-col">
                                <dt class="text-sm font-medium text-gray-500 mb-1">Barcode</dt>
                                <dd class="text-sm text-gray-900 font-mono">{{ $product->barcode }}</dd>
                            </div>
                        @endif

                        @if($product->weight)
                            <div class="flex flex-col">
                                <dt class="text-sm font-medium text-gray-500 mb-1">Weight</dt>
                                <dd class="text-sm text-gray-900">{{ $product->weight }}g</dd>
                            </div>
                        @endif

                        @if($product->dimensions && ($product->dimensions['length'] || $product->dimensions['width'] || $product->dimensions['height']))
                            <div class="flex flex-col">
                                <dt class="text-sm font-medium text-gray-500 mb-1">Dimensions (L×W×H)</dt>
                                <dd class="text-sm text-gray-900">
                                    {{ $product->dimensions['length'] ?? '0' }} ×
                                    {{ $product->dimensions['width'] ?? '0' }} ×
                                    {{ $product->dimensions['height'] ?? '0' }} cm
                                </dd>
                            </div>
                        @endif

                        @if($product->track_inventory)
                            <div class="flex flex-col">
                                <dt class="text-sm font-medium text-gray-500 mb-1">Stock Quantity</dt>
                                <dd class="text-sm text-gray-900">
                                    {{ $product->inventory_quantity }} units
                                    @if($product->inventory_quantity <= $product->low_stock_threshold)
                                        <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            Low Stock
                                        </span>
                                    @endif
                                </dd>
                            </div>
                        @endif

                        <div class="flex flex-col md:col-span-2">
                            <dt class="text-sm font-medium text-gray-500 mb-1">Categories</dt>
                            <dd class="text-sm text-gray-900">
                                @foreach($product->categories as $category)
                                    <a href="{{ route('shop.category', $category->slug) }}"
                                       class="inline-block px-2 py-1 bg-blue-100 text-blue-600 text-xs rounded mr-1 mb-1 hover:bg-blue-200 transition-colors">
                                        {{ $category->name }}
                                    </a>
                                @endforeach
                            </dd>
                        </div>
                    </dl>
                </div>
            </div>
        </div>
        
        <!-- Related Products -->
        @if($relatedProducts->count() > 0)
        <div class="mt-20">
            <h2 class="heading-2 text-center mb-12">
                Related <span class="text-blue-600">Products</span>
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                @foreach($relatedProducts as $relatedProduct)
                <div class="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                    <div class="relative">
                        <a href="{{ route('shop.product', $relatedProduct->slug) }}">
                            <img src="{{ $relatedProduct->primary_image }}" alt="{{ $relatedProduct->name }}" 
                                 class="w-full h-48 object-cover">
                        </a>
                        
                        @if($relatedProduct->discount_percentage > 0)
                        <div class="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-medium">
                            -{{ $relatedProduct->discount_percentage }}%
                        </div>
                        @endif
                    </div>
                    
                    <div class="p-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">
                            <a href="{{ route('shop.product', $relatedProduct->slug) }}" class="hover:text-blue-600 transition-colors">
                                {{ $relatedProduct->name }}
                            </a>
                        </h3>
                        
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <span class="text-lg font-bold text-gray-900">{{ $relatedProduct->formatted_price }}</span>
                                @if($relatedProduct->compare_price)
                                <span class="text-sm text-gray-500 line-through">{{ $relatedProduct->formatted_compare_price }}</span>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
        @endif
    </div>
</section>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Quantity controls
    const quantityInput = document.querySelector('.quantity-input');
    const decreaseBtn = document.querySelector('.decrease-qty');
    const increaseBtn = document.querySelector('.increase-qty');
    
    decreaseBtn.addEventListener('click', function() {
        const currentValue = parseInt(quantityInput.value);
        if (currentValue > 1) {
            quantityInput.value = currentValue - 1;
        }
    });
    
    increaseBtn.addEventListener('click', function() {
        const currentValue = parseInt(quantityInput.value);
        if (currentValue < 10) {
            quantityInput.value = currentValue + 1;
        }
    });
    
    // Add to cart form
    const addToCartForm = document.getElementById('add-to-cart-form');
    if (addToCartForm) {
        addToCartForm.addEventListener('submit', function(e) {
            e.preventDefault();

            console.log('🛒 Add to cart form submitted');

            const formData = new FormData(this);
            const submitButton = this.querySelector('button[type="submit"]');
            const originalText = submitButton.innerHTML;

            // Log form data
            console.log('📝 Form data:', Object.fromEntries(formData));

            submitButton.innerHTML = `
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Adding...
            `;
            submitButton.disabled = true;

            console.log('🚀 Sending request to /cart/add');

            fetch('/cart/add', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: formData
            })
            .then(response => {
                console.log('📡 Response received:', response.status, response.statusText);
                return response.json();
            })
            .then(data => {
                console.log('📦 Response data:', data);
                if (data.success) {
                    showNotification(data.message, 'success');

                    // Update cart count in header
                    updateCartCount(data.cart_count || data.cart.item_count);

                    // Change button to "View Cart"
                    submitButton.innerHTML = `
                        <svg class="mr-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"></path>
                        </svg>
                        View Cart
                    `;
                    submitButton.disabled = false;

                    // Change button behavior to redirect to cart
                    submitButton.onclick = function(e) {
                        e.preventDefault();
                        window.location.href = '/cart';
                    };

                    // Remove form submit listener to prevent double submission
                    addToCartForm.removeEventListener('submit', arguments.callee);
                } else {
                    console.error('❌ Cart add failed:', data.message);
                    showNotification(data.message, 'error');
                    submitButton.innerHTML = originalText;
                    submitButton.disabled = false;
                }
            })
            .catch(error => {
                console.error('💥 Cart add error:', error);
                showNotification('An error occurred. Please try again.', 'error');
                submitButton.innerHTML = originalText;
                submitButton.disabled = false;
            });
        });
    }
});

// Note: updateCartCount and showNotification functions are now loaded globally from cart-utils.js
</script>
@endpush
@endsection

@push('structured_data')
<script type="application/ld+json">
{
    "@context": "https://schema.org/",
    "@type": "Product",
    "name": "{{ $product->name }}",
    "description": "{{ $product->meta_description ?: $product->short_description ?: strip_tags($product->description) }}",
    "image": [
        @if($product->featured_image)
            "{{ asset('storage/' . $product->featured_image) }}"
            @if($product->gallery && count($product->gallery) > 0),@endif
        @endif
        @if($product->gallery && count($product->gallery) > 0)
            @foreach($product->gallery as $image)
                "{{ asset('storage/' . $image) }}"@if(!$loop->last),@endif
            @endforeach
        @endif
    ],
    @if($product->sku)
    "sku": "{{ $product->sku }}",
    @endif
    @if($product->brand)
    "brand": {
        "@type": "Brand",
        "name": "{{ $product->brand }}"
    },
    @endif
    @if($product->model_number)
    "model": "{{ $product->model_number }}",
    @endif
    "offers": {
        "@type": "Offer",
        "url": "{{ route('shop.product', $product->slug) }}",
        "priceCurrency": "ZAR",
        "price": "{{ $product->price }}",
        @if($product->compare_price)
        "priceValidUntil": "{{ now()->addYear()->format('Y-m-d') }}",
        @endif
        "availability": "{{ $product->isInStock() ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock' }}",
        "seller": {
            "@type": "Organization",
            "name": "{{ __('common.company_name') }}"
        }
    },
    "category": "{{ $product->categories->first()->name ?? 'Products' }}",
    @if($product->weight)
    "weight": {
        "@type": "QuantitativeValue",
        "value": "{{ $product->weight }}",
        "unitCode": "GRM"
    },
    @endif
    "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "5",
        "reviewCount": "1"
    }
}
</script>
@endpush

@push('scripts')
<script>
// Image gallery functionality
function changeMainImage(imageSrc, thumbElement) {
    document.getElementById('main-image').src = imageSrc;

    // Update thumbnail borders
    document.querySelectorAll('.gallery-thumb').forEach(thumb => {
        thumb.classList.remove('border-blue-500');
        thumb.classList.add('border-gray-200');
    });

    thumbElement.classList.remove('border-gray-200');
    thumbElement.classList.add('border-blue-500');
}

// Quantity controls


// Notification function
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    const bgColor = {
        'success': 'bg-green-500',
        'error': 'bg-red-500',
        'warning': 'bg-yellow-500',
        'info': 'bg-blue-500'
    }[type] || 'bg-blue-500';

    notification.className = `fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-transform duration-300 translate-x-full`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Slide in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Slide out and remove
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}
</script>
@endpush
