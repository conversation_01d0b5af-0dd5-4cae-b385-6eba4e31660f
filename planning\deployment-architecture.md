# Deployment Architecture & CI/CD Pipeline
## ChiSolution Digital Agency Platform

### 🏗️ Infrastructure Overview

#### **Hosting Environment: Shared cPanel Hosting**
```
Production Environment:
├── Web Server: Apache/Nginx
├── PHP Version: 8.2+
├── Database: MySQL 8.0+
├── SSL Certificate: Let's Encrypt
├── CDN: Cloudflare
├── Email: cPanel Email
├── Backup: Daily automated backups
└── Monitoring: cPanel metrics + custom monitoring
```

#### **Environment Structure**
```
┌─────────────────────────────────────────────────────────────┐
│                    PRODUCTION                               │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Web App   │  │   Database  │  │   Storage   │         │
│  │  (Laravel)  │  │   (MySQL)   │  │   (Files)   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                     STAGING                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Web App   │  │   Database  │  │   Storage   │         │
│  │  (Laravel)  │  │   (MySQL)   │  │   (Files)   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   DEVELOPMENT                               │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Local     │  │   Local     │  │   Local     │         │
│  │   Server    │  │   Database  │  │   Storage   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

### 🚀 CI/CD Pipeline

#### **GitHub Actions Workflow**
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: chisolution_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
    - uses: actions/checkout@v3
    
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'
        extensions: mbstring, xml, ctype, iconv, intl, pdo_mysql, gd, zip
        coverage: xdebug
        
    - name: Cache Composer packages
      id: composer-cache
      uses: actions/cache@v3
      with:
        path: vendor
        key: ${{ runner.os }}-php-${{ hashFiles('**/composer.lock') }}
        restore-keys: |
          ${{ runner.os }}-php-
          
    - name: Install dependencies
      run: composer install --prefer-dist --no-progress --no-suggest
      
    - name: Copy environment file
      run: cp .env.testing .env
      
    - name: Generate application key
      run: php artisan key:generate
      
    - name: Create database
      run: |
        mysql -h 127.0.0.1 -u root -ppassword -e "CREATE DATABASE IF NOT EXISTS chisolution_test;"
        
    - name: Run database migrations
      run: php artisan migrate --force
      
    - name: Run database seeders
      run: php artisan db:seed --force
      
    - name: Run tests
      run: |
        vendor/bin/phpunit --coverage-text --coverage-clover=coverage.xml
        
    - name: Run static analysis
      run: |
        vendor/bin/phpstan analyse --memory-limit=2G
        
    - name: Run code style check
      run: |
        vendor/bin/phpcs --standard=PSR12 app/
        
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml

  deploy-staging:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to Staging
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.STAGING_HOST }}
        username: ${{ secrets.STAGING_USERNAME }}
        password: ${{ secrets.STAGING_PASSWORD }}
        script: |
          cd /home/<USER>/public_html
          git pull origin develop
          composer install --no-dev --optimize-autoloader
          php artisan migrate --force
          php artisan config:cache
          php artisan route:cache
          php artisan view:cache
          php artisan queue:restart

  deploy-production:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to Production
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.PRODUCTION_HOST }}
        username: ${{ secrets.PRODUCTION_USERNAME }}
        password: ${{ secrets.PRODUCTION_PASSWORD }}
        script: |
          cd /home/<USER>/public_html
          
          # Create backup
          php artisan backup:run
          
          # Put application in maintenance mode
          php artisan down --message="Updating application" --retry=60
          
          # Pull latest code
          git pull origin main
          
          # Install/update dependencies
          composer install --no-dev --optimize-autoloader
          
          # Run migrations
          php artisan migrate --force
          
          # Clear and cache config
          php artisan config:cache
          php artisan route:cache
          php artisan view:cache
          
          # Restart queue workers
          php artisan queue:restart
          
          # Bring application back online
          php artisan up
          
          # Notify deployment success
          curl -X POST ${{ secrets.SLACK_WEBHOOK }} \
            -H 'Content-type: application/json' \
            --data '{"text":"🚀 ChiSolution deployed successfully to production!"}'
```

### 📁 Directory Structure for cPanel

#### **Production Directory Layout**
```
/home/<USER>/
├── public_html/                 # Document root
│   ├── index.php               # Laravel entry point
│   ├── .htaccess               # Apache configuration
│   ├── css/                    # Compiled CSS
│   ├── js/                     # Compiled JavaScript
│   ├── images/                 # Static images
│   └── storage/                # Symlink to ../storage/app/public
├── app/                        # Laravel application
├── bootstrap/
├── config/
├── database/
├── resources/
├── routes/
├── storage/
│   ├── app/
│   │   ├── public/             # Public file storage
│   │   └── private/            # Private file storage
│   ├── framework/
│   │   ├── cache/
│   │   ├── sessions/
│   │   └── views/
│   └── logs/
├── vendor/                     # Composer dependencies
├── .env                        # Environment configuration
├── composer.json
├── composer.lock
└── artisan
```

### 🔧 Deployment Scripts

#### **Deployment Script (deploy.sh)**
```bash
#!/bin/bash

# ChiSolution Deployment Script
# Usage: ./deploy.sh [environment]

set -e

ENVIRONMENT=${1:-production}
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/home/<USER>/backups"
APP_DIR="/home/<USER>/public_html"

echo "🚀 Starting deployment to $ENVIRONMENT environment..."

# Function to log messages
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Function to handle errors
error_exit() {
    echo "❌ Error: $1" >&2
    exit 1
}

# Create backup directory if it doesn't exist
mkdir -p $BACKUP_DIR

# Create database backup
log "📦 Creating database backup..."
mysqldump -u $DB_USERNAME -p$DB_PASSWORD $DB_DATABASE > "$BACKUP_DIR/db_backup_$TIMESTAMP.sql" || error_exit "Database backup failed"

# Create file backup
log "📦 Creating file backup..."
tar -czf "$BACKUP_DIR/files_backup_$TIMESTAMP.tar.gz" -C $APP_DIR . || error_exit "File backup failed"

# Put application in maintenance mode
log "🔧 Enabling maintenance mode..."
cd $APP_DIR
php artisan down --message="Deployment in progress" --retry=60 || error_exit "Failed to enable maintenance mode"

# Pull latest code
log "📥 Pulling latest code..."
git pull origin main || error_exit "Git pull failed"

# Install/update Composer dependencies
log "📦 Installing Composer dependencies..."
composer install --no-dev --optimize-autoloader --no-interaction || error_exit "Composer install failed"

# Run database migrations
log "🗄️ Running database migrations..."
php artisan migrate --force || error_exit "Database migration failed"

# Clear and rebuild cache
log "🧹 Clearing and rebuilding cache..."
php artisan config:clear
php artisan config:cache
php artisan route:clear
php artisan route:cache
php artisan view:clear
php artisan view:cache

# Optimize application
log "⚡ Optimizing application..."
php artisan optimize

# Restart queue workers
log "🔄 Restarting queue workers..."
php artisan queue:restart

# Generate sitemap
log "🗺️ Generating sitemap..."
php artisan sitemap:generate

# Warm up cache
log "🔥 Warming up cache..."
php artisan cache:warm

# Disable maintenance mode
log "✅ Disabling maintenance mode..."
php artisan up || error_exit "Failed to disable maintenance mode"

# Clean up old backups (keep last 10)
log "🧹 Cleaning up old backups..."
cd $BACKUP_DIR
ls -t db_backup_*.sql | tail -n +11 | xargs -r rm
ls -t files_backup_*.tar.gz | tail -n +11 | xargs -r rm

# Send deployment notification
log "📢 Sending deployment notification..."
curl -X POST "$SLACK_WEBHOOK_URL" \
    -H 'Content-type: application/json' \
    --data "{\"text\":\"🚀 ChiSolution deployed successfully to $ENVIRONMENT at $(date)\"}"

log "✅ Deployment completed successfully!"
```

#### **Rollback Script (rollback.sh)**
```bash
#!/bin/bash

# ChiSolution Rollback Script
# Usage: ./rollback.sh [backup_timestamp]

set -e

BACKUP_TIMESTAMP=${1}
BACKUP_DIR="/home/<USER>/backups"
APP_DIR="/home/<USER>/public_html"

if [ -z "$BACKUP_TIMESTAMP" ]; then
    echo "Available backups:"
    ls -la $BACKUP_DIR/db_backup_*.sql | awk '{print $9}' | sed 's/.*db_backup_\(.*\)\.sql/\1/'
    echo "Usage: ./rollback.sh [backup_timestamp]"
    exit 1
fi

echo "🔄 Starting rollback to backup: $BACKUP_TIMESTAMP"

# Put application in maintenance mode
echo "🔧 Enabling maintenance mode..."
cd $APP_DIR
php artisan down --message="Rollback in progress" --retry=60

# Restore database
echo "🗄️ Restoring database..."
mysql -u $DB_USERNAME -p$DB_PASSWORD $DB_DATABASE < "$BACKUP_DIR/db_backup_$BACKUP_TIMESTAMP.sql"

# Restore files
echo "📁 Restoring files..."
cd /home/<USER>
tar -xzf "$BACKUP_DIR/files_backup_$BACKUP_TIMESTAMP.tar.gz" -C public_html

# Clear cache
echo "🧹 Clearing cache..."
cd $APP_DIR
php artisan config:clear
php artisan route:clear
php artisan view:clear
php artisan cache:clear

# Disable maintenance mode
echo "✅ Disabling maintenance mode..."
php artisan up

echo "✅ Rollback completed successfully!"
```

### 🔒 Environment Configuration

#### **Production .env Template**
```bash
# Application
APP_NAME="ChiSolution"
APP_ENV=production
APP_KEY=base64:GENERATED_KEY_HERE
APP_DEBUG=false
APP_URL=https://chisolution.com

# Database
DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=chisolution_prod
DB_USERNAME=chisolution_user
DB_PASSWORD=SECURE_PASSWORD_HERE

# Cache & Session
CACHE_DRIVER=file
SESSION_DRIVER=file
SESSION_LIFETIME=120
SESSION_ENCRYPT=true

# Queue
QUEUE_CONNECTION=database

# Mail
MAIL_MAILER=smtp
MAIL_HOST=mail.chisolution.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=MAIL_PASSWORD_HERE
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="ChiSolution"

# Payment Gateways
STRIPE_KEY=pk_live_STRIPE_KEY_HERE
STRIPE_SECRET=sk_live_STRIPE_SECRET_HERE
PAYPAL_CLIENT_ID=PAYPAL_CLIENT_ID_HERE
PAYPAL_CLIENT_SECRET=PAYPAL_CLIENT_SECRET_HERE
PAYPAL_MODE=live

# Google Services
GOOGLE_MAPS_API_KEY=GOOGLE_MAPS_KEY_HERE
GOOGLE_ANALYTICS_ID=GA_TRACKING_ID_HERE
GOOGLE_RECAPTCHA_SITE_KEY=RECAPTCHA_SITE_KEY_HERE
GOOGLE_RECAPTCHA_SECRET_KEY=RECAPTCHA_SECRET_KEY_HERE

# Social Media
FACEBOOK_PIXEL_ID=FACEBOOK_PIXEL_ID_HERE

# File Storage
FILESYSTEM_DISK=public

# Logging
LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

# Security
BCRYPT_ROUNDS=12
```

### 📊 Monitoring & Health Checks

#### **Health Check Endpoint**
```php
// routes/web.php
Route::get('/health', function () {
    $checks = [
        'database' => DB::connection()->getPdo() ? 'ok' : 'error',
        'cache' => Cache::put('health_check', 'ok', 60) ? 'ok' : 'error',
        'storage' => Storage::disk('public')->exists('health_check.txt') ? 'ok' : 'error',
        'queue' => Queue::size() !== false ? 'ok' : 'error'
    ];
    
    $status = in_array('error', $checks) ? 500 : 200;
    
    return response()->json([
        'status' => $status === 200 ? 'healthy' : 'unhealthy',
        'timestamp' => now()->toISOString(),
        'checks' => $checks
    ], $status);
});
```

#### **Monitoring Script (monitor.sh)**
```bash
#!/bin/bash

# ChiSolution Monitoring Script
# Run via cron every 5 minutes

HEALTH_URL="https://chisolution.com/health"
SLACK_WEBHOOK="$SLACK_WEBHOOK_URL"
LOG_FILE="/home/<USER>/logs/monitor.log"

# Check application health
response=$(curl -s -o /dev/null -w "%{http_code}" $HEALTH_URL)

if [ $response -ne 200 ]; then
    echo "$(date): Health check failed with status $response" >> $LOG_FILE
    
    # Send alert to Slack
    curl -X POST $SLACK_WEBHOOK \
        -H 'Content-type: application/json' \
        --data "{\"text\":\"🚨 ChiSolution health check failed! Status: $response\"}"
fi

# Check disk space
disk_usage=$(df /home/<USER>'NR==2 {print $5}' | sed 's/%//')
if [ $disk_usage -gt 80 ]; then
    echo "$(date): Disk usage high: $disk_usage%" >> $LOG_FILE
    
    curl -X POST $SLACK_WEBHOOK \
        -H 'Content-type: application/json' \
        --data "{\"text\":\"⚠️ ChiSolution disk usage high: $disk_usage%\"}"
fi

# Check error logs
error_count=$(tail -100 /home/<USER>/storage/logs/laravel.log | grep -c "ERROR" || echo "0")
if [ $error_count -gt 10 ]; then
    echo "$(date): High error count: $error_count" >> $LOG_FILE
    
    curl -X POST $SLACK_WEBHOOK \
        -H 'Content-type: application/json' \
        --data "{\"text\":\"🔥 ChiSolution has $error_count errors in the last 100 log entries\"}"
fi
```

### 🔄 Backup Strategy

#### **Automated Backup Script (backup.sh)**
```bash
#!/bin/bash

# ChiSolution Backup Script
# Run daily via cron at 2 AM

TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/home/<USER>/backups"
APP_DIR="/home/<USER>/public_html"
RETENTION_DAYS=30

# Create backup directory
mkdir -p $BACKUP_DIR

# Database backup
mysqldump -u $DB_USERNAME -p$DB_PASSWORD $DB_DATABASE | gzip > "$BACKUP_DIR/db_$TIMESTAMP.sql.gz"

# Files backup
tar -czf "$BACKUP_DIR/files_$TIMESTAMP.tar.gz" -C $APP_DIR \
    --exclude='storage/logs/*' \
    --exclude='storage/framework/cache/*' \
    --exclude='storage/framework/sessions/*' \
    --exclude='storage/framework/views/*' \
    .

# Clean up old backups
find $BACKUP_DIR -name "db_*.sql.gz" -mtime +$RETENTION_DAYS -delete
find $BACKUP_DIR -name "files_*.tar.gz" -mtime +$RETENTION_DAYS -delete

echo "$(date): Backup completed successfully" >> /home/<USER>/logs/backup.log
```
