<?php

namespace App\Services;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Http\Request;

class PaginationService
{
    // Default pagination settings
    private const DEFAULT_PER_PAGE = 15;
    private const MAX_PER_PAGE = 100;
    private const MIN_PER_PAGE = 5;

    /**
     * Get optimized pagination parameters from request.
     */
    public function getPaginationParams(Request $request): array
    {
        $perPage = $request->get('per_page', self::DEFAULT_PER_PAGE);
        
        // Validate and sanitize per_page parameter
        $perPage = max(self::MIN_PER_PAGE, min(self::MAX_PER_PAGE, (int) $perPage));
        
        return [
            'per_page' => $perPage,
            'page' => max(1, (int) $request->get('page', 1)),
        ];
    }

    /**
     * Apply efficient pagination to a query builder.
     */
    public function paginateQuery(Builder $query, Request $request, array $options = []): LengthAwarePaginator
    {
        $params = $this->getPaginationParams($request);
        
        // Apply select optimization if specified
        if (isset($options['select'])) {
            $query->select($options['select']);
        }
        
        // Apply eager loading if specified
        if (isset($options['with'])) {
            $query->with($options['with']);
        }
        
        // Apply ordering if specified
        if (isset($options['orderBy'])) {
            $orderBy = $options['orderBy'];
            $direction = $options['orderDirection'] ?? 'desc';
            $query->orderBy($orderBy, $direction);
        }
        
        return $query->paginate(
            $params['per_page'],
            ['*'],
            'page',
            $params['page']
        )->appends($request->query());
    }

    /**
     * Get pagination options for different contexts.
     */
    public function getContextualPagination(string $context): array
    {
        return match ($context) {
            'dashboard_recent' => [
                'per_page' => 5,
                'simple' => true,
            ],
            'admin_list' => [
                'per_page' => 25,
                'simple' => false,
            ],
            'user_orders' => [
                'per_page' => 10,
                'simple' => false,
            ],
            'user_projects' => [
                'per_page' => 12,
                'simple' => false,
            ],
            'product_catalog' => [
                'per_page' => 20,
                'simple' => false,
            ],
            'search_results' => [
                'per_page' => 15,
                'simple' => false,
            ],
            default => [
                'per_page' => self::DEFAULT_PER_PAGE,
                'simple' => false,
            ],
        };
    }

    /**
     * Create optimized pagination for large datasets.
     */
    public function createOptimizedPagination(
        Builder $query,
        Request $request,
        string $context = 'default'
    ): LengthAwarePaginator {
        $contextOptions = $this->getContextualPagination($context);
        $params = $this->getPaginationParams($request);
        
        // Override per_page with context-specific value if not provided in request
        if (!$request->has('per_page')) {
            $params['per_page'] = $contextOptions['per_page'];
        }
        
        // For large datasets, use cursor pagination or limit total count
        if ($context === 'admin_list' && $this->isLargeDataset($query)) {
            return $this->createCursorPagination($query, $request, $params);
        }
        
        return $query->paginate(
            $params['per_page'],
            ['*'],
            'page',
            $params['page']
        )->appends($request->query());
    }

    /**
     * Check if dataset is large enough to require optimization.
     */
    private function isLargeDataset(Builder $query): bool
    {
        // Use explain or estimate for very large tables
        $estimatedCount = $query->toBase()->getCountForPagination();
        return $estimatedCount > 10000;
    }

    /**
     * Create cursor-based pagination for large datasets.
     */
    private function createCursorPagination(Builder $query, Request $request, array $params): LengthAwarePaginator
    {
        // Implement cursor pagination for better performance on large datasets
        $cursor = $request->get('cursor');
        
        if ($cursor) {
            $query->where('id', '>', $cursor);
        }
        
        $items = $query->limit($params['per_page'] + 1)->get();
        $hasMore = $items->count() > $params['per_page'];
        
        if ($hasMore) {
            $items->pop(); // Remove the extra item
        }
        
        $nextCursor = $hasMore ? $items->last()->id : null;
        
        // Create a custom paginator
        return new LengthAwarePaginator(
            $items,
            $items->count(),
            $params['per_page'],
            $params['page'],
            [
                'path' => $request->url(),
                'pageName' => 'page',
                'nextCursor' => $nextCursor,
            ]
        );
    }

    /**
     * Get pagination metadata for API responses.
     */
    public function getPaginationMetadata(LengthAwarePaginator $paginator): array
    {
        return [
            'current_page' => $paginator->currentPage(),
            'per_page' => $paginator->perPage(),
            'total' => $paginator->total(),
            'last_page' => $paginator->lastPage(),
            'from' => $paginator->firstItem(),
            'to' => $paginator->lastItem(),
            'has_more_pages' => $paginator->hasMorePages(),
            'links' => [
                'first' => $paginator->url(1),
                'last' => $paginator->url($paginator->lastPage()),
                'prev' => $paginator->previousPageUrl(),
                'next' => $paginator->nextPageUrl(),
            ],
        ];
    }

    /**
     * Create simple pagination for dashboard widgets.
     */
    public function createSimplePagination(Builder $query, int $limit = 5): \Illuminate\Support\Collection
    {
        return $query->limit($limit)->get();
    }

    /**
     * Get recommended per_page values for UI.
     */
    public function getPerPageOptions(): array
    {
        return [
            5 => '5 per page',
            10 => '10 per page',
            15 => '15 per page',
            25 => '25 per page',
            50 => '50 per page',
            100 => '100 per page',
        ];
    }

    /**
     * Calculate pagination statistics for performance monitoring.
     */
    public function getPaginationStats(LengthAwarePaginator $paginator): array
    {
        return [
            'total_items' => $paginator->total(),
            'total_pages' => $paginator->lastPage(),
            'current_page' => $paginator->currentPage(),
            'items_per_page' => $paginator->perPage(),
            'items_on_current_page' => $paginator->count(),
            'efficiency_ratio' => $paginator->count() / $paginator->perPage(),
            'is_first_page' => $paginator->onFirstPage(),
            'is_last_page' => !$paginator->hasMorePages(),
        ];
    }
}
