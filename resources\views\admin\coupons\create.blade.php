@extends('layouts.dashboard')

@section('title', 'Create Coupon - Admin Dashboard')
@section('page_title', 'Create Coupon')

@section('content')
<div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Create Coupon</h1>
            <p class="text-gray-600">Create a new discount coupon for your customers</p>
        </div>
        <a href="{{ route('admin.coupons.index') }}" 
           class="inline-flex items-center px-4 py-2 bg-neutral-600 text-white rounded-lg hover:bg-neutral-700 transition-colors duration-200">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
            </svg>
            Back to Coupons
        </a>
    </div>

    <!-- Form -->
    <div class="bg-white rounded-xl shadow-soft border border-neutral-100">
        <form method="POST" action="{{ route('admin.coupons.store') }}" class="p-6 space-y-8">
            @csrf
            
            <!-- Basic Information -->
            <div class="space-y-6">
                <h3 class="text-lg font-semibold text-gray-900 border-b border-neutral-200 pb-2">
                    Basic Information
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Coupon Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                            Coupon Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" 
                               id="name" 
                               name="name" 
                               value="{{ old('name') }}"
                               class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('name') border-red-500 @enderror"
                               placeholder="Enter coupon name"
                               required>
                        @error('name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Coupon Code -->
                    <div>
                        <label for="code" class="block text-sm font-medium text-gray-700 mb-2">
                            Coupon Code
                        </label>
                        <div class="flex space-x-2">
                            <input type="text" 
                                   id="code" 
                                   name="code" 
                                   value="{{ old('code') }}"
                                   class="flex-1 px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('code') border-red-500 @enderror"
                                   placeholder="Leave empty to auto-generate">
                            <button type="button" 
                                    id="generate-code" 
                                    class="px-4 py-2 bg-secondary-600 text-white rounded-lg hover:bg-secondary-700 transition-colors duration-200">
                                Generate
                            </button>
                        </div>
                        <p class="mt-1 text-xs text-gray-500">Leave empty to auto-generate a unique code</p>
                        @error('code')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Description -->
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                        Description
                    </label>
                    <textarea id="description" 
                              name="description" 
                              rows="3"
                              class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('description') border-red-500 @enderror"
                              placeholder="Describe this coupon">{{ old('description') }}</textarea>
                    @error('description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Discount Settings -->
            <div class="space-y-6">
                <h3 class="text-lg font-semibold text-gray-900 border-b border-neutral-200 pb-2">
                    Discount Settings
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Discount Type -->
                    <div>
                        <label for="type" class="block text-sm font-medium text-gray-700 mb-2">
                            Discount Type <span class="text-red-500">*</span>
                        </label>
                        <select id="type" 
                                name="type" 
                                class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('type') border-red-500 @enderror"
                                required>
                            <option value="">Select discount type</option>
                            <option value="percentage" {{ old('type') == 'percentage' ? 'selected' : '' }}>Percentage</option>
                            <option value="fixed_amount" {{ old('type') == 'fixed_amount' ? 'selected' : '' }}>Fixed Amount</option>
                            <option value="free_shipping" {{ old('type') == 'free_shipping' ? 'selected' : '' }}>Free Shipping</option>
                        </select>
                        @error('type')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Discount Value -->
                    <div>
                        <label for="value" class="block text-sm font-medium text-gray-700 mb-2">
                            Discount Value <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <span id="value-prefix" class="absolute left-3 top-2 text-gray-500">%</span>
                            <input type="number" 
                                   id="value" 
                                   name="value" 
                                   value="{{ old('value') }}"
                                   step="0.01"
                                   min="0"
                                   class="w-full pl-8 pr-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('value') border-red-500 @enderror"
                                   placeholder="0.00"
                                   required>
                        </div>
                        @error('value')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Maximum Discount -->
                    <div id="max-discount-field" class="hidden">
                        <label for="maximum_discount" class="block text-sm font-medium text-gray-700 mb-2">
                            Maximum Discount
                        </label>
                        <div class="relative">
                            <span class="absolute left-3 top-2 text-gray-500">R</span>
                            <input type="number" 
                                   id="maximum_discount" 
                                   name="maximum_discount" 
                                   value="{{ old('maximum_discount') }}"
                                   step="0.01"
                                   min="0"
                                   class="w-full pl-8 pr-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('maximum_discount') border-red-500 @enderror"
                                   placeholder="0.00">
                        </div>
                        <p class="mt-1 text-xs text-gray-500">Maximum discount amount for percentage coupons</p>
                        @error('maximum_discount')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Minimum Amount -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="minimum_amount" class="block text-sm font-medium text-gray-700 mb-2">
                            Minimum Order Amount
                        </label>
                        <div class="relative">
                            <span class="absolute left-3 top-2 text-gray-500">R</span>
                            <input type="number" 
                                   id="minimum_amount" 
                                   name="minimum_amount" 
                                   value="{{ old('minimum_amount') }}"
                                   step="0.01"
                                   min="0"
                                   class="w-full pl-8 pr-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('minimum_amount') border-red-500 @enderror"
                                   placeholder="0.00">
                        </div>
                        <p class="mt-1 text-xs text-gray-500">Minimum order amount to use this coupon</p>
                        @error('minimum_amount')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Usage Limits -->
            <div class="space-y-6">
                <h3 class="text-lg font-semibold text-gray-900 border-b border-neutral-200 pb-2">
                    Usage Limits
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Total Usage Limit -->
                    <div>
                        <label for="usage_limit" class="block text-sm font-medium text-gray-700 mb-2">
                            Total Usage Limit
                        </label>
                        <input type="number" 
                               id="usage_limit" 
                               name="usage_limit" 
                               value="{{ old('usage_limit') }}"
                               min="1"
                               class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('usage_limit') border-red-500 @enderror"
                               placeholder="Leave empty for unlimited">
                        <p class="mt-1 text-xs text-gray-500">Maximum number of times this coupon can be used</p>
                        @error('usage_limit')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Per Customer Usage Limit -->
                    <div>
                        <label for="usage_limit_per_customer" class="block text-sm font-medium text-gray-700 mb-2">
                            Usage Limit Per Customer
                        </label>
                        <input type="number" 
                               id="usage_limit_per_customer" 
                               name="usage_limit_per_customer" 
                               value="{{ old('usage_limit_per_customer') }}"
                               min="1"
                               class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('usage_limit_per_customer') border-red-500 @enderror"
                               placeholder="Leave empty for unlimited">
                        <p class="mt-1 text-xs text-gray-500">Maximum uses per customer</p>
                        @error('usage_limit_per_customer')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Valid Period -->
            <div class="space-y-6">
                <h3 class="text-lg font-semibold text-gray-900 border-b border-neutral-200 pb-2">
                    Valid Period
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Start Date -->
                    <div>
                        <label for="starts_at" class="block text-sm font-medium text-gray-700 mb-2">
                            Start Date
                        </label>
                        <input type="datetime-local" 
                               id="starts_at" 
                               name="starts_at" 
                               value="{{ old('starts_at') }}"
                               class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('starts_at') border-red-500 @enderror">
                        <p class="mt-1 text-xs text-gray-500">Leave empty to start immediately</p>
                        @error('starts_at')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- End Date -->
                    <div>
                        <label for="expires_at" class="block text-sm font-medium text-gray-700 mb-2">
                            Expiry Date
                        </label>
                        <input type="datetime-local" 
                               id="expires_at" 
                               name="expires_at" 
                               value="{{ old('expires_at') }}"
                               class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('expires_at') border-red-500 @enderror">
                        <p class="mt-1 text-xs text-gray-500">Leave empty for no expiry</p>
                        @error('expires_at')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Status -->
            <div class="space-y-4">
                <h3 class="text-lg font-semibold text-gray-900 border-b border-neutral-200 pb-2">
                    Status
                </h3>
                
                <div class="flex items-center">
                    <input type="checkbox" 
                           id="is_active" 
                           name="is_active" 
                           value="1"
                           {{ old('is_active', true) ? 'checked' : '' }}
                           class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-neutral-300 rounded">
                    <label for="is_active" class="ml-2 block text-sm text-gray-900">
                        Active (available for use)
                    </label>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-neutral-200">
                <a href="{{ route('admin.coupons.index') }}" 
                   class="px-6 py-2 border border-neutral-300 text-neutral-700 rounded-lg hover:bg-neutral-50 transition-colors duration-200">
                    Cancel
                </a>
                <button type="submit" 
                        class="px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200">
                    Create Coupon
                </button>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const typeSelect = document.getElementById('type');
    const valueInput = document.getElementById('value');
    const valuePrefix = document.getElementById('value-prefix');
    const maxDiscountField = document.getElementById('max-discount-field');
    const generateCodeBtn = document.getElementById('generate-code');
    const codeInput = document.getElementById('code');

    // Update value prefix and max discount field based on type
    function updateDiscountFields() {
        const type = typeSelect.value;
        
        switch(type) {
            case 'percentage':
                valuePrefix.textContent = '%';
                valueInput.max = '100';
                maxDiscountField.classList.remove('hidden');
                break;
            case 'fixed_amount':
                valuePrefix.textContent = 'R';
                valueInput.removeAttribute('max');
                maxDiscountField.classList.add('hidden');
                break;
            case 'free_shipping':
                valuePrefix.textContent = '';
                valueInput.value = '0';
                valueInput.readOnly = true;
                maxDiscountField.classList.add('hidden');
                break;
            default:
                valuePrefix.textContent = '%';
                valueInput.readOnly = false;
                maxDiscountField.classList.add('hidden');
        }
    }

    typeSelect.addEventListener('change', updateDiscountFields);
    updateDiscountFields(); // Initial call

    // Generate random coupon code
    generateCodeBtn.addEventListener('click', function() {
        const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let code = '';
        for (let i = 0; i < 8; i++) {
            code += characters.charAt(Math.floor(Math.random() * characters.length));
        }
        codeInput.value = code;
    });
});
</script>
@endpush
