<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\UserAddress;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\UserAddress>
 */
class UserAddressFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = UserAddress::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'type' => $this->faker->randomElement(['billing', 'shipping', 'default']),
            'first_name' => $this->faker->firstName(),
            'last_name' => $this->faker->lastName(),
            'company' => $this->faker->optional(0.3)->company(),
            'address_line_1' => $this->faker->streetAddress(),
            'address_line_2' => $this->faker->optional(0.4)->secondaryAddress(),
            'city' => $this->faker->city(),
            'state' => $this->faker->state(),
            'postal_code' => $this->faker->postcode(),
            'country' => $this->faker->randomElement(['ZA', 'US', 'GB', 'CA', 'AU']),
            'phone' => $this->faker->optional(0.7)->phoneNumber(),
            'is_default' => false,
            'is_deleted' => false,
        ];
    }

    /**
     * Indicate that the address is a billing address.
     */
    public function billing(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'billing',
        ]);
    }

    /**
     * Indicate that the address is a shipping address.
     */
    public function shipping(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'shipping',
        ]);
    }

    /**
     * Indicate that the address is a default/general address.
     */
    public function general(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'default',
        ]);
    }

    /**
     * Indicate that the address is the default address.
     */
    public function default(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_default' => true,
        ]);
    }

    /**
     * Indicate that the address is soft deleted.
     */
    public function deleted(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_deleted' => true,
            'is_default' => false, // Deleted addresses cannot be default
        ]);
    }

    /**
     * Create a South African address.
     */
    public function southAfrican(): static
    {
        return $this->state(fn (array $attributes) => [
            'country' => 'ZA',
            'city' => $this->faker->randomElement([
                'Cape Town', 'Johannesburg', 'Durban', 'Pretoria', 
                'Port Elizabeth', 'Bloemfontein', 'East London', 'Pietermaritzburg'
            ]),
            'state' => $this->faker->randomElement([
                'Western Cape', 'Gauteng', 'KwaZulu-Natal', 'Eastern Cape',
                'Free State', 'Limpopo', 'Mpumalanga', 'North West', 'Northern Cape'
            ]),
            'postal_code' => $this->faker->numberBetween(1000, 9999),
        ]);
    }

    /**
     * Create an address with company information.
     */
    public function withCompany(): static
    {
        return $this->state(fn (array $attributes) => [
            'company' => $this->faker->company(),
        ]);
    }

    /**
     * Create an address without optional fields.
     */
    public function minimal(): static
    {
        return $this->state(fn (array $attributes) => [
            'company' => null,
            'address_line_2' => null,
            'phone' => null,
        ]);
    }
}
