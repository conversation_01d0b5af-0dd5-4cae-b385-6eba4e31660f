<?php

namespace Tests\Unit\Services;

use App\Services\ImageService;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Config;
use Tests\TestCase;
use Mockery;

class ImageServiceTest extends TestCase
{
    private ImageService $imageService;
    
    protected function setUp(): void
    {
        parent::setUp();
        $this->imageService = new ImageService();
        
        // Set up test configuration
        Config::set('image.max_file_size', 10 * 1024 * 1024); // 10MB
        Config::set('image.allowed_mimes', ['image/jpeg', 'image/png', 'image/gif', 'image/webp']);
        Config::set('image.allowed_extensions', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
        Config::set('image.enable_optimization', true);
        Config::set('image.enable_webp_conversion', true);
        Config::set('image.virus_scan.enabled', false); // Disable for testing
        Config::set('image.storage.disk', 'testing');
        Config::set('image.storage.path', 'images');
        Config::set('image.storage.temp_path', 'temp/images');
        Config::set('image.logging.enabled', false); // Disable logging for tests
        
        // Set up test storage disk
        Config::set('filesystems.disks.testing', [
            'driver' => 'local',
            'root' => storage_path('app/testing'),
        ]);
        
        Storage::fake('testing');
    }
    
    protected function tearDown(): void
    {
        Storage::disk('testing')->deleteDirectory('');
        parent::tearDown();
    }
    
    /** @test */
    public function it_can_validate_valid_image_file()
    {
        $file = UploadedFile::fake()->image('test.jpg', 800, 600)->size(1024); // 1MB
        
        $result = $this->imageService->validateImageFile($file);
        
        $this->assertTrue($result['valid']);
        $this->assertEmpty($result['errors']);
        $this->assertArrayHasKey('file_info', $result);
        $this->assertEquals('test.jpg', $result['file_info']['original_name']);
    }
    
    /** @test */
    public function it_rejects_oversized_files()
    {
        Config::set('image.max_file_size', 1024); // 1KB limit
        $file = UploadedFile::fake()->image('large.jpg', 800, 600)->size(2048); // 2KB
        
        $result = $this->imageService->validateImageFile($file);
        
        $this->assertFalse($result['valid']);
        $this->assertContains('File size exceeds maximum allowed size', $result['errors']);
    }
    
    /** @test */
    public function it_rejects_invalid_mime_types()
    {
        $file = UploadedFile::fake()->create('document.pdf', 1024, 'application/pdf');
        
        $result = $this->imageService->validateImageFile($file);
        
        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('Invalid file type', $result['errors'][0]);
    }
    
    /** @test */
    public function it_rejects_invalid_extensions()
    {
        // Create a file with wrong extension but valid mime type
        $file = UploadedFile::fake()->create('test.txt', 1024, 'image/jpeg');
        
        $result = $this->imageService->validateImageFile($file);
        
        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('Invalid file extension', $result['errors'][0]);
    }
    
    /** @test */
    public function it_sanitizes_filenames_properly()
    {
        $dangerousFilename = '../../../etc/passwd.jpg';
        $sanitized = $this->imageService->sanitizeFilename($dangerousFilename);
        
        $this->assertStringNotContainsString('..', $sanitized);
        $this->assertStringNotContainsString('/', $sanitized);
        $this->assertStringContainsString('.jpg', $sanitized);
    }
    
    /** @test */
    public function it_sanitizes_special_characters_in_filenames()
    {
        $filename = 'test file with spaces & special chars!@#$.jpg';
        $sanitized = $this->imageService->sanitizeFilename($filename);
        
        $this->assertStringNotContainsString(' ', $sanitized);
        $this->assertStringNotContainsString('&', $sanitized);
        $this->assertStringNotContainsString('!', $sanitized);
        $this->assertStringNotContainsString('@', $sanitized);
        $this->assertStringContainsString('.jpg', $sanitized);
    }
    
    /** @test */
    public function it_generates_unique_filenames()
    {
        $filename1 = $this->imageService->sanitizeFilename('test.jpg');
        $filename2 = $this->imageService->sanitizeFilename('test.jpg');
        
        $this->assertNotEquals($filename1, $filename2);
    }
    
    /** @test */
    public function it_handles_empty_filenames()
    {
        $sanitized = $this->imageService->sanitizeFilename('');
        
        $this->assertNotEmpty($sanitized);
        $this->assertStringContainsString('image_', $sanitized);
    }
    
    /** @test */
    public function it_can_get_image_format()
    {
        // Create a temporary test image
        $tempPath = tempnam(sys_get_temp_dir(), 'test_image');
        $image = imagecreate(100, 100);
        imagejpeg($image, $tempPath);
        imagedestroy($image);
        
        $reflection = new \ReflectionClass($this->imageService);
        $method = $reflection->getMethod('getImageFormat');
        $method->setAccessible(true);
        
        $format = $method->invoke($this->imageService, $tempPath);
        
        $this->assertEquals('jpeg', $format);
        
        unlink($tempPath);
    }
    
    /** @test */
    public function it_returns_unknown_for_invalid_image_format()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_file');
        file_put_contents($tempPath, 'not an image');
        
        $reflection = new \ReflectionClass($this->imageService);
        $method = $reflection->getMethod('getImageFormat');
        $method->setAccessible(true);
        
        $format = $method->invoke($this->imageService, $tempPath);
        
        $this->assertEquals('unknown', $format);
        
        unlink($tempPath);
    }
    
    /** @test */
    public function it_can_get_image_info_for_existing_file()
    {
        // Create a temporary test image
        $tempPath = tempnam(sys_get_temp_dir(), 'test_image');
        $image = imagecreate(200, 150);
        imagejpeg($image, $tempPath);
        imagedestroy($image);
        
        $info = $this->imageService->getImageInfo($tempPath);
        
        $this->assertTrue($info['exists']);
        $this->assertEquals(200, $info['width']);
        $this->assertEquals(150, $info['height']);
        $this->assertArrayHasKey('size', $info);
        $this->assertArrayHasKey('mime_type', $info);
        
        unlink($tempPath);
    }
    
    /** @test */
    public function it_returns_false_for_non_existent_file_info()
    {
        $info = $this->imageService->getImageInfo('/non/existent/file.jpg');
        
        $this->assertFalse($info['exists']);
    }
    
    /** @test */
    public function it_can_get_public_url_for_image()
    {
        Storage::disk('testing')->put('test-image.jpg', 'fake image content');
        
        $url = $this->imageService->getImageUrl('test-image.jpg');
        
        $this->assertIsString($url);
        $this->assertStringContainsString('test-image.jpg', $url);
    }
    
    /** @test */
    public function virus_scan_returns_clean_when_disabled()
    {
        Config::set('image.virus_scan.enabled', false);
        
        $tempPath = tempnam(sys_get_temp_dir(), 'test_file');
        file_put_contents($tempPath, 'test content');
        
        $result = $this->imageService->scanForViruses($tempPath);
        
        $this->assertTrue($result['clean']);
        $this->assertEquals('Virus scanning disabled', $result['message']);
        
        unlink($tempPath);
    }
    
    /** @test */
    public function basic_security_check_passes_for_clean_content()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_file');
        file_put_contents($tempPath, 'clean image content');
        
        $reflection = new \ReflectionClass($this->imageService);
        $method = $reflection->getMethod('performBasicSecurityCheck');
        $method->setAccessible(true);
        
        $result = $method->invoke($this->imageService, $tempPath);
        
        $this->assertTrue($result['clean']);
        $this->assertEquals('Basic security check passed', $result['message']);
        
        unlink($tempPath);
    }
    
    /** @test */
    public function basic_security_check_fails_for_suspicious_content()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_file');
        file_put_contents($tempPath, '<script>alert("xss")</script>');
        
        $reflection = new \ReflectionClass($this->imageService);
        $method = $reflection->getMethod('performBasicSecurityCheck');
        $method->setAccessible(true);
        
        $result = $method->invoke($this->imageService, $tempPath);
        
        $this->assertFalse($result['clean']);
        $this->assertEquals('Suspicious content detected', $result['message']);
        
        unlink($tempPath);
    }
    
    /** @test */
    public function it_validates_file_signature_for_jpeg()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_image');
        $image = imagecreate(100, 100);
        imagejpeg($image, $tempPath);
        imagedestroy($image);
        
        $file = new UploadedFile($tempPath, 'test.jpg', 'image/jpeg', null, true);
        
        $reflection = new \ReflectionClass($this->imageService);
        $method = $reflection->getMethod('validateFileSignature');
        $method->setAccessible(true);
        
        $result = $method->invoke($this->imageService, $file);
        
        $this->assertTrue($result);
        
        unlink($tempPath);
    }
    
    /** @test */
    public function it_rejects_invalid_file_signature()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_file');
        file_put_contents($tempPath, 'not a real image file');
        
        $file = new UploadedFile($tempPath, 'fake.jpg', 'image/jpeg', null, true);
        
        $reflection = new \ReflectionClass($this->imageService);
        $method = $reflection->getMethod('validateFileSignature');
        $method->setAccessible(true);
        
        $result = $method->invoke($this->imageService, $file);
        
        $this->assertFalse($result);
        
        unlink($tempPath);
    }
}
