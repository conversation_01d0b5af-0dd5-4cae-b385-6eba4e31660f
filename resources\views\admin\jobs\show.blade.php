@extends('layouts.dashboard')

@section('title', $job->title . ' - Job Details - ' . __('common.company_name'))
@section('page_title', 'Job Details')

@section('content')
<div class="p-6 space-y-6">
    <!-- Header Section -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <div class="flex items-center space-x-3">
                <h1 class="text-2xl font-bold text-gray-900">{{ $job->title }}</h1>
                @if($job->is_featured)
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        Featured
                    </span>
                @endif
                @if($job->is_active && !$job->is_deleted)
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Active
                    </span>
                @elseif(!$job->is_active && !$job->is_deleted)
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        Inactive
                    </span>
                @else
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        Deleted
                    </span>
                @endif
            </div>
            <p class="mt-1 text-sm text-gray-600">{{ $job->department }} • {{ $job->location }} • Posted {{ $job->created_at->format('M j, Y') }}</p>
        </div>
        <div class="mt-4 sm:mt-0 flex space-x-3">
            <a href="{{ route('careers.show', $job) }}" target="_blank"
               class="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                </svg>
                View Public Page
            </a>
            <a href="{{ route('admin.jobs.edit', $job) }}"
               class="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                </svg>
                Edit Job
            </a>
            <a href="{{ route('admin.jobs.index') }}"
               class="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                Back to Jobs
            </a>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Applications</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $job->jobApplications->count() }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 rounded-lg">
                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Pending Review</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $job->jobApplications->where('status', 'pending')->count() }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
            <div class="flex items-center">
                <div class="p-2 bg-purple-100 rounded-lg">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Shortlisted</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $job->jobApplications->where('status', 'shortlisted')->count() }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 rounded-lg">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Hired</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $job->jobApplications->where('status', 'hired')->count() }}</p>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Job Details -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Job Information -->
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Job Information</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <span class="text-sm font-medium text-gray-500">Employment Type</span>
                        <p class="text-gray-900">{{ ucfirst(str_replace('-', ' ', $job->employment_type)) }}</p>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-500">Experience Level</span>
                        <p class="text-gray-900">{{ ucfirst(str_replace('-', ' ', $job->experience_level)) }}</p>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-500">Department</span>
                        <p class="text-gray-900">{{ $job->department }}</p>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-500">Location</span>
                        <p class="text-gray-900">
                            {{ $job->location }}
                            @if($job->is_remote)
                                <span class="text-green-600">(Remote Available)</span>
                            @endif
                        </p>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-500">Salary Range</span>
                        <p class="text-gray-900 font-semibold text-green-600">{{ $job->formatted_salary }}</p>
                    </div>
                    @if($job->application_deadline)
                    <div>
                        <span class="text-sm font-medium text-gray-500">Application Deadline</span>
                        <p class="text-gray-900">{{ $job->application_deadline->format('M j, Y') }}</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Job Description -->
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Job Description</h2>
                <div class="prose max-w-none text-gray-700">
                    {!! nl2br(e($job->description)) !!}
                </div>
            </div>

            <!-- Responsibilities -->
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Key Responsibilities</h2>
                <div class="prose max-w-none text-gray-700">
                    {!! nl2br(e($job->responsibilities)) !!}
                </div>
            </div>

            <!-- Requirements -->
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Requirements</h2>
                <div class="prose max-w-none text-gray-700">
                    {!! nl2br(e($job->requirements)) !!}
                </div>
            </div>

            @if($job->benefits)
            <!-- Benefits -->
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Benefits & Perks</h2>
                <div class="prose max-w-none text-gray-700">
                    {!! nl2br(e($job->benefits)) !!}
                </div>
            </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Quick Actions -->
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                <div class="space-y-3">
                    <a href="{{ route('admin.job-applications.index', ['job_id' => $job->id]) }}"
                       class="w-full inline-flex items-center justify-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                        </svg>
                        View Applications ({{ $job->jobApplications->count() }})
                    </a>
                    
                    @if(!$job->is_deleted)
                        <button onclick="toggleActive({{ $job->id }}, {{ $job->is_active ? 'false' : 'true' }})"
                                class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                            @if($job->is_active)
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Deactivate Job
                            @else
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M19 10a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Activate Job
                            @endif
                        </button>

                        <button onclick="toggleFeatured({{ $job->id }}, {{ $job->is_featured ? 'false' : 'true' }})"
                                class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                            <svg class="w-4 h-4 mr-2 {{ $job->is_featured ? 'fill-current text-yellow-500' : '' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                            </svg>
                            {{ $job->is_featured ? 'Remove from Featured' : 'Mark as Featured' }}
                        </button>
                    @endif

                    <form method="POST" action="{{ route('admin.jobs.destroy', $job) }}" class="w-full" 
                          onsubmit="return confirm('Are you sure you want to delete this job? This action cannot be undone.')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" 
                                class="w-full inline-flex items-center justify-center px-4 py-2 border border-red-300 text-red-700 rounded-lg hover:bg-red-50 transition-colors">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            Delete Job
                        </button>
                    </form>
                </div>
            </div>

            <!-- Job Metadata -->
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Job Metadata</h3>
                <div class="space-y-3">
                    <div>
                        <span class="text-sm font-medium text-gray-500">Job ID</span>
                        <p class="text-gray-900 font-mono text-sm">{{ $job->uuid }}</p>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-500">Slug</span>
                        <p class="text-gray-900 font-mono text-sm">{{ $job->slug }}</p>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-500">Sort Order</span>
                        <p class="text-gray-900">{{ $job->sort_order }}</p>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-500">Created</span>
                        <p class="text-gray-900">{{ $job->created_at->format('M j, Y g:i A') }}</p>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-500">Last Updated</span>
                        <p class="text-gray-900">{{ $job->updated_at->format('M j, Y g:i A') }}</p>
                    </div>
                </div>
            </div>

            @if($job->meta_title || $job->meta_description)
            <!-- SEO Information -->
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">SEO Information</h3>
                <div class="space-y-3">
                    @if($job->meta_title)
                    <div>
                        <span class="text-sm font-medium text-gray-500">Meta Title</span>
                        <p class="text-gray-900 text-sm">{{ $job->meta_title }}</p>
                    </div>
                    @endif
                    @if($job->meta_description)
                    <div>
                        <span class="text-sm font-medium text-gray-500">Meta Description</span>
                        <p class="text-gray-900 text-sm">{{ $job->meta_description }}</p>
                    </div>
                    @endif
                </div>
            </div>
            @endif
        </div>
    </div>
</div>

@push('scripts')
<script>
// Toggle active status
function toggleActive(jobId, isActive) {
    fetch(`/admin/jobs/${jobId}/toggle-active`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error updating job status');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error updating job status');
    });
}

// Toggle featured status
function toggleFeatured(jobId, isFeatured) {
    fetch(`/admin/jobs/${jobId}/toggle-featured`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error updating featured status');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error updating featured status');
    });
}
</script>
@endpush
@endsection
