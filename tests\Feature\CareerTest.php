<?php

namespace Tests\Feature;

use App\Models\Job;
use App\Models\JobApplication;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class CareerTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test careers index page displays jobs.
     */
    public function test_careers_index_displays_jobs(): void
    {
        // Create some test jobs
        Job::create([
            'title' => 'Software Developer',
            'description' => 'We are looking for a skilled software developer.',
            'requirements' => 'Bachelor degree in Computer Science',
            'responsibilities' => 'Develop and maintain software applications',
            'location' => 'Cape Town',
            'employment_type' => 'full-time',
            'experience_level' => 'mid',
            'department' => 'Engineering',
            'salary_period' => 'monthly',
            'is_active' => true,
        ]);

        Job::create([
            'title' => 'Marketing Manager',
            'description' => 'Lead our marketing efforts.',
            'requirements' => 'Marketing experience required',
            'responsibilities' => 'Manage marketing campaigns',
            'location' => 'Johannesburg',
            'employment_type' => 'full-time',
            'experience_level' => 'senior',
            'department' => 'Marketing',
            'salary_period' => 'monthly',
            'is_active' => true,
        ]);

        $response = $this->get('/careers');

        $response->assertStatus(200);
        $response->assertSee('Software Developer');
        $response->assertSee('Marketing Manager');
        $response->assertSee('Cape Town');
        $response->assertSee('Johannesburg');
    }

    /**
     * Test careers index filters work correctly.
     */
    public function test_careers_index_filters_work(): void
    {
        Job::create([
            'title' => 'Full-time Developer',
            'description' => 'Full-time position',
            'requirements' => 'Requirements',
            'responsibilities' => 'Responsibilities',
            'location' => 'Cape Town',
            'employment_type' => 'full-time',
            'experience_level' => 'mid',
            'department' => 'Engineering',
            'salary_period' => 'monthly',
            'is_active' => true,
        ]);

        Job::create([
            'title' => 'Part-time Designer',
            'description' => 'Part-time position',
            'requirements' => 'Requirements',
            'responsibilities' => 'Responsibilities',
            'location' => 'Durban',
            'employment_type' => 'part-time',
            'experience_level' => 'junior',
            'department' => 'Design',
            'salary_period' => 'hourly',
            'is_active' => true,
        ]);

        // Test employment type filter
        $response = $this->get('/careers?employment_type=full-time');
        $response->assertStatus(200);
        $response->assertSee('Full-time Developer');
        $response->assertDontSee('Part-time Designer');

        // Test department filter
        $response = $this->get('/careers?department=Design');
        $response->assertStatus(200);
        $response->assertSee('Part-time Designer');
        $response->assertDontSee('Full-time Developer');
    }

    /**
     * Test job detail page displays correctly.
     */
    public function test_job_detail_page_displays_correctly(): void
    {
        $job = Job::create([
            'title' => 'Senior Developer',
            'description' => 'We need a senior developer with extensive experience.',
            'requirements' => '5+ years of experience in web development',
            'responsibilities' => 'Lead development projects and mentor junior developers',
            'location' => 'Cape Town',
            'employment_type' => 'full-time',
            'experience_level' => 'senior',
            'department' => 'Engineering',
            'salary_period' => 'monthly',
            'salary_min' => 50000,
            'salary_max' => 70000,
            'is_active' => true,
        ]);

        $response = $this->get("/careers/job/{$job->slug}");

        $response->assertStatus(200);
        $response->assertSee('Senior Developer');
        $response->assertSee('We need a senior developer');
        $response->assertSee('5+ years of experience');
        $response->assertSee('Lead development projects');
        $response->assertSee('Cape Town');
        $response->assertSee('Apply Now');
    }

    /**
     * Test job application form displays correctly.
     */
    public function test_job_application_form_displays(): void
    {
        $job = Job::create([
            'title' => 'Test Job',
            'description' => 'Test description',
            'requirements' => 'Test requirements',
            'responsibilities' => 'Test responsibilities',
            'location' => 'Test Location',
            'employment_type' => 'full-time',
            'experience_level' => 'entry',
            'department' => 'Test',
            'salary_period' => 'monthly',
            'is_active' => true,
        ]);

        $response = $this->get("/careers/job/{$job->slug}/apply");

        $response->assertStatus(200);
        $response->assertSee('Apply for Test Job');
        $response->assertSee('Personal Information');
        $response->assertSee('First Name');
        $response->assertSee('Last Name');
        $response->assertSee('Email Address');
    }

    /**
     * Test guest can submit job application.
     */
    public function test_guest_can_submit_job_application(): void
    {
        Storage::fake('local');

        $job = Job::create([
            'title' => 'Test Job',
            'description' => 'Test description',
            'requirements' => 'Test requirements',
            'responsibilities' => 'Test responsibilities',
            'location' => 'Test Location',
            'employment_type' => 'full-time',
            'experience_level' => 'entry',
            'department' => 'Test',
            'salary_period' => 'monthly',
            'is_active' => true,
        ]);

        $applicationData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'phone' => '123456789',
            'country' => 'South Africa',
            'cover_letter' => 'I am very interested in this position.',
            'experience_summary' => 'I have 3 years of experience in software development.',
            'highest_qualification' => 'Bachelor Degree in Computer Science',
            'terms_accepted' => true,
        ];

        $response = $this->post("/careers/job/{$job->slug}/apply", $applicationData);

        $this->assertDatabaseHas('job_applications', [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'job_id' => $job->id,
        ]);

        $application = JobApplication::where('email', '<EMAIL>')->first();
        $response->assertRedirect("/careers/application-success/{$application->reference_number}");
    }

    /**
     * Test authenticated user can submit job application.
     */
    public function test_authenticated_user_can_submit_job_application(): void
    {
        $user = User::factory()->create([
            'first_name' => 'Jane',
            'last_name' => 'Smith',
            'email' => '<EMAIL>',
        ]);

        $job = Job::create([
            'title' => 'Test Job',
            'description' => 'Test description',
            'requirements' => 'Test requirements',
            'responsibilities' => 'Test responsibilities',
            'location' => 'Test Location',
            'employment_type' => 'full-time',
            'experience_level' => 'entry',
            'department' => 'Test',
            'salary_period' => 'monthly',
            'is_active' => true,
        ]);

        $applicationData = [
            'first_name' => 'Jane',
            'last_name' => 'Smith',
            'email' => '<EMAIL>',
            'phone' => '987654321',
            'country' => 'South Africa',
            'cover_letter' => 'I am excited about this opportunity.',
            'experience_summary' => 'I have relevant experience for this role.',
            'highest_qualification' => 'Master Degree',
            'terms_accepted' => true,
        ];

        $response = $this->actingAs($user)->post("/careers/job/{$job->slug}/apply", $applicationData);

        $this->assertDatabaseHas('job_applications', [
            'first_name' => 'Jane',
            'last_name' => 'Smith',
            'email' => '<EMAIL>',
            'job_id' => $job->id,
            'user_id' => $user->id,
        ]);

        $response->assertRedirect('/my-job-applications');
    }

    /**
     * Test job application validation.
     */
    public function test_job_application_validation(): void
    {
        $job = Job::create([
            'title' => 'Test Job',
            'description' => 'Test description',
            'requirements' => 'Test requirements',
            'responsibilities' => 'Test responsibilities',
            'location' => 'Test Location',
            'employment_type' => 'full-time',
            'experience_level' => 'entry',
            'department' => 'Test',
            'salary_period' => 'monthly',
            'is_active' => true,
        ]);

        // Test missing required fields
        $response = $this->post("/careers/job/{$job->slug}/apply", []);

        $response->assertSessionHasErrors([
            'first_name',
            'last_name',
            'email',
            'phone',
            'country',
            'cover_letter',
            'experience_summary',
            'highest_qualification',
            'terms_accepted',
        ]);
    }

    /**
     * Test application success page displays correctly.
     */
    public function test_application_success_page_displays(): void
    {
        $job = Job::create([
            'title' => 'Test Job',
            'description' => 'Test description',
            'requirements' => 'Test requirements',
            'responsibilities' => 'Test responsibilities',
            'location' => 'Test Location',
            'employment_type' => 'full-time',
            'experience_level' => 'entry',
            'department' => 'Test',
            'salary_period' => 'monthly',
            'is_active' => true,
        ]);

        $application = JobApplication::create([
            'job_id' => $job->id,
            'first_name' => 'Test',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'phone' => '123456789',
            'country' => 'South Africa',
            'cover_letter' => 'Test cover letter',
            'experience_summary' => 'Test experience',
            'highest_qualification' => 'Bachelor Degree',
        ]);

        $response = $this->get("/careers/application-success/{$application->reference_number}");

        $response->assertStatus(200);
        $response->assertSee('Application Submitted Successfully');
        $response->assertSee($application->reference_number);
        $response->assertSee('Test Job');
        $response->assertSee('Test User');
    }

    /**
     * Test inactive job returns 404.
     */
    public function test_inactive_job_returns_404(): void
    {
        $job = Job::create([
            'title' => 'Inactive Job',
            'description' => 'Test description',
            'requirements' => 'Test requirements',
            'responsibilities' => 'Test responsibilities',
            'location' => 'Test Location',
            'employment_type' => 'full-time',
            'experience_level' => 'entry',
            'department' => 'Test',
            'salary_period' => 'monthly',
            'is_active' => false,
        ]);

        $response = $this->get("/careers/job/{$job->slug}");
        $response->assertStatus(404);

        $response = $this->get("/careers/job/{$job->slug}/apply");
        $response->assertStatus(404);
    }

    /**
     * Test application status check.
     */
    public function test_application_status_check(): void
    {
        $job = Job::create([
            'title' => 'Test Job',
            'description' => 'Test description',
            'requirements' => 'Test requirements',
            'responsibilities' => 'Test responsibilities',
            'location' => 'Test Location',
            'employment_type' => 'full-time',
            'experience_level' => 'entry',
            'department' => 'Test',
            'salary_period' => 'monthly',
            'is_active' => true,
        ]);

        $application = JobApplication::create([
            'job_id' => $job->id,
            'first_name' => 'Status',
            'last_name' => 'Check',
            'email' => '<EMAIL>',
            'phone' => '123456789',
            'country' => 'South Africa',
            'cover_letter' => 'Test cover letter',
            'experience_summary' => 'Test experience',
            'highest_qualification' => 'Bachelor Degree',
        ]);

        $response = $this->post('/careers/check-status', [
            'reference_number' => $application->reference_number,
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(200);
        $response->assertSee('Status Check');
        $response->assertSee($application->reference_number);
    }

    /**
     * Test user can view their job applications.
     */
    public function test_user_can_view_their_job_applications(): void
    {
        $user = User::factory()->create();
        $job = Job::create([
            'title' => 'Test Job',
            'description' => 'Test description',
            'requirements' => 'Test requirements',
            'responsibilities' => 'Test responsibilities',
            'location' => 'Test Location',
            'employment_type' => 'full-time',
            'experience_level' => 'entry',
            'department' => 'Test',
            'salary_period' => 'monthly',
            'is_active' => true,
        ]);

        $application = JobApplication::create([
            'job_id' => $job->id,
            'user_id' => $user->id,
            'first_name' => 'User',
            'last_name' => 'Test',
            'email' => $user->email,
            'phone' => '123456789',
            'country' => 'South Africa',
            'cover_letter' => 'Test cover letter',
            'experience_summary' => 'Test experience',
            'highest_qualification' => 'Bachelor Degree',
        ]);

        $response = $this->actingAs($user)->get('/my-job-applications');

        $response->assertStatus(200);
        $response->assertSee('My Job Applications');
        $response->assertSee('Test Job');
        $response->assertSee($application->reference_number);
    }
}
