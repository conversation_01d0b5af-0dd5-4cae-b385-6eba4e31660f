@extends('layouts.dashboard')

@section('title', isset($address) ? 'Edit Address' : 'Add New Address')

@section('content')
<div class="max-w-2xl mx-auto">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex items-center">
            <a href="{{ route('addresses.index') }}"
               class="text-gray-600 hover:text-gray-800 mr-4">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                </svg>
            </a>
            <div>
                <h1 class="text-3xl font-bold text-gray-900">{{ isset($address) ? 'Edit Address' : 'Add New Address' }}</h1>
                <p class="mt-2 text-gray-600">{{ isset($address) ? 'Update your address information' : 'Add a new billing or shipping address to your account' }}</p>
            </div>
        </div>
    </div>

    <!-- Address Form -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">Address Information</h2>
        </div>

        <form action="{{ isset($address) ? route('addresses.update', $address) : route('addresses.store') }}" method="POST" class="p-6 space-y-6">
            @csrf
            @if(isset($address))
                @method('PATCH')
            @endif
            @csrf

            <!-- Address Type -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-3">Address Type</label>
                <div class="grid grid-cols-3 gap-4">
                    <label class="relative">
                        <input type="radio" name="type" value="default"
                               class="sr-only peer" {{ old('type', $address->type ?? 'default') === 'default' ? 'checked' : '' }}>
                        <div class="w-full p-4 text-center border border-gray-300 rounded-lg cursor-pointer peer-checked:border-primary-500 peer-checked:bg-primary-50 hover:bg-gray-50">
                            <svg class="w-6 h-6 mx-auto mb-2 text-gray-600 peer-checked:text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                            <span class="text-sm font-medium">General</span>
                        </div>
                    </label>
                    <label class="relative">
                        <input type="radio" name="type" value="billing"
                               class="sr-only peer" {{ old('type', $address->type ?? '') === 'billing' ? 'checked' : '' }}>
                        <div class="w-full p-4 text-center border border-gray-300 rounded-lg cursor-pointer peer-checked:border-primary-500 peer-checked:bg-primary-50 hover:bg-gray-50">
                            <svg class="w-6 h-6 mx-auto mb-2 text-gray-600 peer-checked:text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"/>
                            </svg>
                            <span class="text-sm font-medium">Billing</span>
                        </div>
                    </label>
                    <label class="relative">
                        <input type="radio" name="type" value="shipping"
                               class="sr-only peer" {{ old('type', $address->type ?? '') === 'shipping' ? 'checked' : '' }}>
                        <div class="w-full p-4 text-center border border-gray-300 rounded-lg cursor-pointer peer-checked:border-primary-500 peer-checked:bg-primary-50 hover:bg-gray-50">
                            <svg class="w-6 h-6 mx-auto mb-2 text-gray-600 peer-checked:text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>
                            </svg>
                            <span class="text-sm font-medium">Shipping</span>
                        </div>
                    </label>
                </div>
                @error('type')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Name Fields -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="first_name" class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                    <input type="text" id="first_name" name="first_name"
                           value="{{ old('first_name', $address->first_name ?? auth()->user()->first_name) }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('first_name') border-red-500 @enderror">
                    @error('first_name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="last_name" class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                    <input type="text" id="last_name" name="last_name"
                           value="{{ old('last_name', $address->last_name ?? auth()->user()->last_name) }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('last_name') border-red-500 @enderror">
                    @error('last_name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Company -->
            <div>
                <label for="company" class="block text-sm font-medium text-gray-700 mb-2">Company (Optional)</label>
                <input type="text" id="company" name="company"
                       value="{{ old('company', $address->company ?? '') }}"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('company') border-red-500 @enderror">
                @error('company')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Address Lines -->
            <div>
                <label for="address_line_1" class="block text-sm font-medium text-gray-700 mb-2">Address Line 1</label>
                <input type="text" id="address_line_1" name="address_line_1"
                       value="{{ old('address_line_1', $address->address_line_1 ?? '') }}"
                       placeholder="Street address, P.O. box, company name, c/o"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('address_line_1') border-red-500 @enderror">
                @error('address_line_1')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="address_line_2" class="block text-sm font-medium text-gray-700 mb-2">Address Line 2 (Optional)</label>
                <input type="text" id="address_line_2" name="address_line_2"
                       value="{{ old('address_line_2', $address->address_line_2 ?? '') }}"
                       placeholder="Apartment, suite, unit, building, floor, etc."
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('address_line_2') border-red-500 @enderror">
                @error('address_line_2')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- City, State, Postal Code -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label for="city" class="block text-sm font-medium text-gray-700 mb-2">City</label>
                    <input type="text" id="city" name="city" 
                           value="{{ old('city') }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('city') border-red-500 @enderror">
                    @error('city')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="state" class="block text-sm font-medium text-gray-700 mb-2">State/Province</label>
                    <input type="text" id="state" name="state" 
                           value="{{ old('state') }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('state') border-red-500 @enderror">
                    @error('state')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="postal_code" class="block text-sm font-medium text-gray-700 mb-2">Postal Code</label>
                    <input type="text" id="postal_code" name="postal_code" 
                           value="{{ old('postal_code') }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('postal_code') border-red-500 @enderror">
                    @error('postal_code')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Country and Phone -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="country" class="block text-sm font-medium text-gray-700 mb-2">Country</label>
                    <select id="country" name="country" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('country') border-red-500 @enderror">
                        <option value="">Select Country</option>
                        <option value="ZA" {{ old('country') === 'ZA' ? 'selected' : '' }}>South Africa</option>
                        <option value="US" {{ old('country') === 'US' ? 'selected' : '' }}>United States</option>
                        <option value="GB" {{ old('country') === 'GB' ? 'selected' : '' }}>United Kingdom</option>
                        <option value="CA" {{ old('country') === 'CA' ? 'selected' : '' }}>Canada</option>
                        <option value="AU" {{ old('country') === 'AU' ? 'selected' : '' }}>Australia</option>
                        <!-- Add more countries as needed -->
                    </select>
                    @error('country')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number (Optional)</label>
                    <input type="tel" id="phone" name="phone" 
                           value="{{ old('phone', auth()->user()->phone) }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('phone') border-red-500 @enderror">
                    @error('phone')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Default Address -->
            <div class="flex items-center">
                <input type="checkbox" id="is_default" name="is_default" value="1" 
                       {{ old('is_default') ? 'checked' : '' }}
                       class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                <label for="is_default" class="ml-2 text-sm text-gray-700">
                    Set as default address
                </label>
            </div>

            <!-- Submit Buttons -->
            <div class="flex justify-between items-center pt-6 border-t border-gray-200">
                <a href="{{ route('addresses.index') }}" 
                   class="text-gray-600 hover:text-gray-800 transition-colors">
                    Cancel
                </a>
                <button type="submit" 
                        class="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors">
                    Save Address
                </button>
            </div>
        </form>
    </div>
</div>
@endsection
