<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Translation extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'translatable_type',
        'translatable_id',
        'language_id',
        'field_name',
        'field_value',
    ];

    /**
     * Get the translatable model.
     */
    public function translatable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the language that owns the translation.
     */
    public function language(): BelongsTo
    {
        return $this->belongsTo(Language::class);
    }

    /**
     * Get translation for a specific model, field, and language.
     */
    public static function getTranslation($model, string $field, string $languageCode): ?string
    {
        $language = Language::findByCode($languageCode);

        if (!$language) {
            return null;
        }

        $translation = static::where('translatable_type', get_class($model))
            ->where('translatable_id', $model->id)
            ->where('language_id', $language->id)
            ->where('field_name', $field)
            ->first();

        return $translation ? $translation->field_value : null;
    }

    /**
     * Set translation for a specific model, field, and language.
     */
    public static function setTranslation($model, string $field, string $languageCode, string $value): void
    {
        $language = Language::findByCode($languageCode);

        if (!$language) {
            return;
        }

        static::updateOrCreate([
            'translatable_type' => get_class($model),
            'translatable_id' => $model->id,
            'language_id' => $language->id,
            'field_name' => $field,
        ], [
            'field_value' => $value,
        ]);
    }
}
