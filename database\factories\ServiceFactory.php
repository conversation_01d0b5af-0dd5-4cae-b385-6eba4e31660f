<?php

namespace Database\Factories;

use App\Models\Service;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Service>
 */
class ServiceFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $serviceNames = [
            'Web Development',
            'Mobile App Development',
            'E-commerce Solutions',
            'Digital Marketing',
            'SEO Optimization',
            'Brand Identity Design',
            'UI/UX Design',
            'Content Management Systems',
            'Cloud Solutions',
            'IT Consulting',
            'Database Design',
            'API Development',
            'Social Media Management',
            'Email Marketing',
            'Website Maintenance',
            'Security Auditing',
            'Performance Optimization',
            'Custom Software Development',
        ];

        $name = $this->faker->randomElement($serviceNames);
        $slug = Str::slug($name) . '-' . $this->faker->randomNumber(3);

        $features = [
            'Responsive Design',
            'Mobile Optimization',
            'SEO Friendly',
            'Fast Loading',
            'Secure Implementation',
            'User-Friendly Interface',
            'Cross-Browser Compatible',
            'Analytics Integration',
            'Content Management',
            'Social Media Integration',
            'Payment Gateway Integration',
            'Multi-language Support',
            '24/7 Support',
            'Regular Updates',
            'Custom Development',
            'Professional Design',
            'Quality Assurance',
            'Documentation Included',
        ];

        $icons = [
            'fas fa-laptop-code',
            'fas fa-mobile-alt',
            'fas fa-shopping-cart',
            'fas fa-bullhorn',
            'fas fa-search',
            'fas fa-palette',
            'fas fa-pencil-ruler',
            'fas fa-cogs',
            'fas fa-cloud',
            'fas fa-chart-line',
            'fas fa-database',
            'fas fa-code',
            'fas fa-share-alt',
            'fas fa-envelope',
            'fas fa-tools',
            'fas fa-shield-alt',
            'fas fa-tachometer-alt',
            'fas fa-desktop',
        ];

        return [
            'uuid' => $this->faker->uuid(),
            'name' => $name,
            'slug' => $slug,
            'short_description' => $this->faker->sentence(10),
            'description' => $this->faker->paragraphs(3, true),
            'icon' => $this->faker->randomElement($icons),
            'featured_image' => $this->faker->optional(0.7)->imageUrl(800, 600, 'business'),
            'gallery' => $this->faker->optional(0.5)->randomElements([
                $this->faker->imageUrl(800, 600, 'business'),
                $this->faker->imageUrl(800, 600, 'technology'),
                $this->faker->imageUrl(800, 600, 'abstract'),
            ], $this->faker->numberBetween(1, 3)),
            'price_from' => $this->faker->optional(0.8)->randomFloat(2, 500, 10000),
            'features' => $this->faker->randomElements($features, $this->faker->numberBetween(3, 8)),
            'is_featured' => $this->faker->boolean(20), // 20% chance of being featured
            'is_active' => $this->faker->boolean(90), // 90% chance of being active
            'is_deleted' => false,
            'sort_order' => $this->faker->numberBetween(0, 100),
            'meta_title' => $name . ' - Professional ' . $name . ' Services',
            'meta_description' => 'Professional ' . $name . ' services. ' . $this->faker->sentence(8),
            'meta_keywords' => strtolower(str_replace(' ', ', ', $name)) . ', professional services, digital agency',
            'created_at' => $this->faker->dateTimeBetween('-2 years', 'now'),
            'updated_at' => function (array $attributes) {
                return $this->faker->dateTimeBetween($attributes['created_at'], 'now');
            },
        ];
    }

    /**
     * Indicate that the service is featured.
     */
    public function featured(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_featured' => true,
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the service is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the service is deleted.
     */
    public function deleted(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_deleted' => true,
        ]);
    }

    /**
     * Indicate that the service has a high price.
     */
    public function expensive(): static
    {
        return $this->state(fn (array $attributes) => [
            'price_from' => $this->faker->randomFloat(2, 5000, 25000),
        ]);
    }

    /**
     * Indicate that the service has no price (contact for quote).
     */
    public function contactForQuote(): static
    {
        return $this->state(fn (array $attributes) => [
            'price_from' => null,
        ]);
    }

    /**
     * Create a web development service.
     */
    public function webDevelopment(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Web Development',
            'slug' => 'web-development-' . $this->faker->randomNumber(3),
            'short_description' => 'Professional web development services for modern businesses.',
            'description' => 'We create stunning, responsive websites that drive results. Our web development services include custom design, responsive layouts, content management systems, and ongoing support.',
            'icon' => 'fas fa-laptop-code',
            'features' => [
                'Responsive Design',
                'Content Management System',
                'SEO Optimization',
                'Mobile Friendly',
                'Fast Loading',
                'Security Features',
                'Analytics Integration',
                'Social Media Integration',
            ],
            'price_from' => 2500.00,
            'is_featured' => true,
            'is_active' => true,
        ]);
    }

    /**
     * Create a mobile app development service.
     */
    public function mobileAppDevelopment(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Mobile App Development',
            'slug' => 'mobile-app-development-' . $this->faker->randomNumber(3),
            'short_description' => 'Native and cross-platform mobile app development.',
            'description' => 'Transform your business with custom mobile applications. We develop native iOS and Android apps, as well as cross-platform solutions using the latest technologies.',
            'icon' => 'fas fa-mobile-alt',
            'features' => [
                'Native iOS Development',
                'Native Android Development',
                'Cross-Platform Solutions',
                'App Store Optimization',
                'Push Notifications',
                'Offline Functionality',
                'API Integration',
                'User Analytics',
            ],
            'price_from' => 8000.00,
            'is_featured' => true,
            'is_active' => true,
        ]);
    }

    /**
     * Create an e-commerce service.
     */
    public function ecommerce(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'E-commerce Solutions',
            'slug' => 'ecommerce-solutions-' . $this->faker->randomNumber(3),
            'short_description' => 'Complete e-commerce solutions for online businesses.',
            'description' => 'Launch your online store with our comprehensive e-commerce solutions. We provide everything from product catalogs to payment processing and inventory management.',
            'icon' => 'fas fa-shopping-cart',
            'features' => [
                'Product Catalog Management',
                'Shopping Cart Functionality',
                'Payment Gateway Integration',
                'Inventory Management',
                'Order Processing',
                'Customer Accounts',
                'Shipping Integration',
                'Sales Analytics',
            ],
            'price_from' => 5000.00,
            'is_featured' => true,
            'is_active' => true,
        ]);
    }

    /**
     * Create a digital marketing service.
     */
    public function digitalMarketing(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Digital Marketing',
            'slug' => 'digital-marketing-' . $this->faker->randomNumber(3),
            'short_description' => 'Comprehensive digital marketing strategies to grow your business.',
            'description' => 'Boost your online presence with our digital marketing services. We offer SEO, social media marketing, content creation, and paid advertising campaigns.',
            'icon' => 'fas fa-bullhorn',
            'features' => [
                'Search Engine Optimization',
                'Social Media Marketing',
                'Content Creation',
                'Pay-Per-Click Advertising',
                'Email Marketing',
                'Analytics & Reporting',
                'Brand Strategy',
                'Conversion Optimization',
            ],
            'price_from' => 1500.00,
            'is_featured' => false,
            'is_active' => true,
        ]);
    }
}
