<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\DashboardCacheService;

class WarmUpDashboardCaches extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'dashboard:warm-cache {--force : Force cache refresh}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Warm up dashboard caches for better performance';

    /**
     * Execute the console command.
     */
    public function handle(DashboardCacheService $cacheService)
    {
        $this->info('Starting dashboard cache warm-up...');

        if ($this->option('force')) {
            $this->info('Clearing existing caches...');
            $cacheService->invalidateAllDashboards();
        }

        $this->info('Warming up admin dashboard caches...');
        $cacheService->warmUpCaches();

        $this->info('Dashboard caches warmed up successfully!');

        return Command::SUCCESS;
    }
}
