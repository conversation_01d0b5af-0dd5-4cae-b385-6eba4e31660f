<?php

namespace App\Http\Requests\Auth;

use Illuminate\Foundation\Http\FormRequest;

class RegisterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'first_name' => [
                'required',
                'string',
                'max:255',
            ],
            'last_name' => [
                'required',
                'string',
                'max:255',
            ],
            'email' => [
                'required',
                'string',
                'email',
                'max:255',
                'unique:users,email',
            ],
            'password' => [
                'required',
                'string',
                'confirmed',
                'min:8',
            ],
            'password_confirmation' => [
                'required',
                'string',
            ],
            'phone' => [
                'nullable',
                'string',
                'max:20',
            ],
            'terms' => [
                'required',
                'accepted',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'first_name.required' => __('validation.required', ['attribute' => __('auth.first_name')]),
            'first_name.regex' => __('validation.regex', ['attribute' => __('auth.first_name')]),
            'last_name.required' => __('validation.required', ['attribute' => __('auth.last_name')]),
            'last_name.regex' => __('validation.regex', ['attribute' => __('auth.last_name')]),
            'email.required' => __('validation.required', ['attribute' => __('auth.email')]),
            'email.email' => __('validation.email', ['attribute' => __('auth.email')]),
            'email.unique' => __('validation.unique', ['attribute' => __('auth.email')]),
            'password.required' => __('validation.required', ['attribute' => __('auth.password')]),
            'password.confirmed' => __('validation.confirmed', ['attribute' => __('auth.password')]),
            'password_confirmation.required' => __('validation.required', ['attribute' => __('auth.password_confirmation')]),
            'phone.regex' => __('validation.regex', ['attribute' => __('auth.phone')]),
            'terms.required' => __('validation.required', ['attribute' => __('auth.terms')]),
            'terms.accepted' => __('validation.accepted', ['attribute' => __('auth.terms')]),
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'first_name' => __('auth.first_name'),
            'last_name' => __('auth.last_name'),
            'email' => __('auth.email'),
            'password' => __('auth.password'),
            'password_confirmation' => __('auth.password_confirmation'),
            'phone' => __('auth.phone'),
            'terms' => __('auth.terms_and_conditions'),
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'first_name' => trim($this->first_name),
            'last_name' => trim($this->last_name),
            'email' => strtolower(trim($this->email)),
            'phone' => $this->phone ? trim($this->phone) : null,
            'terms' => $this->boolean('terms'),
        ]);
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Check for suspicious patterns in names
            if ($this->hasSuspiciousContent()) {
                $validator->errors()->add('first_name', __('validation.invalid_content'));
            }

            // Additional security checks
            if ($this->isDisposableEmail()) {
                $validator->errors()->add('email', __('validation.disposable_email'));
            }
        });
    }

    /**
     * Check if the content contains suspicious patterns.
     */
    protected function hasSuspiciousContent(): bool
    {
        $suspiciousPatterns = [
            '/\b(admin|root|test|null|undefined)\b/i',
            '/[<>{}]/', // HTML/script tags
            '/javascript:/i',
            '/\bon\w+\s*=/i', // Event handlers
        ];

        $content = $this->first_name . ' ' . $this->last_name;

        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if the email is from a disposable email provider.
     */
    protected function isDisposableEmail(): bool
    {
        // List of common disposable email domains
        $disposableDomains = [
            '10minutemail.com',
            'tempmail.org',
            'guerrillamail.com',
            'mailinator.com',
            'throwaway.email',
        ];

        $emailDomain = substr(strrchr($this->email, "@"), 1);

        return in_array(strtolower($emailDomain), $disposableDomains);
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator): void
    {
        // Log failed registration attempts for security monitoring
        \Log::error('Registration validation failed', [
            'email' => $this->input('email'),
            'ip' => $this->ip(),
            'user_agent' => $this->userAgent(),
            'request_data' => $this->except(['password', 'password_confirmation']),
            'validation_rules' => $this->rules(),
            'errors' => $validator->errors()->toArray(),
        ]);

        // Handle AJAX requests
        if ($this->expectsJson()) {
            $response = response()->json([
                'success' => false,
                'message' => 'Validation failed: ' . $validator->errors()->first(),
                'errors' => $validator->errors(),
                'debug_info' => [
                    'request_data' => $this->except(['password', 'password_confirmation']),
                    'validation_rules' => $this->rules(),
                ]
            ], 422);

            throw new \Illuminate\Http\Exceptions\HttpResponseException($response);
        }

        parent::failedValidation($validator);
    }
}
