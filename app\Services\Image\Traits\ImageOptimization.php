<?php

namespace App\Services\Image\Traits;

use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;
use Spatie\ImageOptimizer\OptimizerChainFactory;

trait ImageOptimization
{
    /**
     * Optimize image for web delivery
     */
    public function optimizeImage(string $imagePath, array $options = []): bool
    {
        try {
            $quality = $options['quality'] ?? config('image.optimization.jpeg_quality', 85);
            $stripMetadata = $options['strip_metadata'] ?? config('image.optimization.strip_metadata', true);
            
            // Load image with Intervention Image
            $manager = new ImageManager(new Driver());
            $image = $manager->read($imagePath);
            
            // Get image format
            $format = $this->getImageFormat($imagePath);
            
            // Apply format-specific optimizations
            switch ($format) {
                case 'jpeg':
                case 'jpg':
                    $image->toJpeg($quality);
                    if (config('image.optimization.progressive_jpeg', true)) {
                        $image->toJpeg($quality)->save($imagePath);
                    }
                    break;
                    
                case 'png':
                    $compression = $options['compression'] ?? config('image.optimization.png_compression', 6);
                    $image->toPng()->save($imagePath);
                    break;
                    
                case 'webp':
                    $webpQuality = $options['webp_quality'] ?? config('image.optimization.webp_quality', 80);
                    $image->toWebp($webpQuality)->save($imagePath);
                    break;
                    
                case 'gif':
                    // GIF optimization is limited, just save as-is
                    $image->toGif()->save($imagePath);
                    break;
            }
            
            // Strip EXIF data if enabled
            if ($stripMetadata && config('image.security.remove_exif_data', true)) {
                $this->stripExifData($imagePath);
            }
            
            // Use Spatie Image Optimizer for additional optimization
            if (config('image.enable_optimization', true)) {
                $optimizerChain = OptimizerChainFactory::create();
                $optimizerChain->optimize($imagePath);
            }
            
            $this->logProcessing("Image optimized: {$imagePath}");
            return true;
            
        } catch (\Exception $e) {
            $this->logError("Image optimization failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Resize image to specified dimensions
     */
    public function resizeImage(string $imagePath, int $width, int $height, array $options = []): bool
    {
        try {
            $crop = $options['crop'] ?? false;
            $maintainAspectRatio = $options['maintain_aspect_ratio'] ?? true;
            $upsize = $options['upsize'] ?? false;
            
            $manager = new ImageManager(new Driver());
            $image = $manager->read($imagePath);
            
            if ($crop) {
                // Crop to exact dimensions
                $image->cover($width, $height);
            } else {
                // Resize maintaining aspect ratio
                if ($maintainAspectRatio) {
                    $image->scale($width, $height);
                } else {
                    $image->resize($width, $height);
                }
            }
            
            // Save the resized image
            $image->save($imagePath);
            
            $this->logProcessing("Image resized: {$imagePath} to {$width}x{$height}");
            return true;
            
        } catch (\Exception $e) {
            $this->logError("Image resize failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create multiple size variants of an image
     */
    public function createSizeVariants(string $originalPath, string $baseName, array $sizes = []): array
    {
        $variants = [];
        $sizes = $sizes ?: config('image.sizes', []);
        
        foreach ($sizes as $sizeName => $sizeConfig) {
            try {
                $variantPath = $this->generateVariantPath($originalPath, $baseName, $sizeName);
                
                // Copy original to variant path
                copy($originalPath, $variantPath);
                
                // Resize the variant
                $resized = $this->resizeImage(
                    $variantPath,
                    $sizeConfig['width'],
                    $sizeConfig['height'],
                    [
                        'crop' => $sizeConfig['crop'] ?? false,
                        'quality' => $sizeConfig['quality'] ?? config('image.default_quality', 85)
                    ]
                );
                
                if ($resized) {
                    $variants[$sizeName] = $variantPath;
                    
                    // Create WebP version if enabled
                    if (config('image.enable_webp_conversion', true)) {
                        $webpPath = $this->convertToWebP($variantPath);
                        if ($webpPath) {
                            $variants[$sizeName . '_webp'] = $webpPath;
                        }
                    }
                }
                
            } catch (\Exception $e) {
                $this->logError("Failed to create size variant {$sizeName}: " . $e->getMessage());
            }
        }
        
        return $variants;
    }
    
    /**
     * Convert image to WebP format
     */
    public function convertToWebP(string $imagePath, int $quality = null): ?string
    {
        try {
            $quality = $quality ?? config('image.optimization.webp_quality', 80);
            $webpPath = $this->getWebPPath($imagePath);
            
            $manager = new ImageManager(new Driver());
            $image = $manager->read($imagePath);
            
            $image->toWebp($quality)->save($webpPath);
            
            $this->logProcessing("WebP conversion completed: {$webpPath}");
            return $webpPath;
            
        } catch (\Exception $e) {
            $this->logError("WebP conversion failed: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Strip EXIF data from image
     */
    protected function stripExifData(string $imagePath): bool
    {
        try {
            if (function_exists('exif_read_data') && exif_imagetype($imagePath)) {
                $manager = new ImageManager(new Driver());
                $image = $manager->read($imagePath);
                
                // Save without EXIF data
                $image->save($imagePath);
                
                return true;
            }
            
            return false;
            
        } catch (\Exception $e) {
            $this->logError("EXIF stripping failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get WebP file path
     */
    protected function getWebPPath(string $originalPath): string
    {
        $pathInfo = pathinfo($originalPath);
        return $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '.webp';
    }
    
    /**
     * Generate variant file path
     */
    protected function generateVariantPath(string $originalPath, string $baseName, string $sizeName): string
    {
        $pathInfo = pathinfo($originalPath);
        return $pathInfo['dirname'] . '/' . $baseName . '_' . $sizeName . '.' . $pathInfo['extension'];
    }
    
    /**
     * Get image format from file
     */
    protected function getImageFormat(string $imagePath): string
    {
        $imageInfo = getimagesize($imagePath);
        
        if ($imageInfo === false) {
            return 'unknown';
        }
        
        switch ($imageInfo[2]) {
            case IMAGETYPE_JPEG:
                return 'jpeg';
            case IMAGETYPE_PNG:
                return 'png';
            case IMAGETYPE_GIF:
                return 'gif';
            case IMAGETYPE_WEBP:
                return 'webp';
            case IMAGETYPE_BMP:
                return 'bmp';
            default:
                return 'unknown';
        }
    }
}
