<?php

namespace App\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * @method static array processUploadedImage(\Illuminate\Http\UploadedFile $file, array $options = [])
 * @method static array processImageFromPath(string $imagePath, array $options = [])
 * @method static array quickUpload(\Illuminate\Http\UploadedFile $file, string $subdirectory = 'uploads')
 * @method static array fullUpload(\Illuminate\Http\UploadedFile $file, string $subdirectory = 'uploads')
 * @method static bool optimizeImage(string $imagePath, array $options = [])
 * @method static bool resizeImage(string $imagePath, int $width, int $height, array $options = [])
 * @method static array createSizeVariants(string $originalPath, string $baseName, array $sizes = [])
 * @method static string|null convertToWebP(string $imagePath, int $quality = null)
 * @method static array validateImageFile(\Illuminate\Http\UploadedFile $file)
 * @method static array scanForViruses(string $filePath)
 * @method static string sanitizeFilename(string $filename)
 * @method static string getImageUrl(string $path)
 * @method static bool deleteImage(string $path, bool $deleteVariants = true)
 * @method static array getImageInfo(string $path)
 *
 * @see \App\Services\ImageService
 */
class ImageService extends Facade
{
    /**
     * Get the registered name of the component.
     */
    protected static function getFacadeAccessor(): string
    {
        return \App\Services\ImageService::class;
    }
}
