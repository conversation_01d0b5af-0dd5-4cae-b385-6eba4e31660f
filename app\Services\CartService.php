<?php

namespace App\Services;

use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\ShoppingCart;
use App\Models\CartItem;
use App\Models\User;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;

class CartService
{
    /**
     * Get or create cart for user or session.
     */
    public function getCart(?User $user = null): ShoppingCart
    {
        if ($user) {
            // Get user's active cart
            $cart = ShoppingCart::where('user_id', $user->id)
                ->where('expires_at', '>', now())
                ->orWhereNull('expires_at')
                ->first();
        } else {
            // Get session cart
            $sessionId = Session::getId();
            $cart = ShoppingCart::where('session_id', $sessionId)
                ->where('expires_at', '>', now())
                ->orWhereNull('expires_at')
                ->first();
        }

        if (!$cart) {
            $cart = $this->createCart($user);
        }

        return $cart;
    }

    /**
     * Create a new cart.
     */
    public function createCart(?User $user = null): ShoppingCart
    {
        return ShoppingCart::create([
            'uuid' => Str::uuid(),
            'user_id' => $user?->id,
            'session_id' => $user ? null : Session::getId(),
            'currency_code' => config('app.default_currency', 'ZAR'),
            'expires_at' => $user ? null : now()->addDays(30), // Session carts expire
        ]);
    }

    /**
     * Add item to cart.
     */
    public function addItem(
        ShoppingCart $cart,
        Product $product,
        int $quantity = 1,
        ?ProductVariant $variant = null
    ): CartItem {
        // Check if item already exists in cart
        $existingItem = $cart->items()
            ->where('product_id', $product->id)
            ->where('variant_id', $variant?->id)
            ->first();

        if ($existingItem) {
            // Update quantity
            $existingItem->quantity += $quantity;
            $existingItem->total_price = $existingItem->quantity * $existingItem->unit_price;
            $existingItem->save();
            
            $this->updateCartTotals($cart);
            return $existingItem;
        }

        // Create new cart item
        $unitPrice = $variant ? $variant->price ?? $product->price : $product->price;
        
        $cartItem = $cart->items()->create([
            'product_id' => $product->id,
            'variant_id' => $variant?->id,
            'quantity' => $quantity,
            'unit_price' => $unitPrice,
            'total_price' => $quantity * $unitPrice,
        ]);

        $this->updateCartTotals($cart);
        return $cartItem;
    }

    /**
     * Update item quantity in cart.
     */
    public function updateItemQuantity(CartItem $cartItem, int $quantity): CartItem
    {
        if ($quantity <= 0) {
            return $this->removeItem($cartItem);
        }

        $cartItem->quantity = $quantity;
        $cartItem->total_price = $cartItem->quantity * $cartItem->unit_price;
        $cartItem->save();

        $this->updateCartTotals($cartItem->shoppingCart);
        return $cartItem;
    }

    /**
     * Remove item from cart.
     */
    public function removeItem(CartItem $cartItem): CartItem
    {
        $cart = $cartItem->shoppingCart;
        $cartItem->delete();
        
        $this->updateCartTotals($cart);
        return $cartItem;
    }

    /**
     * Clear all items from cart.
     */
    public function clearCart(ShoppingCart $cart): void
    {
        $cart->items()->delete();
        $this->updateCartTotals($cart);
    }

    /**
     * Update cart totals.
     */
    public function updateCartTotals(ShoppingCart $cart): void
    {
        $subtotal = $cart->items()->sum('total_price');
        
        // Calculate tax (implement your tax logic here)
        $taxRate = config('shop.tax_rate', 0.15); // 15% VAT
        $taxAmount = $subtotal * $taxRate;
        
        // Calculate shipping (implement your shipping logic here)
        $shippingAmount = $this->calculateShipping($cart, $subtotal);
        
        // Apply discounts (implement your discount logic here)
        $discountAmount = $this->calculateDiscounts($cart, $subtotal);
        
        $total = $subtotal + $taxAmount + $shippingAmount - $discountAmount;

        $cart->update([
            'subtotal' => $subtotal,
            'tax_amount' => $taxAmount,
            'shipping_amount' => $shippingAmount,
            'discount_amount' => $discountAmount,
            'total' => $total,
        ]);
    }

    /**
     * Calculate shipping cost.
     */
    protected function calculateShipping(ShoppingCart $cart, float $subtotal): float
    {
        // Free shipping over certain amount
        $freeShippingThreshold = config('shop.free_shipping_threshold', 500);
        if ($subtotal >= $freeShippingThreshold) {
            return 0;
        }

        // Flat rate shipping
        return config('shop.shipping_rate', 50);
    }

    /**
     * Calculate discounts.
     */
    protected function calculateDiscounts(ShoppingCart $cart, float $subtotal): float
    {
        // Implement discount logic here
        // This could include coupon codes, bulk discounts, etc.
        return 0;
    }

    /**
     * Get cart item count.
     */
    public function getItemCount(ShoppingCart $cart): int
    {
        return $cart->items()->sum('quantity');
    }

    /**
     * Transfer session cart to user cart.
     */
    public function transferSessionCartToUser(User $user): void
    {
        $sessionId = Session::getId();
        $sessionCart = ShoppingCart::where('session_id', $sessionId)->first();
        
        if (!$sessionCart || $sessionCart->items()->count() === 0) {
            return;
        }

        $userCart = $this->getCart($user);
        
        // Transfer items
        foreach ($sessionCart->items as $sessionItem) {
            $this->addItem(
                $userCart,
                $sessionItem->product,
                $sessionItem->quantity,
                $sessionItem->variant
            );
        }

        // Delete session cart
        $sessionCart->items()->delete();
        $sessionCart->delete();
    }

    /**
     * Check if cart has items.
     */
    public function hasItems(ShoppingCart $cart): bool
    {
        return $cart->items()->count() > 0;
    }

    /**
     * Get cart summary.
     */
    public function getCartSummary(ShoppingCart $cart): array
    {
        return [
            'item_count' => $this->getItemCount($cart),
            'subtotal' => $cart->subtotal,
            'tax_amount' => $cart->tax_amount,
            'shipping_amount' => $cart->shipping_amount,
            'discount_amount' => $cart->discount_amount,
            'total' => $cart->total,
            'formatted_subtotal' => 'R' . number_format($cart->subtotal, 2),
            'formatted_tax' => 'R' . number_format($cart->tax_amount, 2),
            'formatted_shipping' => 'R' . number_format($cart->shipping_amount, 2),
            'formatted_discount' => 'R' . number_format($cart->discount_amount, 2),
            'formatted_total' => 'R' . number_format($cart->total, 2),
        ];
    }
}
