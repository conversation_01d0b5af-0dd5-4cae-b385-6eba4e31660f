<?php

namespace App\Services;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\ShoppingCart;
use App\Models\User;
use App\Models\Currency;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

class OrderService
{
    /**
     * Create order from cart.
     */
    public function createOrderFromCart(
        ShoppingCart $cart,
        array $customerData,
        array $billingAddress,
        array $shippingAddress,
        ?User $user = null
    ): Order {
        return DB::transaction(function () use ($cart, $customerData, $billingAddress, $shippingAddress, $user) {
            // Get currency
            $currency = Currency::where('code', $cart->currency_code)->first();
            
            // Create order
            $order = Order::create([
                'uuid' => Str::uuid(),
                'order_number' => $this->generateOrderNumber(),
                'user_id' => $user?->id,
                'email' => $customerData['email'],
                'phone' => $customerData['phone'] ?? null,
                'status' => 'pending',
                'payment_status' => 'pending',
                'currency_id' => $currency?->id,
                'subtotal' => $cart->subtotal,
                'tax_amount' => $cart->tax_amount,
                'shipping_amount' => $cart->shipping_amount,
                'discount_amount' => $cart->discount_amount,
                'total_amount' => $cart->total,
                'billing_address' => $billingAddress,
                'shipping_address' => $shippingAddress,
                'notes' => $customerData['notes'] ?? null,
            ]);

            // Create order items
            foreach ($cart->items as $cartItem) {
                $this->createOrderItem($order, $cartItem);
            }

            // Clear cart
            $cart->items()->delete();
            $cart->delete();

            return $order;
        });
    }

    /**
     * Create order item from cart item.
     */
    protected function createOrderItem(Order $order, $cartItem): OrderItem
    {
        // Create product snapshot
        $productSnapshot = [
            'name' => $cartItem->product->name,
            'sku' => $cartItem->product->sku,
            'description' => $cartItem->product->short_description,
            'image' => $cartItem->product->featured_image,
        ];

        if ($cartItem->variant) {
            $productSnapshot['variant_name'] = $cartItem->variant->name;
            $productSnapshot['variant_sku'] = $cartItem->variant->sku;
            $productSnapshot['variant_attributes'] = $cartItem->variant->attributes;
        }

        return OrderItem::create([
            'order_id' => $order->id,
            'product_id' => $cartItem->product_id,
            'variant_id' => $cartItem->variant_id,
            'quantity' => $cartItem->quantity,
            'unit_price' => $cartItem->unit_price,
            'total_price' => $cartItem->total_price,
            'product_snapshot' => $productSnapshot,
        ]);
    }

    /**
     * Generate unique order number.
     */
    public function generateOrderNumber(): string
    {
        $prefix = config('shop.order_prefix', 'CHI');
        $date = now()->format('Ymd');
        
        // Get last order number for today
        $lastOrder = Order::where('order_number', 'like', $prefix . $date . '%')
            ->orderBy('order_number', 'desc')
            ->first();

        if ($lastOrder) {
            $lastNumber = (int) substr($lastOrder->order_number, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . $date . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Update order status.
     */
    public function updateOrderStatus(Order $order, string $status): Order
    {
        $order->update(['status' => $status]);

        // Handle status-specific logic
        switch ($status) {
            case 'shipped':
                $order->update(['shipped_at' => now()]);
                // Send shipping notification
                break;
            case 'delivered':
                $order->update(['delivered_at' => now()]);
                // Send delivery confirmation
                break;
            case 'cancelled':
                // Handle inventory restoration
                $this->restoreInventory($order);
                break;
        }

        return $order;
    }

    /**
     * Update payment status.
     */
    public function updatePaymentStatus(Order $order, string $paymentStatus): Order
    {
        $order->update(['payment_status' => $paymentStatus]);

        if ($paymentStatus === 'paid') {
            $order->update(['paid_at' => now()]);
            
            // Reduce inventory
            $this->reduceInventory($order);
            
            // Auto-confirm order if payment is successful
            if ($order->status === 'pending') {
                $this->updateOrderStatus($order, 'confirmed');
            }
        }

        return $order;
    }

    /**
     * Reduce inventory for order items.
     */
    protected function reduceInventory(Order $order): void
    {
        foreach ($order->orderItems as $item) {
            if ($item->variant) {
                // Reduce variant inventory
                $item->variant->decrement('inventory_quantity', $item->quantity);
            } else {
                // Reduce product inventory
                $item->product->decrement('inventory_quantity', $item->quantity);
            }
        }
    }

    /**
     * Restore inventory for cancelled orders.
     */
    protected function restoreInventory(Order $order): void
    {
        foreach ($order->orderItems as $item) {
            if ($item->variant) {
                // Restore variant inventory
                $item->variant->increment('inventory_quantity', $item->quantity);
            } else {
                // Restore product inventory
                $item->product->increment('inventory_quantity', $item->quantity);
            }
        }
    }

    /**
     * Calculate order totals.
     */
    public function calculateOrderTotals(Order $order): array
    {
        $subtotal = $order->orderItems->sum('total_price');
        
        // Recalculate tax
        $taxRate = config('shop.tax_rate', 0.15);
        $taxAmount = $subtotal * $taxRate;
        
        // Recalculate shipping
        $shippingAmount = $this->calculateShipping($order, $subtotal);
        
        // Apply discounts
        $discountAmount = $order->discount_amount ?? 0;
        
        $total = $subtotal + $taxAmount + $shippingAmount - $discountAmount;

        return [
            'subtotal' => $subtotal,
            'tax_amount' => $taxAmount,
            'shipping_amount' => $shippingAmount,
            'discount_amount' => $discountAmount,
            'total_amount' => $total,
        ];
    }

    /**
     * Calculate shipping for order.
     */
    protected function calculateShipping(Order $order, float $subtotal): float
    {
        // Free shipping over certain amount
        $freeShippingThreshold = config('shop.free_shipping_threshold', 500);
        if ($subtotal >= $freeShippingThreshold) {
            return 0;
        }

        return config('shop.shipping_rate', 50);
    }

    /**
     * Get order summary.
     */
    public function getOrderSummary(Order $order): array
    {
        return [
            'order_number' => $order->order_number,
            'status' => $order->status,
            'payment_status' => $order->payment_status,
            'item_count' => $order->orderItems->sum('quantity'),
            'subtotal' => $order->subtotal,
            'tax_amount' => $order->tax_amount,
            'shipping_amount' => $order->shipping_amount,
            'discount_amount' => $order->discount_amount,
            'total_amount' => $order->total_amount,
            'formatted_subtotal' => 'R' . number_format($order->subtotal, 2),
            'formatted_tax' => 'R' . number_format($order->tax_amount, 2),
            'formatted_shipping' => 'R' . number_format($order->shipping_amount, 2),
            'formatted_discount' => 'R' . number_format($order->discount_amount, 2),
            'formatted_total' => 'R' . number_format($order->total_amount, 2),
        ];
    }

    /**
     * Check if order can be cancelled.
     */
    public function canBeCancelled(Order $order): bool
    {
        return in_array($order->status, ['pending', 'confirmed']) && 
               $order->payment_status !== 'paid';
    }

    /**
     * Check if order can be refunded.
     */
    public function canBeRefunded(Order $order): bool
    {
        return $order->payment_status === 'paid' && 
               in_array($order->status, ['confirmed', 'processing', 'shipped']);
    }
}
