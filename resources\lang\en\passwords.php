<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Password Reset Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines are the default lines which match reasons
    | that are given by the password broker for a password update attempt
    | has failed, such as for an invalid token or invalid new password.
    |
    */

    'reset' => 'Your password has been reset successfully! You can now log in with your new password.',
    'sent' => 'We have sent your password reset link to your email address. Please check your inbox and follow the instructions.',
    'sent_secure' => 'If an account with that email address exists, you will receive a password reset link shortly. Please check your inbox and spam folder.',
    'throttled' => 'Please wait before retrying. Too many password reset attempts.',
    'token' => 'This password reset token is invalid or has expired. Please request a new password reset link.',
    'user' => "We can't find a user with that email address. Please check your email and try again.",

];
