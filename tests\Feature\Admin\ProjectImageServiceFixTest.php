<?php

namespace Tests\Feature\Admin;

use App\Models\Project;
use App\Models\User;
use App\Models\Role;
use App\Services\ActivityLogger;
use App\Services\ImageService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class ProjectImageServiceFixTest extends TestCase
{
    use RefreshDatabase;

    protected User $admin;
    protected User $client;

    protected function setUp(): void
    {
        parent::setUp();

        // Create roles
        $adminRole = Role::firstOrCreate(['name' => 'admin'], [
            'slug' => 'admin',
            'description' => 'Administrator role',
            'permissions' => ['*'],
            'is_active' => true
        ]);
        $clientRole = Role::firstOrCreate(['name' => 'client'], [
            'slug' => 'customer',
            'description' => 'Customer role with appropriate permissions',
            'permissions' => [
                'profile' => ['read', 'update'],
                'projects' => ['create', 'read'],
                'orders' => ['create', 'read'],
                'dashboard' => ['access']
            ],
            'is_active' => true
        ]);

        // Create users
        $this->admin = User::factory()->create(['role_id' => $adminRole->id]);
        $this->client = User::factory()->create(['role_id' => $clientRole->id]);

        // Mock services
        $this->mock(ActivityLogger::class, function ($mock) {
            $mock->shouldReceive('logCustomerActivity')->andReturn(\App\Models\ActivityLog::factory()->make());
        });
    }

    /** @test */
    public function project_update_uses_correct_image_service_delete_method()
    {
        Storage::fake('public');
        
        $project = Project::factory()->create([
            'client_id' => $this->client->id,
            'featured_image' => 'projects/old-image.jpg'
        ]);

        // Mock ImageService with correct method calls
        $imageService = $this->mock(ImageService::class);
        $imageService->shouldReceive('deleteImage')
            ->once()
            ->with('projects/old-image.jpg', true)
            ->andReturn(true);
        $imageService->shouldReceive('processUploadedImage')
            ->once()
            ->andReturn([
                'success' => true,
                'original_path' => 'projects/new-image.jpg',
                'webp_path' => 'projects/new-image.webp',
                'variants' => [],
                'file_info' => ['size' => 1024, 'mime' => 'image/jpeg'],
                'processing_time_ms' => 150.5
            ]);

        $file = UploadedFile::fake()->image('new-image.jpg', 1200, 800);

        $updateData = [
            'title' => $project->title,
            'description' => $project->description,
            'client_id' => $this->client->id,
            'client_name' => $this->client->first_name . ' ' . $this->client->last_name,
            'status' => 'completed',
            'featured_image' => $file,
            'content' => $project->content ?? 'Test content',
            'service_id' => $project->service_id,
            'project_url' => $project->project_url,
            'start_date' => $project->start_date,
            'end_date' => $project->end_date,
            'estimated_hours' => $project->estimated_hours,
            'actual_hours' => $project->actual_hours,
            'hourly_rate' => $project->hourly_rate,
            'total_amount' => $project->total_amount,
            'currency_code' => $project->currency_code ?? 'USD',
            'priority' => $project->priority ?? 'medium',
            'is_featured' => $project->is_featured ?? false,
            'is_published' => $project->is_published ?? true,
            'meta_title' => $project->meta_title,
            'meta_description' => $project->meta_description,
            'meta_keywords' => $project->meta_keywords,
        ];

        $response = $this->actingAs($this->admin)
            ->put(route('admin.projects.update', $project), $updateData);

        $response->assertRedirect(route('admin.projects.show', $project));
        
        $this->assertDatabaseHas('projects', [
            'id' => $project->id,
            'featured_image' => 'projects/new-image.jpg',
        ]);
    }

    /** @test */
    public function project_update_handles_legacy_full_path_images()
    {
        Storage::fake('public');
        
        // Create project with legacy full system path
        $legacyPath = storage_path('app/public/images/projects/legacy-image.jpg');
        $project = Project::factory()->create([
            'client_id' => $this->client->id,
            'featured_image' => $legacyPath
        ]);

        // Mock ImageService - should receive normalized path
        $imageService = $this->mock(ImageService::class);
        $imageService->shouldReceive('deleteImage')
            ->once()
            ->with('images/projects/legacy-image.jpg', true)
            ->andReturn(true);
        $imageService->shouldReceive('processUploadedImage')
            ->once()
            ->andReturn([
                'success' => true,
                'original_path' => 'projects/new-image.jpg',
                'webp_path' => 'projects/new-image.webp',
                'variants' => [],
                'file_info' => ['size' => 1024, 'mime' => 'image/jpeg'],
                'processing_time_ms' => 150.5
            ]);

        $file = UploadedFile::fake()->image('new-image.jpg', 1200, 800);

        $updateData = [
            'title' => $project->title,
            'description' => $project->description,
            'client_id' => $this->client->id,
            'client_name' => $this->client->first_name . ' ' . $this->client->last_name,
            'status' => 'completed',
            'featured_image' => $file,
        ];

        $response = $this->actingAs($this->admin)
            ->put(route('admin.projects.update', $project), $updateData);

        $response->assertRedirect(route('admin.projects.show', $project));
        
        $this->assertDatabaseHas('projects', [
            'id' => $project->id,
            'featured_image' => 'projects/new-image.jpg',
        ]);
    }

    /** @test */
    public function project_update_continues_when_old_image_deletion_fails()
    {
        Storage::fake('public');
        
        $project = Project::factory()->create([
            'client_id' => $this->client->id,
            'featured_image' => 'projects/non-existent-image.jpg'
        ]);

        // Mock ImageService - deleteImage throws exception
        $imageService = $this->mock(ImageService::class);
        $imageService->shouldReceive('deleteImage')
            ->once()
            ->with('projects/non-existent-image.jpg', true)
            ->andThrow(new \Exception('File not found'));
        $imageService->shouldReceive('processUploadedImage')
            ->once()
            ->andReturn([
                'success' => true,
                'original_path' => 'projects/new-image.jpg',
                'webp_path' => 'projects/new-image.webp',
                'variants' => [],
                'file_info' => ['size' => 1024, 'mime' => 'image/jpeg'],
                'processing_time_ms' => 150.5
            ]);

        $file = UploadedFile::fake()->image('new-image.jpg', 1200, 800);

        $updateData = [
            'title' => $project->title,
            'description' => $project->description,
            'client_id' => $this->client->id,
            'client_name' => $this->client->first_name . ' ' . $this->client->last_name,
            'status' => 'completed',
            'featured_image' => $file,
        ];

        $response = $this->actingAs($this->admin)
            ->put(route('admin.projects.update', $project), $updateData);

        // Should still succeed despite deletion failure
        $response->assertRedirect(route('admin.projects.show', $project));
        
        $this->assertDatabaseHas('projects', [
            'id' => $project->id,
            'featured_image' => 'projects/new-image.jpg',
        ]);
    }

    /** @test */
    public function project_model_handles_legacy_image_paths_in_url_accessor()
    {
        // Mock ImageService
        $imageService = $this->mock(ImageService::class);
        $imageService->shouldReceive('getImageUrl')
            ->with('images/projects/legacy-image.jpg')
            ->andReturn('http://localhost:8000/storage/images/projects/legacy-image.jpg');

        // Create project with legacy full system path
        $legacyPath = storage_path('app/public/images/projects/legacy-image.jpg');
        $project = Project::factory()->create([
            'featured_image' => $legacyPath
        ]);

        // Access the URL - should normalize the path and update the database
        $imageUrl = $project->featured_image_url;

        $this->assertEquals('http://localhost:8000/storage/images/projects/legacy-image.jpg', $imageUrl);
        
        // Check that the database was updated with normalized path
        $this->assertDatabaseHas('projects', [
            'id' => $project->id,
            'featured_image' => 'images/projects/legacy-image.jpg',
        ]);
    }

    /** @test */
    public function project_model_returns_placeholder_for_null_image()
    {
        $project = Project::factory()->create([
            'featured_image' => null
        ]);

        $imageUrl = $project->featured_image_url;

        $this->assertStringContainsString('images/projects/placeholder.jpg', $imageUrl);
    }

    /** @test */
    public function project_update_without_image_upload_works()
    {
        $project = Project::factory()->create([
            'client_id' => $this->client->id,
            'title' => 'Original Title'
        ]);

        $updateData = [
            'title' => 'Updated Title',
            'description' => 'Updated description',
            'client_id' => $this->client->id,
            'client_name' => $this->client->first_name . ' ' . $this->client->last_name,
            'status' => 'completed',
            // No featured_image field
        ];

        $response = $this->actingAs($this->admin)
            ->put(route('admin.projects.update', $project), $updateData);

        $response->assertRedirect(route('admin.projects.show', $project));
        
        $this->assertDatabaseHas('projects', [
            'id' => $project->id,
            'title' => 'Updated Title',
        ]);
    }
}
