<?php

namespace Tests\Unit\Models;

use App\Models\Project;
use App\Services\ImageService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ProjectImageUrlTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function featured_image_url_uses_image_service()
    {
        // Mock ImageService
        $imageService = $this->mock(ImageService::class);
        $imageService->shouldReceive('getImageUrl')
            ->with('projects/test-image.jpg')
            ->andReturn('http://localhost:8000/storage/projects/test-image.jpg');

        $project = Project::factory()->create([
            'featured_image' => 'projects/test-image.jpg'
        ]);

        $this->assertEquals('http://localhost:8000/storage/projects/test-image.jpg', $project->featured_image_url);
    }

    /** @test */
    public function featured_image_url_returns_placeholder_when_no_image()
    {
        $project = Project::factory()->create([
            'featured_image' => null
        ]);

        $this->assertStringContainsString('images/projects/placeholder.jpg', $project->featured_image_url);
    }

    /** @test */
    public function primary_image_is_alias_for_featured_image_url()
    {
        // Mock ImageService
        $imageService = $this->mock(ImageService::class);
        $imageService->shouldReceive('getImageUrl')
            ->with('projects/test-image.jpg')
            ->andReturn('http://localhost:8000/storage/projects/test-image.jpg');

        $project = Project::factory()->create([
            'featured_image' => 'projects/test-image.jpg'
        ]);

        $this->assertEquals($project->featured_image_url, $project->primary_image);
    }

    /** @test */
    public function all_images_includes_featured_and_gallery_with_urls()
    {
        // Mock ImageService
        $imageService = $this->mock(ImageService::class);
        $imageService->shouldReceive('getImageUrl')
            ->with('projects/featured.jpg')
            ->andReturn('http://localhost:8000/storage/projects/featured.jpg');
        $imageService->shouldReceive('getImageUrl')
            ->with('projects/gallery1.jpg')
            ->andReturn('http://localhost:8000/storage/projects/gallery1.jpg');
        $imageService->shouldReceive('getImageUrl')
            ->with('projects/gallery2.jpg')
            ->andReturn('http://localhost:8000/storage/projects/gallery2.jpg');

        $project = Project::factory()->create([
            'featured_image' => 'projects/featured.jpg',
            'gallery' => ['projects/gallery1.jpg', 'projects/gallery2.jpg']
        ]);

        $allImages = $project->all_images;

        $this->assertCount(3, $allImages);
        $this->assertContains('http://localhost:8000/storage/projects/featured.jpg', $allImages);
        $this->assertContains('http://localhost:8000/storage/projects/gallery1.jpg', $allImages);
        $this->assertContains('http://localhost:8000/storage/projects/gallery2.jpg', $allImages);
    }

    /** @test */
    public function all_images_handles_null_gallery()
    {
        // Mock ImageService
        $imageService = $this->mock(ImageService::class);
        $imageService->shouldReceive('getImageUrl')
            ->with('projects/featured.jpg')
            ->andReturn('http://localhost:8000/storage/projects/featured.jpg');

        $project = Project::factory()->create([
            'featured_image' => 'projects/featured.jpg',
            'gallery' => null
        ]);

        $allImages = $project->all_images;

        $this->assertCount(1, $allImages);
        $this->assertContains('http://localhost:8000/storage/projects/featured.jpg', $allImages);
    }

    /** @test */
    public function all_images_handles_empty_gallery()
    {
        // Mock ImageService
        $imageService = $this->mock(ImageService::class);
        $imageService->shouldReceive('getImageUrl')
            ->with('projects/featured.jpg')
            ->andReturn('http://localhost:8000/storage/projects/featured.jpg');

        $project = Project::factory()->create([
            'featured_image' => 'projects/featured.jpg',
            'gallery' => []
        ]);

        $allImages = $project->all_images;

        $this->assertCount(1, $allImages);
        $this->assertContains('http://localhost:8000/storage/projects/featured.jpg', $allImages);
    }

    /** @test */
    public function all_images_returns_empty_array_when_no_images()
    {
        $project = Project::factory()->create([
            'featured_image' => null,
            'gallery' => null
        ]);

        $allImages = $project->all_images;

        $this->assertCount(1, $allImages); // Still contains placeholder
        $this->assertStringContainsString('images/projects/placeholder.jpg', $allImages[0]);
    }

    /** @test */
    public function all_images_removes_duplicates()
    {
        // Mock ImageService
        $imageService = $this->mock(ImageService::class);
        $imageService->shouldReceive('getImageUrl')
            ->with('projects/same-image.jpg')
            ->andReturn('http://localhost:8000/storage/projects/same-image.jpg');

        $project = Project::factory()->create([
            'featured_image' => 'projects/same-image.jpg',
            'gallery' => ['projects/same-image.jpg', 'projects/same-image.jpg'] // Duplicates
        ]);

        $allImages = $project->all_images;

        $this->assertCount(1, $allImages); // Should remove duplicates
        $this->assertContains('http://localhost:8000/storage/projects/same-image.jpg', $allImages);
    }
}
