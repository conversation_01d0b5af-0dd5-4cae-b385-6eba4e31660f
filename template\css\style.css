/*--------------------------------------------------------------------- File Name: style.css ---------------------------------------------------------------------*/


/*--------------------------------------------------------------------- import Fonts ---------------------------------------------------------------------*/

@import url('https://fonts.googleapis.com/css?family=Rajdhani:300,400,500,600,700');
@import url('https://fonts.googleapis.com/css?family=Poppins:100,100i,200,200i,300,300i,400,400i,500,500i,600,600i,700,700i,800,800i,900,900i');
@font-face {
    font-family: "Righteous";
    src: url("../fonts/Poppins-Black.html");
    src: url("../fonts/Roboto.html");
}


/*****---------------------------------------- 1) font-family: 'Rajdhani', sans-serif;
 2) font-family: 'Poppins', sans-serif;
 ----------------------------------------*****/


/*--------------------------------------------------------------------- import Files ---------------------------------------------------------------------*/

@import url(animate.min.css);
@import url(normalize.css);
@import url(icomoon.css);
@import url(css/font-awesome.min.html);
@import url(meanmenu.css);
@import url(owl.carousel.min.css);
@import url(swiper.min.html);
@import url(slick.css);
@import url(jquery.fancybox.min.css);
@import url(jquery-ui.css);
@import url(nice-select.css);

/*--------------------------------------------------------------------- skeleton ---------------------------------------------------------------------*/

* {
    box-sizing: border-box !important;
    transition: ease all 0.5s;
}

html {
    scroll-behavior: smooth;
}

body {
    color: #666666;
    font-size: 14px;
    font-family: Poppins;
    line-height: 1.80857;
    font-weight: normal;
    overflow-x: hidden;
}

a {
    color: #1f1f1f;
    text-decoration: none !important;
    outline: none !important;
    -webkit-transition: all .3s ease-in-out;
    -moz-transition: all .3s ease-in-out;
    -ms-transition: all .3s ease-in-out;
    -o-transition: all .3s ease-in-out;
    transition: all .3s ease-in-out;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    letter-spacing: 0;
    font-weight: normal;
    position: relative;
    padding: 0 0 10px 0;
    font-weight: normal;
    line-height: normal;
    color: #111111;
    margin: 0
}

h1 {
    font-size: 24px
}

h2 {
    font-size: 22px
}

h3 {
    font-size: 18px
}

h4 {
    font-size: 16px
}

h5 {
    font-size: 14px
}

h6 {
    font-size: 13px
}

*,
*::after,
*::before {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

h1 a,
h2 a,
h3 a,
h4 a,
h5 a,
h6 a {
    color: #212121;
    text-decoration: none!important;
    opacity: 1
}

button:focus {
    outline: none;
}

ul,
li,
ol {
    margin: 0px;
    padding: 0px;
    list-style: none;
}

p {
    margin: 20px;
    font-weight: 300;
    font-size: 15px;
    line-height: 24px;
}

a {
    color: #222222;
    text-decoration: none;
    outline: none !important;
}

a,
.btn {
    text-decoration: none !important;
    outline: none !important;
    -webkit-transition: all .3s ease-in-out;
    -moz-transition: all .3s ease-in-out;
    -ms-transition: all .3s ease-in-out;
    -o-transition: all .3s ease-in-out;
    transition: all .3s ease-in-out;
}

img {
    max-width: 100%;
    height: auto;
}

 :focus {
    outline: 0;
}

.layout_padding {
    padding-top: 90px;
    padding-bottom: 0px;
}

.padding_0 {
    padding: 0px;
}

.header_bg {
    width: 100%;
    float: left;
    background: #2d4fbc !important;
    padding-bottom: 20px !important;
}

.header_section {
    width: 100%;
    float: left;
    background-image: url(../images/banner-bg.png);
    height: auto;
    background-size: cover;
    padding-bottom: 90px;
}

.bg-dark {
    background-color: transparent !important;
}

.navbar-dark .navbar-toggler-icon {
    background-image: url(../images/toggle-icon.png);
}

.navbar-toggler-icon {
    width: 55px;
}

.navbar-dark .navbar-toggler {
    border-color: transparent !important;
}

.navbar-toggler {
    padding: .25rem 0px;
}

.navbar {
    padding: 30px 0px 10px 0px;
}

.navbar-dark .navbar-nav .nav-link {
    color: #000;
    font-size: 18px;
    padding-bottom: 0px;
}

.navbar-dark .navbar-nav .nav-link:focus,
.navbar-dark .navbar-nav .nav-link:hover {
    color: #000;
}

.logo {
    width: 70%;
    float: left;
}

.banner_taital {
    width: 100%;
    float: left;
    font-size: 90px;
    color: #060303;
    font-weight: bold;
    line-height: 100px;
    padding-top: 60px;
}

.banner_text {
    width: 90%;
    float: left;
    font-size: 16px;
    color: #383838;
    margin-left: 0px;
}

.read_bt {
    width: 170px;
    float: left;
    margin-top: 10px;
}

.read_bt a {
    width: 100%;
    float: left;
    font-size: 16px;
    color: #ffffff;
    text-align: center;
    background-color: #070414;
    border-bottom: 7px solid #393bb9;
    border-radius: 30px;
    padding: 9px 0px;
}

.read_bt a:hover {
    background-color: #8480f9;
    border-bottom: 7px solid #393bb9;
}

#my_slider .carousel-control-prev:hover,
#my_slider .carousel-control-next:hover,
#my_slider .carousel-control-prev:focus,
#my_slider .carousel-control-next:focus {
    background: #055eff;
    color: #fff;
}

#my_slider a.carousel-control-prev {
    position: absolute;
    left: 850px;
    top: 100%;
}

#my_slider a.carousel-control-next {
    position: absolute;
    right: 350px;
    top: 100%;
    background-color: #055eff;
}

#my_slider .carousel-control-prev,
#my_slider .carousel-control-next {
    width: 65px;
    height: 65px;
    background: #000006;
    opacity: 1;
    font-size: 30px;
    color: #ffffff;
}

.about_section {
    width: 100%;
    float: left;
    position: relative;
    padding-bottom: 90px;
}

.image_2 {
    width: 90%;
    float: left;
}

.about_section::after {
    content: '';
    position: absolute;
    height: 466px;
    width: 655px;
    margin: 0 auto;
    justify-content: center;
    left: 0px;
    right: initial;
    display: block;
    background-image: url(../images/about-bg.png);
    top: 37px;
    z-index: -1;
    background-size: 100%;
}

.about_taital {
    width: 100%;
    float: left;
    font-size: 40px;
    color: #383838;
}

.about_text {
    width: 100%;
    float: left;
    font-size: 16px;
    color: #0c0900;
    margin-left: 0px;
}

.us_text {
    background-color: #ececeb;
    border-radius: 100%;
    color: #5114b2;
    padding: 5px 10px;
}

.read_bt_1 {
    width: 170px;
    float: left;
    margin-top: 40px;
}

.read_bt_1 a {
    width: 100%;
    float: left;
    font-size: 16px;
    color: #ffffff;
    text-align: center;
    background-color: #393bb9;
    border-radius: 30px;
    padding: 9px 0px;
}

.read_bt_1 a:hover {
    background-color: #8480f9;
}

.services_section {
    width: 100%;
    float: left;
    background-image: url(../images/services-bg.png);
    background-size: cover;
    height: auto;
    padding-bottom: 90px;
    margin-top: 90px;
    background-repeat: no-repeat;
}

.service_taital {
    width: 100%;
    float: left;
    font-size: 40px;
    color: #ffffff;
    text-align: center;
}

.service_text {
    width: 100%;
    float: left;
    font-size: 16px;
    color: #dcdada;
    margin-left: 0px;
    text-align: center;
}

.our_text {
    background-color: #ececeb;
    border-radius: 100%;
    color: #5114b2;
    padding: 17px 10px;
}

.services_section_2 {
    width: 100%;
    float: left;
}

.icon_1 {
    width: 100%;
    float: left;
    text-align: center;
    padding-top: 90px;
}

.design_text {
    width: 100%;
    float: left;
    text-align: center;
    font-size: 24px;
    color: #ffffff;
    padding-top: 10px;
}

.lorem_text {
    width: 80%;
    text-align: center;
    color: #d5d5d5;
    font-size: 16px;
    margin: 0 auto;
}

.icon_3 {
    width: 100%;
    float: left;
    text-align: center;
    padding-top: 220px;
}

.read_bt_2 {
    width: 170px;
    margin: 0 auto;
    text-align: center;
}

.read_bt_2 a {
    width: 100%;
    float: left;
    font-size: 16px;
    color: #000;
    text-align: center;
    background-color: #ffffff;
    border-radius: 30px;
    padding: 9px 0px;
    margin-top: 70px;
}

.read_bt_2 a:hover {
    background-color: #8480f9;
}

.blog_section {
    width: 100%;
    float: left;
    padding-bottom: 90px;
}

.blog_taital {
    width: 100%;
    float: left;
    font-size: 40px;
    color: #181717;
    text-align: center;
    margin-top: 15px;
}

.blog_text {
    width: 100%;
    float: left;
    font-size: 16px;
    color: #2d2d2d;
    margin-left: 0px;
    text-align: center;
    font-weight: 400;
}

.box_main {
    width: 100%;
    float: left;
    background-color: #ffffff;
    height: auto;
    border-radius: 5px;
    padding-bottom: 20px;
}

.box_main:hover {
    box-shadow: 0px 1px 10px 1px #888888;
}

.student_bg {
    width: 100%;
    float: left;
    background-image: url(../images/img-3.png);
    height: auto;
    background-size: cover;
    position: relative;
    min-height: 287px;
    background-repeat: no-repeat;
}

.image_15 {
    width: 20%;
    margin: 0 auto;
    text-align: center;
    position: absolute;
    top: -40px;
    background: #334bbb;
    display: block;
    border-radius: 100%;
    color: #ffffff;
    font-size: 20px;
    line-height: 24px;
    padding: 13px 20px;
    left: 0;
    right: 0;
}

.hannery_text {
    width: 100%;
    float: left;
    font-size: 20px;
    color: #1d1f1e;
    font-weight: bold;
    padding-left: 20px;
    padding-top: 15px;
}

.fact_text {
    width: 100%;
    float: left;
    font-size: 15px;
    color: #282828;
    margin: 0px;
    padding: 0px 11px;
    padding-left: 20px;
}

.newsletter_section {
    width: 100%;
    float: left;
    background-color: #2e4dbc;
    height: auto;
    padding: 40px 0px;
}

.newsletter_text {
    width: 100%;
    float: left;
    color: #ffffff;
    font-size: 40px;
}

.tempor_text {
    width: 100%;
    float: left;
    color: #ffffff;
    font-size: 16px;
    margin-left: 0px;
    margin-top: 0px;
}

.mail_bt_main {
    width: 100%;
    float: left;
    padding-top: 50px;
}

.mail_text {
    width: 60%;
    float: left;
    color: #000;
    font-size: 16px;
    background-color: #ffffff;
    padding: 10px 15px;
    border: 0px;
    height: auto;
    border-radius: 50px;
}

.subscribe_bt {
    width: 150px;
    float: left;
    color: #acabab;
    font-size: 16px;
    background-color: #040202;
    padding: 10px 15px;
    text-align: center;
    border-radius: 50px;
    margin-left: 30px;
}

.subscribe_bt a {
    color: #acabab;
}

.client_section {
    width: 100%;
    float: left;
    background-color: #f5f5f5;
    height: auto;
    padding-bottom: 140px;
}

.tes_text {
    background-color: #ececeb;
    border-radius: 100%;
    color: #5114b2;
    padding: 15px 10px;
}

.client_section_2 {
    width: 100%;
    float: left;
}

.client_box {
    width: 100%;
    float: left;
    background-color: #402fb7;
    padding: 30px 30px 10px 30px;
    border-radius: 20px;
}

.client_box.active {
    background-color: #ffffff;
}

.client_box:hover {
    background-color: #ffffff;
}

.client_box:hover .magna_text {
    color: #000;
}

.client_box:hover .consectetur_text {
    color: #000;
}

.client_box:hover .ipsum_text {
    color: #000;
}

.left_main {
    width: 30%;
    float: left;
}

.right_main {
    width: 70%;
    float: left;
}

.magna_text {
    width: 100%;
    float: left;
    font-size: 24px;
    color: #ffffff;
    font-weight: bold;
}

.magna_text.active {
    color: #000;
}

.consectetur_text {
    width: 100%;
    float: left;
    font-size: 20px;
    color: #ffffff;
    margin: 0px;
}

.consectetur_text.active {
    color: #000;
}

.quote_icon {
    width: 30px;
    float: left;
    padding-top: 10px;
    background-image: url(../images/quote-icon1.png);
    height: 100px;
    background-size: 100%;
    background-repeat: no-repeat;
    margin-top: 15px;
}

.quote_icon.active {
    background-image: url(../images/quote-icon.png);
}

.client_box .quote_icon:hover {
    background-image: url(../images/quote-icon.png);
}

.ipsum_text {
    width: 100%;
    float: left;
    font-size: 16px;
    color: #ffffff;
    margin-left: 0px;
}

.ipsum_text.active {
    color: #000;
}

.carousel-indicators li {
    width: 50px;
    height: 15px;
    border-radius: 40px;
    background-color: #0c0900;
}

.carousel-indicators .active {
    background-color: #402fb7;
}

.carousel-indicators {
    bottom: -80px;
}

.contact_section {
    width: 100%;
    float: left;
    padding-bottom: 90px;
}

.touch_taital {
    width: 100%;
    float: left;
    font-size: 40px;
    color: #1d1c1c;
    font-weight: bold;
    text-align: center;
}

.contact_section_2 {
    width: 100%;
    float: left;
    margin-top: 30px;
}

.email_text {
    width: 100%;
    float: left;
    padding: 20px 20px 20px 20px;
}

.email-bt {
    width: 100%;
    float: left;
    font-size: 18px;
    color: #2a2a2c;
    margin-bottom: 20px;
    padding: 8px 15px;
    border: 1px solid #909090;
}

.massage-bt {
    width: 100%;
    float: left;
    font-size: 18px;
    color: #2a2a2c;
    margin-bottom: 20px;
    padding: 40px 15px 8px 15px;
    border: 1px solid #909090;
    height: 120px;
}

.send_btn {
    width: 170px;
    float: left;
}

.send_btn a {
    width: 100%;
    float: left;
    text-align: center;
    font-size: 20px;
    color: #ffffff;
    background-color: #4c35bc;
    padding: 10px;
    border-radius: 30px;
}

.send_btn a:hover {
    color: #ffffff;
    background-color: #000;
}

.footer_section {
    width: 100%;
    float: left;
    background-color: #070517;
    height: auto;
    padding-bottom: 40px;
}

.address_main {
    width: 100%;
    margin: 0 auto;
    text-align: center;
    background-image: url(../images/footer-bg.png);
    height: auto;
    display: flex;
    padding: 15px 0px;
    background-repeat: no-repeat;
    background-size: 100%;
}

.address_text {
    width: 100%;
    font-size: 16px;
    color: #ffffff;
}

.address_text a {
    color: #ffffff;
}

.padding_left_15 {
    padding-left: 15px;
}

.footer_section_2 {
    width: 100%;
    float: left;
    padding-top: 40px;
}

.link_text {
    width: 100%;
    float: left;
    font-size: 20px;
    color: #fcfcfc;
    font-weight: bold;
    text-transform: uppercase;
}

.footer_menu {
    width: 100%;
    float: left;
}

.footer_menu ul {
    margin: 0px;
    padding: 0px;
}

.footer_menu li {
    font-size: 16px;
    color: #878585;
}

.footer_menu li a {
    color: #878585;
}

.footer_menu li a:hover {
    color: #fcfcfc;
}

.footer_text {
    width: 55%;
    float: left;
    font-size: 16px;
    color: #878585;
    margin-left: 0px;
    margin-top: 0px;
}

.footer_text_1 {
    width: 100%;
    float: left;
    font-size: 16px;
    color: #878585;
    margin-left: 0px;
    margin-top: 0px;
}

.social_icon {
    width: 100%;
    float: left;
}

.social_icon ul {
    margin: 0px;
    padding: 0px;
}

.social_icon li {
    float: left;
    padding-right: 10px;
    padding-top: 10px;
}

.copyright_section {
    width: 100%;
    float: left;
    background-color: #ffffff;
    height: auto;
}

.copyright_text {
    width: 100%;
    float: left;
    color: #0b0b0c;
    text-align: center;
    font-size: 16px;
    margin-left: 0px;
}

.copyright_text a {
    color: #0b0b0c;
}

.copyright_text a:hover {
    color: #402fb7;
}

.margin_top_90 {
    margin-top: 90px;
}