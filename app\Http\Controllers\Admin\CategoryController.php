<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ProductCategory;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class CategoryController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:admin,staff');
    }

    /**
     * Display a listing of categories.
     */
    public function index(): View
    {
        $categories = ProductCategory::with(['parent', 'children'])
                                   ->where('is_deleted', false)
                                   ->orderBy('sort_order')
                                   ->orderBy('name')
                                   ->get();

        // Build tree structure
        $categoryTree = collect($this->buildCategoryTree($categories));

        return view('admin.categories.index', compact('categoryTree', 'categories'));
    }

    /**
     * Show the form for creating a new category.
     */
    public function create(): View
    {
        $parentCategories = ProductCategory::where('is_deleted', false)
                                         ->whereNull('parent_id')
                                         ->orderBy('name')
                                         ->get();

        return view('admin.categories.create', compact('parentCategories'));
    }

    /**
     * Store a newly created category.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:200',
            'slug' => 'nullable|string|max:255|unique:product_categories,slug',
            'description' => 'nullable|string',
            'parent_id' => 'nullable|exists:product_categories,id',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string',
            'meta_keywords' => 'nullable|string',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
        ]);

        // Generate slug if not provided
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['name']);
        }

        // Handle image upload
        if ($request->hasFile('image')) {
            $validated['image'] = $request->file('image')->store('categories', 'public');
        }

        $category = ProductCategory::create($validated);

        return redirect()->route('admin.categories.index')
                        ->with('success', 'Category created successfully.');
    }

    /**
     * Display the specified category.
     */
    public function show(ProductCategory $category): View
    {
        $category->load(['parent', 'children', 'products']);
        
        return view('admin.categories.show', compact('category'));
    }

    /**
     * Show the form for editing the specified category.
     */
    public function edit(ProductCategory $category): View
    {
        $parentCategories = ProductCategory::where('is_deleted', false)
                                         ->where('id', '!=', $category->id)
                                         ->whereNull('parent_id')
                                         ->orderBy('name')
                                         ->get();

        return view('admin.categories.edit', compact('category', 'parentCategories'));
    }

    /**
     * Update the specified category.
     */
    public function update(Request $request, ProductCategory $category): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:200',
            'slug' => [
                'nullable',
                'string',
                'max:255',
                Rule::unique('product_categories', 'slug')->ignore($category->id)
            ],
            'description' => 'nullable|string',
            'parent_id' => [
                'nullable',
                'exists:product_categories,id',
                function ($attribute, $value, $fail) use ($category) {
                    if ($value == $category->id) {
                        $fail('A category cannot be its own parent.');
                    }
                }
            ],
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string',
            'meta_keywords' => 'nullable|string',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
        ]);

        // Generate slug if not provided
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['name']);
        }

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($category->image) {
                \Storage::disk('public')->delete($category->image);
            }
            $validated['image'] = $request->file('image')->store('categories', 'public');
        }

        $category->update($validated);

        return redirect()->route('admin.categories.index')
                        ->with('success', 'Category updated successfully.');
    }

    /**
     * Remove the specified category.
     */
    public function destroy(ProductCategory $category): RedirectResponse
    {
        // Check if category has children
        if ($category->children()->exists()) {
            return redirect()->route('admin.categories.index')
                           ->with('error', 'Cannot delete category with subcategories.');
        }

        // Check if category has products
        if ($category->products()->exists()) {
            return redirect()->route('admin.categories.index')
                           ->with('error', 'Cannot delete category with products.');
        }

        // Soft delete
        $category->update(['is_deleted' => true]);

        return redirect()->route('admin.categories.index')
                        ->with('success', 'Category deleted successfully.');
    }

    /**
     * Build hierarchical category tree.
     */
    private function buildCategoryTree($categories, $parentId = null): array
    {
        $tree = [];

        foreach ($categories as $category) {
            if ($category->parent_id == $parentId) {
                $children = $this->buildCategoryTree($categories, $category->id);
                if ($children) {
                    $category->children_tree = $children;
                }
                $tree[] = $category;
            }
        }

        return $tree;
    }

    /**
     * Get categories as JSON for AJAX requests.
     */
    public function getCategories(Request $request)
    {
        $categories = ProductCategory::where('is_deleted', false)
                                   ->where('is_active', true)
                                   ->orderBy('name')
                                   ->get(['id', 'name', 'parent_id']);

        return response()->json($categories);
    }

    /**
     * Reorder categories.
     */
    public function reorder(Request $request): RedirectResponse
    {
        $request->validate([
            'categories' => 'required|array',
            'categories.*.id' => 'required|exists:product_categories,id',
            'categories.*.sort_order' => 'required|integer|min:0',
        ]);

        foreach ($request->categories as $categoryData) {
            ProductCategory::where('id', $categoryData['id'])
                          ->update(['sort_order' => $categoryData['sort_order']]);
        }

        return redirect()->route('admin.categories.index')
                        ->with('success', 'Categories reordered successfully.');
    }

    /**
     * Toggle category featured status via AJAX.
     */
    public function toggleFeatured(ProductCategory $category)
    {
        $category->update(['is_featured' => !$category->is_featured]);

        return response()->json([
            'success' => true,
            'is_featured' => $category->is_featured,
            'message' => $category->is_featured ? 'Category featured successfully.' : 'Category unfeatured successfully.'
        ]);
    }
}
