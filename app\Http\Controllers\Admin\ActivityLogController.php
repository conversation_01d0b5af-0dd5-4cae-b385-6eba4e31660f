<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ActivityLog;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Carbon\Carbon;

class ActivityLogController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        // Additional security check - ensure user is admin or staff
        $this->middleware(function ($request, $next) {
            $user = $request->user();

            if (!$user || !$user->isAdminOrStaff()) {
                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'error' => [
                            'code' => 'INSUFFICIENT_PERMISSIONS',
                            'message' => 'Access denied. Admin or staff role required.',
                            'timestamp' => now()->toISOString()
                        ]
                    ], 403);
                }

                abort(403, 'Access denied. Admin or staff role required.');
            }

            return $next($request);
        });
    }

    /**
     * Display a listing of activity logs.
     */
    public function index(Request $request): View
    {
        $query = ActivityLog::with('user')
            ->orderBy('occurred_at', 'desc');

        // Filter by activity type
        if ($request->filled('activity_type')) {
            $query->where('activity_type', $request->activity_type);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by email
        if ($request->filled('email')) {
            $query->where('user_email', 'like', '%' . $request->email . '%');
        }

        // Filter by IP address
        if ($request->filled('ip_address')) {
            $query->where('ip_address', $request->ip_address);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('occurred_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('occurred_at', '<=', $request->date_to);
        }

        // Filter suspicious activities
        if ($request->boolean('suspicious_only')) {
            $query->where('is_suspicious', true);
        }

        // Filter enumeration attempts
        if ($request->boolean('enumeration_only')) {
            $query->where('request_data->enumeration_attempt', true);
        }

        $logs = $query->paginate(50);

        $activityTypes = ActivityLog::distinct('activity_type')
            ->pluck('activity_type')
            ->sort();

        $statuses = ActivityLog::distinct('status')
            ->pluck('status')
            ->sort();

        return view('admin.activity-logs.index', compact('logs', 'activityTypes', 'statuses'));
    }

    /**
     * Display the specified activity log.
     */
    public function show(ActivityLog $activityLog): View
    {
        return view('admin.activity-logs.show', compact('activityLog'));
    }

    /**
     * Get activity logs data for API/AJAX requests.
     */
    public function data(Request $request): JsonResponse
    {
        $query = ActivityLog::with('user')
            ->orderBy('occurred_at', 'desc');

        // Apply filters
        if ($request->filled('activity_type')) {
            $query->where('activity_type', $request->activity_type);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('email')) {
            $query->where('user_email', 'like', '%' . $request->email . '%');
        }

        if ($request->filled('ip_address')) {
            $query->where('ip_address', $request->ip_address);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('occurred_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('occurred_at', '<=', $request->date_to);
        }

        if ($request->boolean('suspicious_only')) {
            $query->where('is_suspicious', true);
        }

        $logs = $query->paginate(50);

        return response()->json([
            'data' => $logs->items(),
            'pagination' => [
                'current_page' => $logs->currentPage(),
                'last_page' => $logs->lastPage(),
                'per_page' => $logs->perPage(),
                'total' => $logs->total(),
            ]
        ]);
    }

    /**
     * Get activity log statistics.
     */
    public function stats(): JsonResponse
    {
        $stats = [
            'total_logs' => ActivityLog::count(),
            'password_reset_requests' => ActivityLog::where('activity_type', 'password_reset_request')->count(),
            'successful_resets' => ActivityLog::where('activity_type', 'password_reset_success')->count(),
            'failed_resets' => ActivityLog::where('activity_type', 'password_reset_failed')->count(),
            'suspicious_activities' => ActivityLog::where('is_suspicious', true)->count(),
            'enumeration_attempts' => ActivityLog::where('request_data->enumeration_attempt', true)->count(),
            'today_logs' => ActivityLog::whereDate('occurred_at', today())->count(),
            'this_week_logs' => ActivityLog::whereBetween('occurred_at', [
                now()->startOfWeek(),
                now()->endOfWeek()
            ])->count(),
            'high_risk_activities' => ActivityLog::where('risk_score', '>=', 80)->count(),
            'today_enumeration' => ActivityLog::where('request_data->enumeration_attempt', true)
                ->whereDate('occurred_at', today())->count(),
        ];

        // Top IP addresses
        $topIps = ActivityLog::selectRaw('ip_address, COUNT(*) as count')
            ->groupBy('ip_address')
            ->orderBy('count', 'desc')
            ->limit(10)
            ->get();

        // Recent suspicious activities
        $recentSuspicious = ActivityLog::where('is_suspicious', true)
            ->orderBy('occurred_at', 'desc')
            ->limit(10)
            ->get();

        // Activity by type
        $activityByType = ActivityLog::selectRaw('activity_type, COUNT(*) as count')
            ->groupBy('activity_type')
            ->orderBy('count', 'desc')
            ->get();

        return response()->json([
            'stats' => $stats,
            'top_ips' => $topIps,
            'recent_suspicious' => $recentSuspicious,
            'activity_by_type' => $activityByType,
        ]);
    }

    /**
     * Remove the specified activity log.
     */
    public function destroy(ActivityLog $activityLog)
    {
        $activityLog->delete();

        return redirect()->route('admin.activity-logs.index')
                        ->with('success', 'Activity log deleted successfully.');
    }

    /**
     * Bulk delete activity logs.
     */
    public function bulkDelete(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:activity_logs,id'
        ]);

        $deletedCount = ActivityLog::whereIn('id', $validated['ids'])->delete();

        return response()->json([
            'success' => true,
            'message' => "Successfully deleted {$deletedCount} activity log(s).",
            'deleted_count' => $deletedCount
        ]);
    }

    /**
     * Clean old activity logs.
     */
    public function cleanOldLogs(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'days' => 'required|integer|min:1|max:365'
        ]);

        $cutoffDate = now()->subDays($validated['days']);
        $deletedCount = ActivityLog::where('occurred_at', '<', $cutoffDate)->delete();

        return response()->json([
            'success' => true,
            'message' => "Successfully deleted {$deletedCount} activity log(s) older than {$validated['days']} days.",
            'deleted_count' => $deletedCount
        ]);
    }

    /**
     * Export activity logs.
     */
    public function export(Request $request)
    {
        $format = $request->get('format', 'csv');

        $query = ActivityLog::with('user')->orderBy('occurred_at', 'desc');

        // Apply same filters as index
        if ($request->filled('activity_type')) {
            $query->where('activity_type', $request->activity_type);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('email')) {
            $query->where('user_email', 'like', '%' . $request->email . '%');
        }

        if ($request->filled('date_from')) {
            $query->whereDate('occurred_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('occurred_at', '<=', $request->date_to);
        }

        $logs = $query->limit(10000)->get(); // Limit for performance

        if ($format === 'csv') {
            return $this->exportToCsv($logs);
        } elseif ($format === 'json') {
            return $this->exportToJson($logs);
        }

        return redirect()->back()->with('error', 'Invalid export format.');
    }

    /**
     * Export logs to CSV.
     */
    private function exportToCsv($logs)
    {
        $filename = 'activity_logs_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($logs) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'ID', 'User Email', 'Activity Type', 'Description', 'Status',
                'Risk Score', 'IP Address', 'Location', 'Device', 'Occurred At'
            ]);

            foreach ($logs as $log) {
                fputcsv($file, [
                    $log->id,
                    $log->user_email ?? 'System',
                    $log->activity_type,
                    $log->description,
                    $log->status,
                    $log->risk_score,
                    $log->ip_address,
                    $log->location_info ? json_encode($log->location_info) : '',
                    $log->device_info ? json_encode($log->device_info) : '',
                    $log->occurred_at->format('Y-m-d H:i:s'),
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export logs to JSON.
     */
    private function exportToJson($logs): JsonResponse
    {
        return response()->json([
            'exported_at' => now()->toISOString(),
            'total_records' => $logs->count(),
            'data' => $logs->toArray()
        ]);
    }
}
