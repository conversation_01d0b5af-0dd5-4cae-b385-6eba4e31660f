@extends('layouts.app')

@section('title', 'Project Application Details')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Project Application Details</h4>
                    <div class="d-flex gap-2">
                        @if($projectApplication->status === 'pending')
                            <a href="{{ route('project-applications.edit', $projectApplication) }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                        @endif
                        <a href="{{ route('project-applications.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h5>{{ $projectApplication->title }}</h5>
                            <p class="text-muted mb-3">
                                <small>
                                    <i class="fas fa-calendar"></i> Submitted: {{ $projectApplication->created_at->format('M d, Y \a\t g:i A') }}
                                    @if($projectApplication->reviewed_at)
                                        | <i class="fas fa-check-circle"></i> Reviewed: {{ $projectApplication->reviewed_at->format('M d, Y \a\t g:i A') }}
                                    @endif
                                </small>
                            </p>

                            <div class="mb-4">
                                <h6>Description</h6>
                                <p>{{ $projectApplication->description }}</p>
                            </div>

                            @if($projectApplication->requirements)
                                <div class="mb-4">
                                    <h6>Requirements</h6>
                                    <p>{{ $projectApplication->requirements }}</p>
                                </div>
                            @endif

                            @if($projectApplication->attachments && count($projectApplication->attachments) > 0)
                                <div class="mb-4">
                                    <h6>Attachments</h6>
                                    <div class="list-group">
                                        @foreach($projectApplication->attachments as $attachment)
                                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                                <div>
                                                    <i class="fas fa-file"></i>
                                                    <strong>{{ $attachment['original_name'] }}</strong>
                                                    <small class="text-muted">({{ number_format($attachment['size'] / 1024, 1) }} KB)</small>
                                                </div>
                                                <a href="{{ Storage::url($attachment['stored_name']) }}" 
                                                   class="btn btn-sm btn-outline-primary" 
                                                   target="_blank">
                                                    <i class="fas fa-download"></i> Download
                                                </a>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endif

                            @if($projectApplication->admin_notes)
                                <div class="mb-4">
                                    <h6>Admin Notes</h6>
                                    <div class="alert alert-info">
                                        {{ $projectApplication->admin_notes }}
                                    </div>
                                </div>
                            @endif
                        </div>

                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Application Details</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <strong>Status:</strong>
                                        <span class="badge badge-{{ $projectApplication->status_color }}">
                                            {{ ucfirst($projectApplication->status) }}
                                        </span>
                                    </div>

                                    @if($projectApplication->budget_range)
                                        <div class="mb-3">
                                            <strong>Budget Range:</strong><br>
                                            ${{ $projectApplication->budget_range }}
                                        </div>
                                    @endif

                                    @if($projectApplication->timeline)
                                        <div class="mb-3">
                                            <strong>Timeline:</strong><br>
                                            {{ $projectApplication->timeline }}
                                        </div>
                                    @endif

                                    <div class="mb-3">
                                        <strong>Priority:</strong><br>
                                        <span class="badge badge-{{ $projectApplication->priority === 'urgent' ? 'danger' : ($projectApplication->priority === 'high' ? 'warning' : ($projectApplication->priority === 'medium' ? 'info' : 'secondary')) }}">
                                            {{ ucfirst($projectApplication->priority) }}
                                        </span>
                                    </div>

                                    @if($projectApplication->service)
                                        <div class="mb-3">
                                            <strong>Service:</strong><br>
                                            {{ $projectApplication->service->name }}
                                        </div>
                                    @endif

                                    @if($projectApplication->project)
                                        <div class="mb-3">
                                            <strong>Related Project:</strong><br>
                                            {{ $projectApplication->project->name }}
                                        </div>
                                    @endif

                                    @if($projectApplication->reviewedBy)
                                        <div class="mb-3">
                                            <strong>Reviewed By:</strong><br>
                                            {{ $projectApplication->reviewedBy->name }}
                                        </div>
                                    @endif
                                </div>
                            </div>

                            @if($projectApplication->status === 'pending')
                                <div class="mt-3">
                                    <form action="{{ route('project-applications.destroy', $projectApplication) }}" 
                                          method="POST" 
                                          onsubmit="return confirm('Are you sure you want to delete this application?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-danger btn-sm w-100">
                                            <i class="fas fa-trash"></i> Delete Application
                                        </button>
                                    </form>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
