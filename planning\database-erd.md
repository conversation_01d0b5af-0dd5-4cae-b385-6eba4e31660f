# 🗄️ ChiSolution Database Architecture
## Professional Database Design for Digital Agency & E-commerce Platform

### 📊 Database Overview

The ChiSolution database is architected with the following core modules:

1. **🔐 Authentication & User Management**
2. **🛍️ E-commerce & Product Catalog**
3. **📦 Order Management & Fulfillment**
4. **🎯 Project Management & Client Services**
5. **📝 Content Management System**
6. **🌍 Internationalization & Localization**
7. **📊 Analytics & Activity Tracking**
8. **⚙️ System Configuration**

---

## 🏗️ Core Database Modules

### 🔐 Module 1: Authentication & User Management

#### users
**Purpose:** Central user management with role-based access control
```sql
users
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── uuid (VARCHAR(36) UNIQUE NOT NULL) -- Public identifier
├── first_name (VARCHAR(100) NOT NULL)
├── last_name (VARCHAR(100) NOT NULL)
├── email (VARCHAR(255) UNIQUE NOT NULL)
├── email_verified_at (TIMES<PERSON>MP NULL)
├── password (VARCHAR(255) NOT NULL)
├── phone (VARCHAR(20) NULL)
├── avatar (VARCHAR(500) NULL) -- File path or URL
├── role_id (BIGINT UNSIGNED NOT NULL FK→roles.id)
├── is_active (BOOLEAN DEFAULT TRUE)
├── is_deleted (BOOLEAN DEFAULT FALSE) -- Soft delete
├── last_login_at (TIMESTAMP NULL)
├── login_count (INT UNSIGNED DEFAULT 0)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_users_email_active (email, is_active, is_deleted)
INDEX idx_users_role_active (role_id, is_active)
INDEX idx_users_uuid (uuid)
INDEX idx_users_login (last_login_at)
```

#### roles
**Purpose:** Role-based permission system
```sql
roles
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── name (VARCHAR(50) UNIQUE NOT NULL) -- admin, staff, client, customer
├── slug (VARCHAR(50) UNIQUE NOT NULL)
├── display_name (VARCHAR(100) NOT NULL)
├── description (TEXT NULL)
├── permissions (JSON NOT NULL) -- Structured permissions
├── is_active (BOOLEAN DEFAULT TRUE)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_roles_name_active (name, is_active)
INDEX idx_roles_slug (slug)
```

#### user_addresses
**Purpose:** Multi-address management for billing/shipping
```sql
user_addresses
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── user_id (BIGINT UNSIGNED NOT NULL FK→users.id CASCADE)
├── type (ENUM('billing', 'shipping', 'both') DEFAULT 'both')
├── label (VARCHAR(50) NULL) -- "Home", "Office", etc.
├── first_name (VARCHAR(100) NOT NULL)
├── last_name (VARCHAR(100) NOT NULL)
├── company (VARCHAR(200) NULL)
├── address_line_1 (VARCHAR(255) NOT NULL)
├── address_line_2 (VARCHAR(255) NULL)
├── city (VARCHAR(100) NOT NULL)
├── state_province (VARCHAR(100) NOT NULL)
├── postal_code (VARCHAR(20) NOT NULL)
├── country_code (CHAR(2) NOT NULL) -- ISO 3166-1 alpha-2
├── phone (VARCHAR(20) NULL)
├── is_default (BOOLEAN DEFAULT FALSE)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_user_addresses_user_type (user_id, type)
INDEX idx_user_addresses_default (user_id, is_default)
INDEX idx_user_addresses_country (country_code)
```

---

### 🌍 Module 2: Internationalization & Localization

#### languages
**Purpose:** Multi-language support system
```sql
languages
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── code (VARCHAR(5) UNIQUE NOT NULL) -- en, fr, es, en-US
├── name (VARCHAR(100) NOT NULL) -- English, French, Spanish
├── native_name (VARCHAR(100) NOT NULL) -- English, Français, Español
├── direction (ENUM('ltr', 'rtl') DEFAULT 'ltr')
├── is_active (BOOLEAN DEFAULT TRUE)
├── is_default (BOOLEAN DEFAULT FALSE)
├── sort_order (INT UNSIGNED DEFAULT 0)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_languages_code_active (code, is_active)
INDEX idx_languages_default (is_default)
INDEX idx_languages_sort (sort_order)
```

#### currencies
**Purpose:** Multi-currency support with exchange rates
```sql
currencies
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── code (CHAR(3) UNIQUE NOT NULL) -- ZAR, USD, EUR, GBP, RWF
├── name (VARCHAR(100) NOT NULL) -- South African Rand
├── symbol (VARCHAR(10) NOT NULL) -- R, $, €, £
├── symbol_position (ENUM('before', 'after') DEFAULT 'before')
├── decimal_places (TINYINT UNSIGNED DEFAULT 2)
├── exchange_rate (DECIMAL(15,6) DEFAULT 1.000000) -- Base currency rate
├── is_default (BOOLEAN DEFAULT FALSE)
├── is_active (BOOLEAN DEFAULT TRUE)
├── last_updated (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_currencies_code_active (code, is_active)
INDEX idx_currencies_default (is_default)
INDEX idx_currencies_rate_updated (last_updated)
```

#### translations
**Purpose:** Polymorphic translation system for all content
```sql
translations
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── translatable_type (VARCHAR(100) NOT NULL) -- Model class name
├── translatable_id (BIGINT UNSIGNED NOT NULL) -- Model ID
├── language_id (BIGINT UNSIGNED NOT NULL FK→languages.id CASCADE)
├── field_name (VARCHAR(100) NOT NULL) -- Field being translated
├── field_value (LONGTEXT NOT NULL) -- Translated content
├── is_approved (BOOLEAN DEFAULT TRUE)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_translations_polymorphic (translatable_type, translatable_id)
INDEX idx_translations_language_field (language_id, field_name)
UNIQUE KEY uk_translations_unique (translatable_type, translatable_id, language_id, field_name)
```

---

### 🛍️ Module 3: E-commerce & Product Catalog

#### product_categories
**Purpose:** Hierarchical product categorization system
```sql
product_categories
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── uuid (VARCHAR(36) UNIQUE NOT NULL)
├── name (VARCHAR(200) NOT NULL)
├── slug (VARCHAR(255) UNIQUE NOT NULL)
├── description (TEXT NULL)
├── image (VARCHAR(500) NULL)
├── parent_id (BIGINT UNSIGNED NULL FK→product_categories.id CASCADE)
├── level (TINYINT UNSIGNED DEFAULT 0) -- Denormalized depth
├── path (VARCHAR(1000) NULL) -- Materialized path: /1/5/12/
├── meta_title (VARCHAR(255) NULL)
├── meta_description (TEXT NULL)
├── meta_keywords (TEXT NULL)
├── is_active (BOOLEAN DEFAULT TRUE)
├── is_deleted (BOOLEAN DEFAULT FALSE)
├── sort_order (INT UNSIGNED DEFAULT 0)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_categories_slug_active (slug, is_active, is_deleted)
INDEX idx_categories_parent_sort (parent_id, sort_order)
INDEX idx_categories_path (path)
INDEX idx_categories_level (level)
INDEX idx_categories_uuid (uuid)
```

#### products
**Purpose:** Core product catalog with comprehensive attributes
```sql
products
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── uuid (VARCHAR(36) UNIQUE NOT NULL)
├── name (VARCHAR(300) NOT NULL)
├── slug (VARCHAR(400) UNIQUE NOT NULL)
├── short_description (TEXT NULL)
├── description (LONGTEXT NULL)
├── sku (VARCHAR(100) UNIQUE NOT NULL)
├── barcode (VARCHAR(100) NULL)
├── brand (VARCHAR(100) NULL)
├── model (VARCHAR(100) NULL)
├── price (DECIMAL(12,2) NOT NULL)
├── compare_price (DECIMAL(12,2) NULL) -- Original/MSRP price
├── cost_price (DECIMAL(12,2) NULL) -- Internal cost
├── profit_margin (DECIMAL(5,2) NULL) -- Calculated margin %
├── track_inventory (BOOLEAN DEFAULT TRUE)
├── inventory_quantity (INT DEFAULT 0)
├── low_stock_threshold (INT DEFAULT 5)
├── weight (DECIMAL(8,3) NULL) -- In grams
├── dimensions (JSON NULL) -- {length, width, height, unit}
├── featured_image (VARCHAR(500) NULL)
├── gallery (JSON NULL) -- Array of image URLs
├── attributes (JSON NULL) -- Custom product attributes
├── is_active (BOOLEAN DEFAULT TRUE)
├── is_featured (BOOLEAN DEFAULT FALSE)
├── is_deleted (BOOLEAN DEFAULT FALSE)
├── meta_title (VARCHAR(255) NULL)
├── meta_description (TEXT NULL)
├── meta_keywords (TEXT NULL)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_products_slug_active (slug, is_active, is_deleted)
INDEX idx_products_sku_active (sku, is_active)
INDEX idx_products_featured_active (is_featured, is_active)
INDEX idx_products_price_range (price, is_active)
INDEX idx_products_inventory (inventory_quantity, track_inventory)
INDEX idx_products_uuid (uuid)
FULLTEXT idx_products_search (name, description, sku, brand, model)
```

#### product_category_relations
**Purpose:** Many-to-many relationship between products and categories
```sql
product_category_relations
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── product_id (BIGINT UNSIGNED NOT NULL FK→products.id CASCADE)
├── category_id (BIGINT UNSIGNED NOT NULL FK→product_categories.id CASCADE)
├── is_primary (BOOLEAN DEFAULT FALSE) -- Primary category for product
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
UNIQUE KEY uk_product_category (product_id, category_id)
INDEX idx_product_category_product (product_id)
INDEX idx_product_category_category (category_id)
INDEX idx_product_category_primary (product_id, is_primary)
```

#### product_variants
**Purpose:** Product variations (size, color, etc.)
```sql
product_variants
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── product_id (BIGINT UNSIGNED NOT NULL FK→products.id CASCADE)
├── name (VARCHAR(200) NOT NULL)
├── sku (VARCHAR(100) UNIQUE NOT NULL)
├── barcode (VARCHAR(100) NULL)
├── price (DECIMAL(12,2) NULL) -- Override product price
├── compare_price (DECIMAL(12,2) NULL)
├── cost_price (DECIMAL(12,2) NULL)
├── inventory_quantity (INT DEFAULT 0)
├── weight (DECIMAL(8,3) NULL)
├── dimensions (JSON NULL)
├── image (VARCHAR(500) NULL) -- Variant-specific image
├── attributes (JSON NOT NULL) -- {color: "red", size: "XL"}
├── is_active (BOOLEAN DEFAULT TRUE)
├── is_deleted (BOOLEAN DEFAULT FALSE)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_variants_product_active (product_id, is_active, is_deleted)
INDEX idx_variants_sku (sku)
INDEX idx_variants_inventory (inventory_quantity)
```

---

### 📦 Module 4: Shopping Cart & Order Management

#### shopping_carts
**Purpose:** Persistent shopping cart for users and sessions
```sql
shopping_carts
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── uuid (VARCHAR(36) UNIQUE NOT NULL)
├── user_id (BIGINT UNSIGNED NULL FK→users.id CASCADE)
├── session_id (VARCHAR(255) NULL) -- For guest users
├── currency_code (CHAR(3) NOT NULL DEFAULT 'ZAR')
├── subtotal (DECIMAL(12,2) DEFAULT 0.00)
├── tax_amount (DECIMAL(12,2) DEFAULT 0.00)
├── shipping_amount (DECIMAL(12,2) DEFAULT 0.00)
├── discount_amount (DECIMAL(12,2) DEFAULT 0.00)
├── total (DECIMAL(12,2) DEFAULT 0.00)
├── expires_at (TIMESTAMP NULL) -- Cart expiration
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_carts_user (user_id)
INDEX idx_carts_session (session_id)
INDEX idx_carts_expires (expires_at)
INDEX idx_carts_uuid (uuid)
```
#### cart_items
**Purpose:** Items in shopping carts
```sql
cart_items
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── shopping_cart_id (BIGINT UNSIGNED NOT NULL FK→shopping_carts.id CASCADE)
├── product_id (BIGINT UNSIGNED NOT NULL FK→products.id CASCADE)
├── variant_id (BIGINT UNSIGNED NULL FK→product_variants.id CASCADE)
├── quantity (INT UNSIGNED NOT NULL DEFAULT 1)
├── unit_price (DECIMAL(12,2) NOT NULL) -- Price at time of adding
├── total_price (DECIMAL(12,2) NOT NULL) -- quantity * unit_price
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_cart_items_cart (shopping_cart_id)
INDEX idx_cart_items_product (product_id)
INDEX idx_cart_items_variant (variant_id)
UNIQUE KEY uk_cart_item_unique (shopping_cart_id, product_id, variant_id)
```

#### orders
**Purpose:** Customer orders with comprehensive tracking
```sql
orders
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── uuid (VARCHAR(36) UNIQUE NOT NULL)
├── order_number (VARCHAR(50) UNIQUE NOT NULL) -- Human-readable order number
├── user_id (BIGINT UNSIGNED NULL FK→users.id SET NULL)
├── customer_email (VARCHAR(255) NOT NULL)
├── customer_first_name (VARCHAR(100) NOT NULL)
├── customer_last_name (VARCHAR(100) NOT NULL)
├── customer_phone (VARCHAR(20) NULL)
├── customer_company (VARCHAR(200) NULL)
├── status (ENUM('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded') DEFAULT 'pending')
├── payment_status (ENUM('pending', 'paid', 'failed', 'refunded', 'partially_refunded') DEFAULT 'pending')
├── payment_method (VARCHAR(50) NULL) -- stripe, paypal, bank_transfer, etc.
├── payment_reference (VARCHAR(255) NULL) -- External payment ID
├── currency_code (CHAR(3) NOT NULL DEFAULT 'ZAR')
├── exchange_rate (DECIMAL(15,6) DEFAULT 1.000000) -- Rate at time of order
├── subtotal (DECIMAL(12,2) NOT NULL)
├── tax_amount (DECIMAL(12,2) DEFAULT 0.00)
├── shipping_amount (DECIMAL(12,2) DEFAULT 0.00)
├── discount_amount (DECIMAL(12,2) DEFAULT 0.00)
├── total (DECIMAL(12,2) NOT NULL)
├── billing_address (JSON NOT NULL) -- Complete billing address
├── shipping_address (JSON NOT NULL) -- Complete shipping address
├── notes (TEXT NULL) -- Customer notes
├── admin_notes (TEXT NULL) -- Internal notes
├── shipped_at (TIMESTAMP NULL)
├── delivered_at (TIMESTAMP NULL)
├── paid_at (TIMESTAMP NULL)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_orders_number (order_number)
INDEX idx_orders_user_status (user_id, status)
INDEX idx_orders_email_status (customer_email, status)
INDEX idx_orders_payment_status (payment_status)
INDEX idx_orders_dates (created_at, status)
INDEX idx_orders_uuid (uuid)
```

#### order_items
**Purpose:** Line items for orders with product snapshots
```sql
order_items
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── order_id (BIGINT UNSIGNED NOT NULL FK→orders.id CASCADE)
├── product_id (BIGINT UNSIGNED NOT NULL FK→products.id RESTRICT)
├── variant_id (BIGINT UNSIGNED NULL FK→product_variants.id RESTRICT)
├── quantity (INT UNSIGNED NOT NULL)
├── unit_price (DECIMAL(12,2) NOT NULL) -- Price at time of order
├── total_price (DECIMAL(12,2) NOT NULL) -- quantity * unit_price
├── product_snapshot (JSON NOT NULL) -- Product details at time of order
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_order_items_order (order_id)
INDEX idx_order_items_product (product_id)
INDEX idx_order_items_variant (variant_id)
```

---

### 🎯 Module 5: Project Management & Client Services

#### services
**Purpose:** Digital agency service offerings
```sql
services
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── uuid (VARCHAR(36) UNIQUE NOT NULL)
├── name (VARCHAR(200) NOT NULL)
├── slug (VARCHAR(255) UNIQUE NOT NULL)
├── short_description (TEXT NULL)
├── description (LONGTEXT NULL)
├── icon (VARCHAR(500) NULL) -- Icon file path or class
├── featured_image (VARCHAR(500) NULL)
├── gallery (JSON NULL) -- Array of image URLs
├── price_from (DECIMAL(12,2) NULL) -- Starting price
├── features (JSON NULL) -- Service features list
├── is_featured (BOOLEAN DEFAULT FALSE)
├── is_active (BOOLEAN DEFAULT TRUE)
├── is_deleted (BOOLEAN DEFAULT FALSE)
├── sort_order (INT UNSIGNED DEFAULT 0)
├── meta_title (VARCHAR(255) NULL)
├── meta_description (TEXT NULL)
├── meta_keywords (TEXT NULL)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_services_slug_active (slug, is_active, is_deleted)
INDEX idx_services_featured_active (is_featured, is_active)
INDEX idx_services_sort (sort_order)
INDEX idx_services_uuid (uuid)
```

#### projects
**Purpose:** Client project portfolio and management
```sql
projects
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── uuid (VARCHAR(36) UNIQUE NOT NULL)
├── title (VARCHAR(300) NOT NULL)
├── slug (VARCHAR(400) UNIQUE NOT NULL)
├── description (TEXT NULL)
├── content (LONGTEXT NULL) -- Full project description
├── client_name (VARCHAR(200) NULL) -- Display name
├── client_id (BIGINT UNSIGNED NULL FK→users.id SET NULL)
├── service_id (BIGINT UNSIGNED NULL FK→services.id SET NULL)
├── featured_image (VARCHAR(500) NULL)
├── gallery (JSON NULL) -- Project images
├── project_url (VARCHAR(500) NULL) -- Live project URL
├── start_date (DATE NULL)
├── end_date (DATE NULL)
├── estimated_hours (DECIMAL(8,2) NULL)
├── actual_hours (DECIMAL(8,2) NULL)
├── hourly_rate (DECIMAL(8,2) NULL)
├── total_amount (DECIMAL(12,2) NULL)
├── currency_code (CHAR(3) DEFAULT 'ZAR')
├── status (ENUM('planning', 'in_progress', 'review', 'completed', 'on_hold', 'cancelled') DEFAULT 'planning')
├── priority (ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium')
├── is_featured (BOOLEAN DEFAULT FALSE)
├── is_published (BOOLEAN DEFAULT FALSE) -- Show in portfolio
├── is_deleted (BOOLEAN DEFAULT FALSE)
├── meta_title (VARCHAR(255) NULL)
├── meta_description (TEXT NULL)
├── meta_keywords (TEXT NULL)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_projects_slug_published (slug, is_published, is_deleted)
INDEX idx_projects_client_status (client_id, status)
INDEX idx_projects_service (service_id)
INDEX idx_projects_status_priority (status, priority)
INDEX idx_projects_featured_published (is_featured, is_published)
INDEX idx_projects_uuid (uuid)
```

---

### 📝 Module 6: Content Management System

#### pages
**Purpose:** Static page content management
```sql
pages
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── uuid (VARCHAR(36) UNIQUE NOT NULL)
├── title (VARCHAR(300) NOT NULL)
├── slug (VARCHAR(400) UNIQUE NOT NULL)
├── content (LONGTEXT NULL)
├── excerpt (TEXT NULL)
├── featured_image (VARCHAR(500) NULL)
├── template (VARCHAR(100) NULL) -- Custom template name
├── is_published (BOOLEAN DEFAULT FALSE)
├── is_deleted (BOOLEAN DEFAULT FALSE)
├── published_at (TIMESTAMP NULL)
├── meta_title (VARCHAR(255) NULL)
├── meta_description (TEXT NULL)
├── meta_keywords (TEXT NULL)
├── created_by (BIGINT UNSIGNED NULL FK→users.id SET NULL)
├── updated_by (BIGINT UNSIGNED NULL FK→users.id SET NULL)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_pages_slug_published (slug, is_published, is_deleted)
INDEX idx_pages_published_date (is_published, published_at)
INDEX idx_pages_created_by (created_by)
INDEX idx_pages_uuid (uuid)
```

#### blog_categories
**Purpose:** Blog post categorization
```sql
blog_categories
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── name (VARCHAR(200) NOT NULL)
├── slug (VARCHAR(255) UNIQUE NOT NULL)
├── description (TEXT NULL)
├── image (VARCHAR(500) NULL)
├── parent_id (BIGINT UNSIGNED NULL FK→blog_categories.id CASCADE)
├── sort_order (INT UNSIGNED DEFAULT 0)
├── is_active (BOOLEAN DEFAULT TRUE)
├── is_deleted (BOOLEAN DEFAULT FALSE)
├── meta_title (VARCHAR(255) NULL)
├── meta_description (TEXT NULL)
├── meta_keywords (TEXT NULL)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_blog_categories_slug_active (slug, is_active, is_deleted)
INDEX idx_blog_categories_parent_sort (parent_id, sort_order)
```

#### blog_posts
**Purpose:** Blog content management
```sql
blog_posts
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── uuid (VARCHAR(36) UNIQUE NOT NULL)
├── title (VARCHAR(300) NOT NULL)
├── slug (VARCHAR(400) UNIQUE NOT NULL)
├── excerpt (TEXT NULL)
├── content (LONGTEXT NOT NULL)
├── featured_image (VARCHAR(500) NULL)
├── category_id (BIGINT UNSIGNED NULL FK→blog_categories.id SET NULL)
├── author_id (BIGINT UNSIGNED NOT NULL FK→users.id RESTRICT)
├── is_published (BOOLEAN DEFAULT FALSE)
├── is_featured (BOOLEAN DEFAULT FALSE)
├── is_deleted (BOOLEAN DEFAULT FALSE)
├── published_at (TIMESTAMP NULL)
├── meta_title (VARCHAR(255) NULL)
├── meta_description (TEXT NULL)
├── meta_keywords (TEXT NULL)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_blog_posts_slug_published (slug, is_published, is_deleted)
INDEX idx_blog_posts_category_published (category_id, is_published)
INDEX idx_blog_posts_author (author_id)
INDEX idx_blog_posts_featured_published (is_featured, is_published)
INDEX idx_blog_posts_published_date (is_published, published_at)
INDEX idx_blog_posts_uuid (uuid)
```

---

### 📊 Module 7: Analytics & Activity Tracking

#### activity_logs
**Purpose:** Comprehensive system activity tracking
```sql
activity_logs
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── uuid (VARCHAR(36) UNIQUE NOT NULL)
├── user_id (BIGINT UNSIGNED NULL FK→users.id SET NULL)
├── user_email (VARCHAR(255) NULL) -- Denormalized for deleted users
├── user_name (VARCHAR(255) NULL) -- Denormalized for deleted users
├── activity_type (VARCHAR(100) NOT NULL) -- login, logout, create_order, etc.
├── activity_description (TEXT NOT NULL)
├── status (ENUM('success', 'failed', 'pending') DEFAULT 'success')
├── failure_reason (TEXT NULL)
├── ip_address (VARCHAR(45) NULL) -- IPv4/IPv6
├── user_agent (TEXT NULL)
├── device_type (VARCHAR(50) NULL) -- mobile, desktop, tablet
├── browser (VARCHAR(100) NULL)
├── platform (VARCHAR(100) NULL) -- Windows, macOS, Linux, etc.
├── country (VARCHAR(100) NULL)
├── region (VARCHAR(100) NULL)
├── city (VARCHAR(100) NULL)
├── latitude (DECIMAL(10,8) NULL)
├── longitude (DECIMAL(11,8) NULL)
├── url (VARCHAR(1000) NULL)
├── method (VARCHAR(10) NULL) -- GET, POST, etc.
├── request_data (JSON NULL)
├── response_data (JSON NULL)
├── session_id (VARCHAR(255) NULL)
├── request_id (VARCHAR(100) NULL) -- Unique request identifier
├── is_suspicious (BOOLEAN DEFAULT FALSE)
├── security_notes (TEXT NULL)
├── risk_score (TINYINT UNSIGNED DEFAULT 0) -- 0-100
├── occurred_at (TIMESTAMP NOT NULL)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_activity_logs_user_type_date (user_id, activity_type, occurred_at)
INDEX idx_activity_logs_email_type_date (user_email, activity_type, occurred_at)
INDEX idx_activity_logs_ip_date (ip_address, occurred_at)
INDEX idx_activity_logs_type_status_date (activity_type, status, occurred_at)
INDEX idx_activity_logs_suspicious_date (is_suspicious, occurred_at)
INDEX idx_activity_logs_occurred (occurred_at)
INDEX idx_activity_logs_uuid (uuid)
```

---

### ⚙️ Module 8: System Configuration

#### settings
**Purpose:** Application configuration management
```sql
settings
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── key (VARCHAR(255) UNIQUE NOT NULL)
├── value (LONGTEXT NULL)
├── type (ENUM('string', 'integer', 'boolean', 'json', 'array') DEFAULT 'string')
├── group (VARCHAR(100) NULL) -- general, email, payment, etc.
├── label (VARCHAR(255) NULL) -- Human-readable label
├── description (TEXT NULL)
├── is_public (BOOLEAN DEFAULT FALSE) -- Can be accessed by frontend
├── is_encrypted (BOOLEAN DEFAULT FALSE) -- Sensitive data
├── validation_rules (JSON NULL) -- Validation constraints
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_settings_key (key)
INDEX idx_settings_group_public (group, is_public)
```

#### contact_submissions
**Purpose:** Contact form submissions management
```sql
contact_submissions
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── name (VARCHAR(200) NOT NULL)
├── email (VARCHAR(255) NOT NULL)
├── phone (VARCHAR(20) NULL)
├── company (VARCHAR(200) NULL)
├── subject (VARCHAR(300) NOT NULL)
├── message (LONGTEXT NOT NULL)
├── ip_address (VARCHAR(45) NULL)
├── user_agent (TEXT NULL)
├── referrer (VARCHAR(1000) NULL)
├── is_read (BOOLEAN DEFAULT FALSE)
├── is_spam (BOOLEAN DEFAULT FALSE)
├── replied_at (TIMESTAMP NULL)
├── replied_by (BIGINT UNSIGNED NULL FK→users.id SET NULL)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_contact_submissions_email_date (email, created_at)
INDEX idx_contact_submissions_read_spam (is_read, is_spam)
INDEX idx_contact_submissions_replied (replied_by, replied_at)
```

---

## 🔗 Entity Relationships & Business Rules

### Core Relationships
1. **users (1) ←→ (M) user_addresses** - Users can have multiple addresses
2. **users (M) ←→ (1) roles** - Each user has one role
3. **products (M) ←→ (M) product_categories** - Many-to-many through pivot table
4. **products (1) ←→ (M) product_variants** - Products can have multiple variants
5. **users (1) ←→ (M) shopping_carts** - Users can have multiple carts (expired/active)
6. **shopping_carts (1) ←→ (M) cart_items** - Cart contains multiple items
7. **users (1) ←→ (M) orders** - Users can place multiple orders
8. **orders (1) ←→ (M) order_items** - Orders contain multiple line items
9. **users (1) ←→ (M) projects** - Clients can have multiple projects
10. **services (1) ←→ (M) projects** - Services can be used in multiple projects
11. **languages (1) ←→ (M) translations** - Polymorphic translation system
12. **users (1) ←→ (M) activity_logs** - Comprehensive activity tracking

### Business Rules
- **Soft Deletes**: All tables have `is_deleted` boolean flag for soft delete functionality
  - Only admin users can perform hard deletes
  - All other users (staff, client, customer) can only soft delete
  - Queries automatically filter out soft-deleted records unless explicitly included
- **UUID Public IDs**: All public-facing entities have UUID for security
- **Audit Trail**: All critical operations logged in activity_logs
- **Multi-Currency**: All monetary values support multiple currencies
- **Multi-Language**: All content can be translated
- **Inventory Tracking**: Products can optionally track inventory
- **Guest Checkout**: Orders can be placed without user accounts
- **Role-Based Access**: Permissions controlled through roles table

### Soft Delete Implementation
All tables include `is_deleted` column with the following specifications:
- **Column Type**: `BOOLEAN DEFAULT FALSE`
- **Indexing**: Composite indexes include `is_deleted` for query optimization
- **Model Scopes**: Global scopes filter out soft-deleted records by default
- **Admin Override**: Admin users can view and restore soft-deleted records
- **Cascade Behavior**: Soft deleting parent records soft deletes related children

### Performance Optimizations
- **Composite Indexes**: Optimized for common query patterns
- **Denormalized Data**: Strategic denormalization for performance
- **Full-Text Search**: Product and content search capabilities
- **Materialized Paths**: Category hierarchy optimization
- **JSON Columns**: Flexible attribute storage
- **Timestamp Indexing**: Optimized for date-range queries
