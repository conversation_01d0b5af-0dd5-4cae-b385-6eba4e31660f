@extends('layouts.app')

@section('title', __('Privacy Policy') . ' - ' . __('common.company_name'))

@section('content')
<div class="container mx-auto px-4 py-16">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">Privacy Policy</h1>
        
        <div class="prose prose-lg max-w-none">
            <p class="text-gray-600 mb-6">Last updated: {{ date('F j, Y') }}</p>
            
            <h2>1. Information We Collect</h2>
            <p>We collect information you provide directly to us, such as when you create an account, make a purchase, or contact us for support.</p>
            
            <h2>2. How We Use Your Information</h2>
            <p>We use the information we collect to provide, maintain, and improve our services, process transactions, and communicate with you.</p>
            
            <h2>3. Information Sharing</h2>
            <p>We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.</p>
            
            <h2>4. Data Security</h2>
            <p>We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.</p>
            
            <h2>5. Cookies</h2>
            <p>We use cookies to enhance your experience on our website. You can choose to disable cookies through your browser settings.</p>
            
            <h2>6. Your Rights</h2>
            <p>You have the right to access, update, or delete your personal information. You may also opt out of certain communications from us.</p>
            
            <h2>7. Changes to This Policy</h2>
            <p>We may update this privacy policy from time to time. We will notify you of any changes by posting the new policy on this page.</p>
            
            <h2>8. Contact Us</h2>
            <p>If you have any questions about this Privacy Policy, please contact us.</p>
        </div>
        
        <div class="mt-12 pt-8 border-t border-gray-200">
            <a href="{{ route('register') }}" class="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Registration
            </a>
        </div>
    </div>
</div>
@endsection
