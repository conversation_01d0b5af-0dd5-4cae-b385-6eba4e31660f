@extends('emails.layout')

@section('title', 'New Order Received - ' . $order->order_number)

@section('content')
<div class="greeting">
    New Order Received!
</div>

<p class="content-text">
    A new order has been placed on your website. Here are the details:
</p>

<!-- Order Summary Box -->
<div style="background-color: #ebf8ff; border: 1px solid #3182ce; border-radius: 8px; padding: 20px; margin: 20px 0;">
    <h3 style="margin: 0 0 15px 0; color: #2c5282; font-size: 18px;">Order Summary</h3>
    
    <table style="width: 100%; border-collapse: collapse;">
        <tr>
            <td style="padding: 8px 0; color: #4a5568; font-weight: 600;">Order Number:</td>
            <td style="padding: 8px 0; color: #2d3748; text-align: right; font-family: monospace;">#{{ $order->order_number }}</td>
        </tr>
        <tr>
            <td style="padding: 8px 0; color: #4a5568; font-weight: 600;">Customer:</td>
            <td style="padding: 8px 0; color: #2d3748; text-align: right;">{{ $customer_name }}</td>
        </tr>
        <tr>
            <td style="padding: 8px 0; color: #4a5568; font-weight: 600;">Email:</td>
            <td style="padding: 8px 0; color: #2d3748; text-align: right;">{{ $order->email }}</td>
        </tr>
        <tr>
            <td style="padding: 8px 0; color: #4a5568; font-weight: 600;">Order Date:</td>
            <td style="padding: 8px 0; color: #2d3748; text-align: right;">{{ $order->created_at->format('M d, Y \a\t g:i A') }}</td>
        </tr>
        <tr>
            <td style="padding: 8px 0; color: #4a5568; font-weight: 600;">Payment Status:</td>
            <td style="padding: 8px 0; text-align: right;">
                <span style="background-color: {{ $order->payment_status === 'paid' ? '#48bb78' : '#ed8936' }}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; text-transform: uppercase;">
                    {{ ucfirst($order->payment_status) }}
                </span>
            </td>
        </tr>
        <tr style="border-top: 1px solid #3182ce;">
            <td style="padding: 12px 0 8px 0; color: #2c5282; font-weight: 700; font-size: 16px;">Total Amount:</td>
            <td style="padding: 12px 0 8px 0; color: #2c5282; font-weight: 700; font-size: 16px; text-align: right;">{{ $order->formatted_total }}</td>
        </tr>
    </table>
</div>

<!-- Order Items -->
<h3 style="color: #2d3748; margin: 30px 0 15px 0;">Items Ordered ({{ $order->items->count() }} items)</h3>

@foreach($order->items as $item)
<div style="border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; margin-bottom: 10px; background-color: #ffffff;">
    <table style="width: 100%;">
        <tr>
            <td style="width: 70%;">
                <h4 style="margin: 0 0 5px 0; color: #2d3748; font-size: 16px;">{{ $item->product_name }}</h4>
                @if($item->variant_name)
                <p style="margin: 0 0 5px 0; color: #718096; font-size: 14px;">{{ $item->variant_name }}</p>
                @endif
                <p style="margin: 0; color: #4a5568; font-size: 14px;">
                    <strong>SKU:</strong> {{ $item->product_sku ?? 'N/A' }} | 
                    <strong>Qty:</strong> {{ $item->quantity }}
                </p>
            </td>
            <td style="width: 30%; text-align: right; vertical-align: top;">
                <p style="margin: 0; color: #2d3748; font-weight: 600; font-size: 16px;">{{ $item->formatted_total_price }}</p>
                <p style="margin: 5px 0 0 0; color: #718096; font-size: 14px;">{{ $item->formatted_unit_price }} each</p>
            </td>
        </tr>
    </table>
</div>
@endforeach

<!-- Customer Information -->
<h3 style="color: #2d3748; margin: 30px 0 15px 0;">Customer Information</h3>

<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
    <!-- Billing Address -->
    <div style="background-color: #f7fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px;">
        <h4 style="margin: 0 0 10px 0; color: #2d3748; font-size: 16px;">Billing Address</h4>
        <p style="margin: 0; color: #4a5568; line-height: 1.5; font-size: 14px;">
            <strong>{{ $order->billing_first_name }} {{ $order->billing_last_name }}</strong><br>
            @if($order->billing_company){{ $order->billing_company }}<br>@endif
            {{ $order->billing_address_line_1 }}<br>
            @if($order->billing_address_line_2){{ $order->billing_address_line_2 }}<br>@endif
            {{ $order->billing_city }}, {{ $order->billing_state }} {{ $order->billing_postal_code }}<br>
            {{ $order->billing_country }}
            @if($order->billing_phone)<br><strong>Phone:</strong> {{ $order->billing_phone }}@endif
        </p>
    </div>

    <!-- Shipping Address -->
    <div style="background-color: #f7fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px;">
        <h4 style="margin: 0 0 10px 0; color: #2d3748; font-size: 16px;">Shipping Address</h4>
        <p style="margin: 0; color: #4a5568; line-height: 1.5; font-size: 14px;">
            <strong>{{ $order->shipping_first_name }} {{ $order->shipping_last_name }}</strong><br>
            {{ $order->shipping_address_line_1 }}<br>
            @if($order->shipping_address_line_2){{ $order->shipping_address_line_2 }}<br>@endif
            {{ $order->shipping_city }}, {{ $order->shipping_state }} {{ $order->shipping_postal_code }}<br>
            {{ $order->shipping_country }}
            @if($order->shipping_phone)<br><strong>Phone:</strong> {{ $order->shipping_phone }}@endif
        </p>
    </div>
</div>

<!-- Order Totals -->
<div style="background-color: #f7fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px; margin: 20px 0;">
    <h3 style="margin: 0 0 15px 0; color: #2d3748; font-size: 18px;">Order Totals</h3>
    
    <table style="width: 100%; border-collapse: collapse;">
        <tr>
            <td style="padding: 5px 0; color: #4a5568;">Subtotal:</td>
            <td style="padding: 5px 0; color: #2d3748; text-align: right;">{{ $order->formatted_subtotal }}</td>
        </tr>
        
        @if($order->discount_amount > 0)
        <tr>
            <td style="padding: 5px 0; color: #38a169;">
                Discount @if($order->coupon_code)({{ $order->coupon_code }})@endif:
            </td>
            <td style="padding: 5px 0; color: #38a169; text-align: right;">-{{ $order->formatted_discount_amount }}</td>
        </tr>
        @endif
        
        <tr>
            <td style="padding: 5px 0; color: #4a5568;">Shipping:</td>
            <td style="padding: 5px 0; color: #2d3748; text-align: right;">{{ $order->formatted_shipping_amount }}</td>
        </tr>
        
        <tr>
            <td style="padding: 5px 0; color: #4a5568;">Tax:</td>
            <td style="padding: 5px 0; color: #2d3748; text-align: right;">{{ $order->formatted_tax_amount }}</td>
        </tr>
        
        <tr style="border-top: 2px solid #2d3748;">
            <td style="padding: 10px 0 5px 0; color: #2d3748; font-weight: 700; font-size: 18px;">Total:</td>
            <td style="padding: 10px 0 5px 0; color: #2d3748; font-weight: 700; font-size: 18px; text-align: right;">{{ $order->formatted_total }}</td>
        </tr>
    </table>
</div>

<!-- Action Required -->
@if($order->payment_status === 'pending')
<div style="background-color: #fffbeb; border-left: 4px solid #f59e0b; padding: 20px; margin: 20px 0; border-radius: 0 8px 8px 0;">
    <p style="margin: 0; color: #92400e; font-weight: 600;">
        ⚠️ Action Required: Payment Pending
    </p>
    <p style="margin: 10px 0 0 0; color: #92400e; font-size: 14px;">
        This order is waiting for payment completion. Monitor the payment status and follow up if needed.
    </p>
</div>
@else
<div class="info-box">
    <p><strong>✅ Payment Received</strong></p>
    <p>
        Payment has been successfully processed. You can now prepare this order for fulfillment.
    </p>
</div>
@endif

<!-- Action Buttons -->
<div class="button-container">
    <a href="{{ $admin_url }}" class="btn">
        View Order in Admin
    </a>
    
    <a href="{{ route('admin.orders.index') }}" class="btn btn-secondary" style="margin-left: 10px;">
        All Orders
    </a>
</div>

<p class="content-text">
    Please process this order promptly and update the customer with any shipping information.
</p>
@endsection
