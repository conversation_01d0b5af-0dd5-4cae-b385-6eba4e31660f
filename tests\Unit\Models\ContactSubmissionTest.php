<?php

namespace Tests\Unit\Models;

use App\Models\ContactSubmission;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ContactSubmissionTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Seed roles for User factory
        $this->seed(\Database\Seeders\RoleSeeder::class);
    }

    public function test_contact_submission_can_be_created()
    {
        $submission = ContactSubmission::create([
            'name' => '<PERSON> Doe',
            'email' => '<EMAIL>',
            'phone' => '+27123456789',
            'company' => 'Test Company',
            'service' => 'web-development',
            'subject' => 'Test Subject',
            'message' => 'Test message content',
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Test User Agent',
            'referrer' => 'https://example.com',
        ]);

        $this->assertInstanceOf(ContactSubmission::class, $submission);
        $this->assertEquals('<PERSON>', $submission->name);
        $this->assertEquals('<EMAIL>', $submission->email);
        $this->assertNotNull($submission->uuid);
        $this->assertFalse($submission->is_read);
        $this->assertFalse($submission->is_spam);
    }

    public function test_contact_submission_generates_uuid_on_creation()
    {
        $submission = ContactSubmission::create([
            'name' => 'Jane Doe',
            'email' => '<EMAIL>',
            'message' => 'Test message',
        ]);

        $this->assertNotNull($submission->uuid);
        $this->assertIsString($submission->uuid);
    }

    public function test_contact_submission_casts_attributes_correctly()
    {
        $attachments = [
            [
                'original_name' => 'test.pdf',
                'stored_name' => 'test_123.pdf',
                'path' => 'contact-submissions/2025/07/test_123.pdf',
                'size' => 1024,
                'mime_type' => 'application/pdf',
            ]
        ];

        $submission = ContactSubmission::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'message' => 'Test message',
            'attachments' => $attachments,
            'is_read' => true,
            'is_spam' => false,
            'replied_at' => now(),
        ]);

        $this->assertIsArray($submission->attachments);
        $this->assertEquals($attachments, $submission->attachments);
        $this->assertIsBool($submission->is_read);
        $this->assertIsBool($submission->is_spam);
        $this->assertInstanceOf(\Carbon\Carbon::class, $submission->replied_at);
    }

    public function test_contact_submission_belongs_to_replied_by_user()
    {
        $user = User::factory()->create();
        
        $submission = ContactSubmission::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'message' => 'Test message',
            'replied_by' => $user->id,
        ]);

        $this->assertInstanceOf(User::class, $submission->repliedBy);
        $this->assertEquals($user->id, $submission->repliedBy->id);
    }

    public function test_unread_scope_filters_unread_submissions()
    {
        ContactSubmission::create([
            'name' => 'User 1',
            'email' => '<EMAIL>',
            'message' => 'Message 1',
            'is_read' => false,
        ]);

        ContactSubmission::create([
            'name' => 'User 2',
            'email' => '<EMAIL>',
            'message' => 'Message 2',
            'is_read' => true,
        ]);

        $unreadSubmissions = ContactSubmission::unread()->get();

        $this->assertCount(1, $unreadSubmissions);
        $this->assertEquals('<EMAIL>', $unreadSubmissions->first()->email);
    }

    public function test_not_spam_scope_filters_non_spam_submissions()
    {
        ContactSubmission::create([
            'name' => 'User 1',
            'email' => '<EMAIL>',
            'message' => 'Legitimate message',
            'is_spam' => false,
        ]);

        ContactSubmission::create([
            'name' => 'Spammer',
            'email' => '<EMAIL>',
            'message' => 'Spam message',
            'is_spam' => true,
        ]);

        $nonSpamSubmissions = ContactSubmission::notSpam()->get();

        $this->assertCount(1, $nonSpamSubmissions);
        $this->assertEquals('<EMAIL>', $nonSpamSubmissions->first()->email);
    }

    public function test_mark_as_read_updates_is_read_flag()
    {
        $submission = ContactSubmission::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'message' => 'Test message',
            'is_read' => false,
        ]);

        $submission->markAsRead();

        $this->assertTrue($submission->fresh()->is_read);
    }

    public function test_mark_as_spam_updates_is_spam_flag()
    {
        $submission = ContactSubmission::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'message' => 'Test message',
            'is_spam' => false,
        ]);

        $submission->markAsSpam();

        $this->assertTrue($submission->fresh()->is_spam);
    }

    public function test_mark_as_replied_updates_reply_fields()
    {
        $user = User::factory()->create();
        
        $submission = ContactSubmission::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'message' => 'Test message',
            'is_read' => false,
        ]);

        $submission->markAsReplied($user);

        $fresh = $submission->fresh();
        $this->assertNotNull($fresh->replied_at);
        $this->assertEquals($user->id, $fresh->replied_by);
        $this->assertTrue($fresh->is_read);
    }

    public function test_contact_submission_with_attachments()
    {
        $attachments = [
            [
                'original_name' => 'project_brief.pdf',
                'stored_name' => 'project_brief_20250702_123456.pdf',
                'path' => 'contact-submissions/2025/07/project_brief_20250702_123456.pdf',
                'size' => 2048576, // 2MB
                'mime_type' => 'application/pdf',
                'scan_results' => [
                    'virus_scan' => 'clean',
                    'content_scan' => 'safe',
                    'scanned_at' => now()->toISOString(),
                ]
            ],
            [
                'original_name' => 'wireframe.png',
                'stored_name' => 'wireframe_20250702_123457.webp',
                'path' => 'contact-submissions/2025/07/wireframe_20250702_123457.webp',
                'size' => 512000, // 500KB
                'mime_type' => 'image/png',
                'scan_results' => [
                    'virus_scan' => 'clean',
                    'optimized' => true,
                    'converted_to_webp' => true,
                ]
            ]
        ];

        $submission = ContactSubmission::create([
            'name' => 'Project Manager',
            'email' => '<EMAIL>',
            'phone' => '+27123456789',
            'company' => 'Tech Company Ltd',
            'service' => 'web-development',
            'subject' => 'New Website Project',
            'message' => 'We need a new corporate website with modern design and CMS functionality.',
            'attachments' => $attachments,
            'ip_address' => '*************',
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ]);

        $this->assertCount(2, $submission->attachments);
        $this->assertEquals('project_brief.pdf', $submission->attachments[0]['original_name']);
        $this->assertEquals('wireframe.png', $submission->attachments[1]['original_name']);
        $this->assertArrayHasKey('scan_results', $submission->attachments[0]);
        $this->assertArrayHasKey('scan_results', $submission->attachments[1]);
    }
}
