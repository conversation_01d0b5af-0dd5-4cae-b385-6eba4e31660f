@extends('layouts.auth')

@section('title', __('auth.login') . ' - ' . __('common.company_name'))
@section('meta_description', __('auth.login_description'))

@section('content')
<div class="auth-container">
    <div class="auth-background">
        <div class="auth-window">
            <div class="auth-header">
                <h1 class="auth-title">{{ __('auth.welcome_back') }}</h1>
                <p class="auth-subtitle">{{ __('auth.login_subtitle') }}</p>
            </div>

            <form class="auth-form" id="login-form" method="POST" action="{{ route('login') }}">
                @csrf

                <!-- Email Field -->
                <div class="floating-input-group">
                    <input 
                        type="email" 
                        name="email" 
                        id="email"
                        class="floating-input @error('email') error @enderror" 
                        value="{{ old('email') }}"
                        required 
                        autocomplete="email"
                        placeholder=" "
                    >
                    <label for="email" class="floating-label">{{ __('auth.email') }}</label>
                    @error('email')
                        <span class="error-message">{{ $message }}</span>
                    @enderror
                </div>

                <!-- Password Field -->
                <div class="floating-input-group">
                    <input 
                        type="password" 
                        name="password" 
                        id="password"
                        class="floating-input @error('password') error @enderror" 
                        required 
                        autocomplete="current-password"
                        placeholder=" "
                    >
                    <label for="password" class="floating-label">{{ __('auth.password') }}</label>
                    <div class="password-toggle">
                        <button type="button" class="password-toggle-btn" data-target="password">
                            <i class="password-icon" data-show="👁️" data-hide="🙈">👁️</i>
                        </button>
                    </div>
                    @error('password')
                        <span class="error-message">{{ $message }}</span>
                    @enderror
                </div>

                <!-- Remember Me -->
                <div class="form-options">
                    <label class="checkbox-label">
                        <input type="checkbox" name="remember" id="remember" {{ old('remember') ? 'checked' : '' }}>
                        <span class="checkbox-custom"></span>
                        <span class="checkbox-text">{{ __('auth.remember_me') }}</span>
                    </label>
                    
                    <a href="{{ route('password.request') }}" class="forgot-password-link">
                        {{ __('auth.forgot_password') }}
                    </a>
                </div>

                <!-- Submit Button -->
                <button type="submit" class="auth-submit-btn" id="login-btn">
                    <span class="btn-text">{{ __('auth.login') }}</span>
                    <span class="btn-loading hidden">
                        <svg class="animate-spin h-5 w-5" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        {{ __('auth.logging_in') }}
                    </span>
                </button>

                <!-- Form Messages -->
                <div class="form-messages" id="form-messages"></div>

                <!-- Register Link -->
                <div class="auth-footer">
                    <span class="auth-footer-text">{{ __('auth.no_account') }}</span>
                    <a href="{{ route('register') }}" class="auth-footer-link">{{ __('auth.create_account') }}</a>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="{{ asset('js/auth-forms.js') }}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize login form with AJAX (no client-side validation)
    const loginForm = new AuthForm('login-form', {
        submitButton: 'login-btn',
        loadingText: '{{ __("auth.logging_in") }}',
        successRedirect: '{{ route("dashboard") }}',
        validateOnSubmit: false, // No client-side validation for login
        messages: {
            networkError: '{{ __("auth.network_error") }}',
            serverError: '{{ __("auth.server_error") }}',
            validationError: '{{ __("auth.validation_error") }}'
        }
    });
});
</script>
@endpush
