<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Project;
use App\Models\Service;
use App\Models\User;
use App\Services\ActivityLogger;
use App\Services\ImageService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class ProjectController extends Controller
{
    protected ActivityLogger $activityLogger;
    protected ImageService $imageService;

    /**
     * Create a new controller instance.
     */
    public function __construct(ActivityLogger $activityLogger, ImageService $imageService)
    {
        $this->middleware(['auth', 'role:admin,staff']);
        $this->activityLogger = $activityLogger;
        $this->imageService = $imageService;
    }

    /**
     * Display a listing of projects.
     */
    public function index(Request $request): View
    {
        $query = Project::with(['client', 'service'])
            ->where('is_deleted', false)
            ->latest();

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('service_id')) {
            $query->where('service_id', $request->service_id);
        }

        if ($request->filled('client_id')) {
            $query->where('client_id', $request->client_id);
        }

        if ($request->filled('is_published')) {
            $query->where('is_published', $request->is_published === '1');
        }

        if ($request->filled('is_featured')) {
            $query->where('is_featured', $request->is_featured === '1');
        }

        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                  ->orWhere('description', 'like', "%{$searchTerm}%")
                  ->orWhere('client_name', 'like', "%{$searchTerm}%");
            });
        }

        $projects = $query->paginate(20)->withQueryString();

        // Get filter options
        $services = Service::active()->ordered()->get();
        $clients = User::whereHas('projects')->orWhereHas('role', function($query) {
            $query->where('name', 'client');
        })->get();
        $statuses = [
            'planning' => 'Planning',
            'in_progress' => 'In Progress',
            'review' => 'Under Review',
            'completed' => 'Completed',
            'on_hold' => 'On Hold',
            'cancelled' => 'Cancelled',
        ];

        // Log admin access
        $this->activityLogger->logCustomerActivity(
            'admin_projects_list',
            'Admin accessed projects list',
            'success',
            null,
            [
                'filters_applied' => $request->only(['status', 'service_id', 'client_id', 'is_published', 'is_featured', 'search']),
                'total_results' => $projects->total(),
                'admin_user' => Auth::user()->email,
            ]
        );

        return view('admin.projects.index', compact('projects', 'services', 'clients', 'statuses'));
    }

    /**
     * Show the form for creating a new project.
     */
    public function create(): View
    {
        $services = Service::active()->ordered()->get();
        $clients = User::where('role_id', function($query) {
            $query->select('id')->from('roles')->where('name', 'client');
        })->get();

        $statuses = [
            'planning' => 'Planning',
            'in_progress' => 'In Progress',
            'review' => 'Under Review',
            'completed' => 'Completed',
            'on_hold' => 'On Hold',
            'cancelled' => 'Cancelled',
        ];

        return view('admin.projects.create', compact('services', 'clients', 'statuses'));
    }

    /**
     * Store a newly created project.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string|max:1000',
            'content' => 'nullable|string|max:50000',
            'client_id' => 'nullable|exists:users,id',
            'client_name' => 'required|string|max:200',
            'service_id' => 'nullable|exists:services,id',
            'project_url' => 'nullable|url|max:500',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'estimated_hours' => 'nullable|numeric|min:0|max:10000',
            'actual_hours' => 'nullable|numeric|min:0|max:10000',
            'hourly_rate' => 'nullable|numeric|min:0|max:10000',
            'total_amount' => 'nullable|numeric|min:0|max:999999.99',
            'currency_code' => 'nullable|string|max:3',
            'status' => 'required|in:planning,in_progress,review,completed,on_hold,cancelled',
            'priority' => 'nullable|in:low,medium,high,urgent',
            'is_featured' => 'boolean',
            'is_published' => 'boolean',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:500',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
        ]);

        // Generate slug
        $validated['slug'] = Str::slug($validated['title']);
        
        // Ensure unique slug
        $originalSlug = $validated['slug'];
        $counter = 1;
        while (Project::where('slug', $validated['slug'])->exists()) {
            $validated['slug'] = $originalSlug . '-' . $counter;
            $counter++;
        }

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            $result = $this->imageService->processUploadedImage($request->file('featured_image'), [
                'subdirectory' => 'projects',
                'create_variants' => true,
                'create_webp' => true,
            ]);

            if ($result['success'] && isset($result['original_path'])) {
                $validated['featured_image'] = $result['original_path'];
            } else {
                // If upload failed, add error to session
                if (!$result['success']) {
                    $errorMessage = 'Failed to upload image';
                    if (isset($result['errors']) && is_array($result['errors'])) {
                        $errorMessage .= ': ' . implode(', ', $result['errors']);
                    }

                    return redirect()->back()
                        ->withErrors(['featured_image' => $errorMessage])
                        ->withInput();
                }
            }
        }

        $project = Project::create($validated);

        // Log project creation
        $this->activityLogger->logCustomerActivity(
            'admin_project_created',
            "Admin created project: {$project->title}",
            'success',
            null,
            [
                'project_id' => $project->id,
                'project_title' => $project->title,
                'admin_user' => Auth::user()->email,
            ]
        );

        return redirect()->route('admin.projects.index')
            ->with('success', 'Project created successfully.');
    }

    /**
     * Display the specified project.
     */
    public function show(Project $project): View
    {
        $project->load(['client', 'service']);

        // Log admin view
        $this->activityLogger->logCustomerActivity(
            'admin_project_view',
            "Admin viewed project: {$project->title}",
            'success',
            null,
            [
                'project_id' => $project->id,
                'project_title' => $project->title,
                'admin_user' => Auth::user()->email,
            ]
        );

        return view('admin.projects.show', compact('project'));
    }

    /**
     * Show the form for editing the specified project.
     */
    public function edit(Project $project): View
    {
        $project->load(['client', 'service']);
        $services = Service::active()->ordered()->get();
        $clients = User::where('role_id', function($query) {
            $query->select('id')->from('roles')->where('name', 'client');
        })->get();

        $statuses = [
            'planning' => 'Planning',
            'in_progress' => 'In Progress',
            'review' => 'Under Review',
            'completed' => 'Completed',
            'on_hold' => 'On Hold',
            'cancelled' => 'Cancelled',
        ];

        return view('admin.projects.edit', compact('project', 'services', 'clients', 'statuses'));
    }

    /**
     * Update the specified project.
     */
    public function update(Request $request, Project $project): RedirectResponse
    {
        $originalData = $project->toArray();
        
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string|max:1000',
            'content' => 'nullable|string|max:50000',
            'client_id' => 'nullable|exists:users,id',
            'client_name' => 'required|string|max:200',
            'service_id' => 'nullable|exists:services,id',
            'project_url' => 'nullable|url|max:500',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'estimated_hours' => 'nullable|numeric|min:0|max:10000',
            'actual_hours' => 'nullable|numeric|min:0|max:10000',
            'hourly_rate' => 'nullable|numeric|min:0|max:10000',
            'total_amount' => 'nullable|numeric|min:0|max:999999.99',
            'currency_code' => 'nullable|string|max:3',
            'status' => 'required|in:planning,in_progress,review,completed,on_hold,cancelled',
            'priority' => 'nullable|in:low,medium,high,urgent',
            'is_featured' => 'boolean',
            'is_published' => 'boolean',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:500',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
        ]);

        // Update slug if title changed
        if ($validated['title'] !== $project->title) {
            $validated['slug'] = Str::slug($validated['title']);
            
            // Ensure unique slug
            $originalSlug = $validated['slug'];
            $counter = 1;
            while (Project::where('slug', $validated['slug'])->where('id', '!=', $project->id)->exists()) {
                $validated['slug'] = $originalSlug . '-' . $counter;
                $counter++;
            }
        }

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            // Delete old image if exists
            if ($project->featured_image) {
                // Handle both relative paths and full system paths (for legacy data)
                $imagePath = $project->featured_image;
                if (str_contains($imagePath, storage_path())) {
                    // Convert full system path to relative path
                    $imagePath = str_replace(storage_path('app/public/'), '', $imagePath);
                    $imagePath = str_replace('\\', '/', $imagePath); // Normalize path separators
                }

                try {
                    $this->imageService->deleteImage($imagePath, true);
                } catch (\Exception $e) {
                    // Log the error but don't fail the update
                    \Log::warning('Failed to delete old project image: ' . $e->getMessage(), [
                        'project_id' => $project->id,
                        'image_path' => $imagePath
                    ]);
                }
            }

            $result = $this->imageService->processUploadedImage($request->file('featured_image'), [
                'subdirectory' => 'projects',
                'create_variants' => true,
                'create_webp' => true,
            ]);

            if ($result['success'] && isset($result['original_path'])) {
                $validated['featured_image'] = $result['original_path'];
            } else {
                // If upload failed, add error to session
                if (!$result['success']) {
                    $errorMessage = 'Failed to upload image';
                    if (isset($result['errors']) && is_array($result['errors'])) {
                        $errorMessage .= ': ' . implode(', ', $result['errors']);
                    }

                    return redirect()->back()
                        ->withErrors(['featured_image' => $errorMessage])
                        ->withInput();
                }
            }
        }

        $project->update($validated);

        // Log project update
        $this->activityLogger->logCustomerActivity(
            'admin_project_updated',
            "Admin updated project: {$project->title}",
            'success',
            null,
            [
                'project_id' => $project->id,
                'project_title' => $project->title,
                'changes_made' => array_keys(array_diff_assoc($validated, $originalData)),
                'admin_user' => Auth::user()->email,
            ]
        );

        return redirect()->route('admin.projects.index')
            ->with('success', 'Project updated successfully.');
    }

    /**
     * Remove the specified project (soft delete).
     */
    public function destroy(Project $project): RedirectResponse
    {
        $project->update(['is_deleted' => true]);

        // Log project deletion
        $this->activityLogger->logCustomerActivity(
            'admin_project_deleted',
            "Admin deleted project: {$project->title}",
            'success',
            null,
            [
                'project_id' => $project->id,
                'project_title' => $project->title,
                'admin_user' => Auth::user()->email,
            ]
        );

        return redirect()->route('admin.projects.index')
            ->with('success', 'Project deleted successfully.');
    }

    /**
     * Toggle project published status.
     */
    public function togglePublished(Project $project): JsonResponse
    {
        $newStatus = !$project->is_published;
        $project->update(['is_published' => $newStatus]);

        // Log status change
        $this->activityLogger->logCustomerActivity(
            'admin_project_published_toggle',
            "Admin " . ($newStatus ? 'published' : 'unpublished') . " project: {$project->title}",
            'success',
            null,
            [
                'project_id' => $project->id,
                'project_title' => $project->title,
                'new_status' => $newStatus,
                'admin_user' => Auth::user()->email,
            ]
        );

        return response()->json([
            'success' => true,
            'is_published' => $newStatus,
            'message' => 'Project ' . ($newStatus ? 'published' : 'unpublished') . ' successfully.',
        ]);
    }

    /**
     * Toggle project featured status.
     */
    public function toggleFeatured(Project $project): JsonResponse
    {
        $newStatus = !$project->is_featured;
        $project->update(['is_featured' => $newStatus]);

        // Log status change
        $this->activityLogger->logCustomerActivity(
            'admin_project_featured_toggle',
            "Admin " . ($newStatus ? 'featured' : 'unfeatured') . " project: {$project->title}",
            'success',
            null,
            [
                'project_id' => $project->id,
                'project_title' => $project->title,
                'new_status' => $newStatus,
                'admin_user' => Auth::user()->email,
            ]
        );

        return response()->json([
            'success' => true,
            'is_featured' => $newStatus,
            'message' => 'Project ' . ($newStatus ? 'featured' : 'unfeatured') . ' successfully.',
        ]);
    }
}
