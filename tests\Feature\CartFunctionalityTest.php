<?php

namespace Tests\Feature;

use App\Models\Product;
use App\Models\ShoppingCart;
use App\Models\Currency;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Session;
use Tests\TestCase;

class CartFunctionalityTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create currency
        $this->currency = Currency::factory()->create([
            'code' => 'ZAR',
            'symbol' => 'R',
        ]);
    }

    /** @test */
    public function guest_can_add_product_to_cart()
    {
        $product = Product::factory()->create([
            'is_active' => true,
            'track_inventory' => true,
            'inventory_quantity' => 10,
            'price' => 100.00,
        ]);

        $response = $this->postJson('/cart/add', [
            'product_id' => $product->id,
            'quantity' => 2,
        ]);

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'message' => 'Product added to cart successfully!',
        ]);

        // Check that cart was created
        $this->assertDatabaseHas('shopping_carts', [
            'session_id' => Session::getId(),
            'user_id' => null,
        ]);

        // Check that cart item was created
        $this->assertDatabaseHas('cart_items', [
            'product_id' => $product->id,
            'quantity' => 2,
            'price' => 100.00,
            'total' => 200.00,
        ]);
    }

    /** @test */
    public function cart_add_validates_required_fields()
    {
        $response = $this->postJson('/cart/add', []);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['product_id', 'quantity']);
    }

    /** @test */
    public function cart_add_checks_product_exists()
    {
        $response = $this->postJson('/cart/add', [
            'product_id' => 999999,
            'quantity' => 1,
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['product_id']);
    }

    /** @test */
    public function cart_add_checks_product_is_active()
    {
        $product = Product::factory()->create([
            'is_active' => false,
        ]);

        $response = $this->postJson('/cart/add', [
            'product_id' => $product->id,
            'quantity' => 1,
        ]);

        $response->assertStatus(400);
        $response->assertJson([
            'success' => false,
            'message' => 'This product is no longer available.',
        ]);
    }

    /** @test */
    public function cart_add_checks_inventory()
    {
        $product = Product::factory()->create([
            'is_active' => true,
            'track_inventory' => true,
            'inventory_quantity' => 5,
        ]);

        $response = $this->postJson('/cart/add', [
            'product_id' => $product->id,
            'quantity' => 10, // More than available
        ]);

        $response->assertStatus(400);
        $response->assertJson([
            'success' => false,
            'message' => 'Insufficient stock available.',
        ]);
    }

    /** @test */
    public function cart_can_be_retrieved()
    {
        $product = Product::factory()->create([
            'is_active' => true,
            'price' => 50.00,
        ]);

        // Add product to cart
        $this->postJson('/cart/add', [
            'product_id' => $product->id,
            'quantity' => 1,
        ]);

        // Get cart
        $response = $this->get('/cart');
        $response->assertStatus(200);
        $response->assertViewIs('pages.cart.index');
        $response->assertViewHas('cart');
    }

    /** @test */
    public function cart_count_endpoint_works()
    {
        $product = Product::factory()->create([
            'is_active' => true,
            'price' => 25.00,
        ]);

        // Add product to cart
        $this->postJson('/cart/add', [
            'product_id' => $product->id,
            'quantity' => 3,
        ]);

        // Get cart count
        $response = $this->getJson('/cart/count');
        $response->assertStatus(200);
        $response->assertJson([
            'count' => 3,
        ]);
    }
}
