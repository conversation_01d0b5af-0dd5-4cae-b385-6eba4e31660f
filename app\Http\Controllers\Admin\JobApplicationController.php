<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\JobApplication;
use App\Models\Job;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class JobApplicationController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:admin,staff');
    }

    /**
     * Display a listing of job applications.
     */
    public function index(Request $request): View
    {
        $query = JobApplication::with(['job', 'user']);

        // Search functionality
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('first_name', 'like', "%{$searchTerm}%")
                  ->orWhere('last_name', 'like', "%{$searchTerm}%")
                  ->orWhere('email', 'like', "%{$searchTerm}%")
                  ->orWhere('reference_number', 'like', "%{$searchTerm}%")
                  ->orWhereHas('job', function ($jobQuery) use ($searchTerm) {
                      $jobQuery->where('title', 'like', "%{$searchTerm}%");
                  });
            });
        }

        // Filter by status
        if ($request->filled('status') && $request->status !== 'all') {
            $query->where('status', $request->status);
        }

        // Filter by job
        if ($request->filled('job_id') && $request->job_id !== 'all') {
            $query->where('job_id', $request->job_id);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $applications = $query->latest()->paginate(15);

        // Get filter options
        $statuses = JobApplication::getStatusOptions();
        $jobs = Job::active()->orderBy('title')->get();

        // Get statistics
        $stats = [
            'total' => JobApplication::count(),
            'pending' => JobApplication::where('status', 'pending')->count(),
            'reviewing' => JobApplication::where('status', 'reviewing')->count(),
            'shortlisted' => JobApplication::where('status', 'shortlisted')->count(),
            'hired' => JobApplication::where('status', 'hired')->count(),
            'recent' => JobApplication::recent(7)->count(),
        ];

        return view('admin.job-applications.index', compact('applications', 'statuses', 'jobs', 'stats'));
    }

    /**
     * Display the specified job application.
     */
    public function show(JobApplication $jobApplication): View
    {
        $jobApplication->load(['job', 'user', 'reviewedBy']);

        return view('admin.job-applications.show', compact('jobApplication'));
    }

    /**
     * Update the specified job application.
     */
    public function update(Request $request, JobApplication $jobApplication): RedirectResponse
    {
        $validated = $request->validate([
            'status' => 'required|string|in:' . implode(',', array_keys(JobApplication::getStatusOptions())),
            'admin_notes' => 'nullable|string|max:2000',
        ]);

        $validated['reviewed_by'] = Auth::id();
        $validated['reviewed_at'] = now();

        $jobApplication->update($validated);

        return redirect()->route('admin.job-applications.show', $jobApplication)
                        ->with('success', 'Job application updated successfully.');
    }

    /**
     * Remove the specified job application.
     */
    public function destroy(JobApplication $jobApplication): RedirectResponse
    {
        $jobApplication->delete();

        return redirect()->route('admin.job-applications.index')
                        ->with('success', 'Job application deleted successfully.');
    }

    /**
     * Update application status via AJAX.
     */
    public function updateStatus(Request $request, JobApplication $jobApplication): JsonResponse
    {
        $validated = $request->validate([
            'status' => 'required|string|in:' . implode(',', array_keys(JobApplication::getStatusOptions())),
            'admin_notes' => 'nullable|string|max:2000',
        ]);

        $validated['reviewed_by'] = Auth::id();
        $validated['reviewed_at'] = now();

        $jobApplication->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'Application status updated successfully.',
            'status' => $jobApplication->status,
            'status_label' => JobApplication::getStatusOptions()[$jobApplication->status],
            'reviewed_at' => $jobApplication->reviewed_at->format('M j, Y g:i A'),
            'reviewed_by' => $jobApplication->reviewedBy->name ?? 'Unknown',
        ]);
    }

    /**
     * Download application attachment.
     */
    public function downloadAttachment(JobApplication $jobApplication, int $attachmentIndex)
    {
        $attachments = $jobApplication->attachments ?? [];

        if (!isset($attachments[$attachmentIndex])) {
            abort(404, 'Attachment not found.');
        }

        $attachment = $attachments[$attachmentIndex];
        $filePath = $attachment['path'] ?? null;

        if (!$filePath || !Storage::exists($filePath)) {
            abort(404, 'File not found.');
        }

        $originalName = $attachment['original_name'] ?? 'attachment';

        return Storage::download($filePath, $originalName);
    }

    /**
     * Get job application statistics.
     */
    public function getStatistics(Request $request): JsonResponse
    {
        $period = $request->get('period', '30'); // days

        $stats = [
            'total_applications' => JobApplication::count(),
            'recent_applications' => JobApplication::recent($period)->count(),
            'pending_applications' => JobApplication::where('status', 'pending')->count(),
            'hired_applications' => JobApplication::where('status', 'hired')->count(),
            'applications_by_status' => JobApplication::selectRaw('status, COUNT(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status')
                ->toArray(),
            'applications_by_job' => JobApplication::with('job')
                ->selectRaw('job_id, COUNT(*) as count')
                ->groupBy('job_id')
                ->get()
                ->mapWithKeys(function ($item) {
                    return [$item->job->title ?? 'Unknown' => $item->count];
                })
                ->toArray(),
            'recent_trend' => JobApplication::selectRaw('DATE(created_at) as date, COUNT(*) as count')
                ->where('created_at', '>=', now()->subDays($period))
                ->groupBy('date')
                ->orderBy('date')
                ->pluck('count', 'date')
                ->toArray(),
        ];

        return response()->json($stats);
    }
}
