@props([
    'title' => null,
    'subtitle' => null,
    'padding' => true,
    'shadow' => true,
    'border' => true
])

@php
$classes = 'bg-white rounded-xl overflow-hidden';

if ($shadow) {
    $classes .= ' shadow-soft';
}

if ($border) {
    $classes .= ' border border-neutral-100';
}
@endphp

<div {{ $attributes->merge(['class' => $classes]) }}>
    @if($title || $subtitle || isset($header))
        <div class="px-6 py-4 border-b border-neutral-200">
            @isset($header)
                {{ $header }}
            @else
                <div class="flex items-center justify-between">
                    <div>
                        @if($title)
                            <h3 class="text-lg font-semibold text-gray-900">{{ $title }}</h3>
                        @endif
                        @if($subtitle)
                            <p class="text-sm text-gray-600 mt-1">{{ $subtitle }}</p>
                        @endif
                    </div>
                    @isset($actions)
                        <div class="flex items-center space-x-2">
                            {{ $actions }}
                        </div>
                    @endisset
                </div>
            @endisset
        </div>
    @endif

    <div @class(['p-6' => $padding])>
        {{ $slot }}
    </div>

    @isset($footer)
        <div class="px-6 py-4 bg-gray-50 border-t border-neutral-200">
            {{ $footer }}
        </div>
    @endisset
</div>
