<?php

namespace App\Services\Image\Traits;

use Illuminate\Support\Facades\Log;

trait ImageLogging
{
    /**
     * Log image processing information
     */
    protected function logProcessing(string $message, array $context = []): void
    {
        if (!config('image.logging.enabled', true)) {
            return;
        }
        
        $channel = config('image.logging.log_channel', 'daily');
        $level = config('image.logging.log_level', 'info');
        
        $context['service'] = 'ImageService';
        $context['timestamp'] = now()->toISOString();
        
        Log::channel($channel)->log($level, $message, $context);
    }
    
    /**
     * Log image processing errors
     */
    protected function logError(string $message, array $context = []): void
    {
        if (!config('image.logging.enabled', true)) {
            return;
        }
        
        $channel = config('image.logging.log_channel', 'daily');
        
        $context['service'] = 'ImageService';
        $context['timestamp'] = now()->toISOString();
        $context['error'] = true;
        
        Log::channel($channel)->error($message, $context);
    }
    
    /**
     * Log processing time
     */
    protected function logProcessingTime(string $operation, float $startTime, array $context = []): void
    {
        if (!config('image.logging.log_processing_time', true)) {
            return;
        }
        
        $endTime = microtime(true);
        $processingTime = round(($endTime - $startTime) * 1000, 2); // Convert to milliseconds
        
        $context['operation'] = $operation;
        $context['processing_time_ms'] = $processingTime;
        
        $this->logProcessing("Image processing completed: {$operation} took {$processingTime}ms", $context);
    }
    
    /**
     * Log file details
     */
    protected function logFileDetails(string $operation, array $fileInfo): void
    {
        if (!config('image.logging.log_file_details', true)) {
            return;
        }
        
        $context = [
            'operation' => $operation,
            'file_info' => $fileInfo
        ];
        
        $this->logProcessing("File details logged for operation: {$operation}", $context);
    }
}
