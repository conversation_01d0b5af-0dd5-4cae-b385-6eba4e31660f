@extends('layouts.auth')

@section('title', __('auth.register') . ' - ' . __('common.company_name'))
@section('meta_description', __('auth.register_description'))

@section('content')
<div class="auth-container">
    <div class="auth-background">
        <div class="auth-window register-window">
            <div class="auth-header">
                <h1 class="auth-title">{{ __('auth.create_account') }}</h1>
                <p class="auth-subtitle">{{ __('auth.register_subtitle') }}</p>
            </div>

            <form class="auth-form" id="register-form" method="POST" action="{{ route('register') }}">
                @csrf

                <!-- First Name Field -->
                <div class="floating-input-group">
                    <input 
                        type="text" 
                        name="first_name" 
                        id="first_name"
                        class="floating-input @error('first_name') error @enderror" 
                        value="{{ old('first_name') }}"
                        required 
                        autocomplete="given-name"
                        placeholder=" "
                    >
                    <label for="first_name" class="floating-label">{{ __('auth.first_name') }}</label>
                    @error('first_name')
                        <span class="error-message">{{ $message }}</span>
                    @enderror
                </div>

                <!-- Last Name Field -->
                <div class="floating-input-group">
                    <input 
                        type="text" 
                        name="last_name" 
                        id="last_name"
                        class="floating-input @error('last_name') error @enderror" 
                        value="{{ old('last_name') }}"
                        required 
                        autocomplete="family-name"
                        placeholder=" "
                    >
                    <label for="last_name" class="floating-label">{{ __('auth.last_name') }}</label>
                    @error('last_name')
                        <span class="error-message">{{ $message }}</span>
                    @enderror
                </div>

                <!-- Email Field -->
                <div class="floating-input-group">
                    <input 
                        type="email" 
                        name="email" 
                        id="email"
                        class="floating-input @error('email') error @enderror" 
                        value="{{ old('email') }}"
                        required 
                        autocomplete="email"
                        placeholder=" "
                    >
                    <label for="email" class="floating-label">{{ __('auth.email') }}</label>
                    @error('email')
                        <span class="error-message">{{ $message }}</span>
                    @enderror
                </div>

                <!-- Phone Field -->
                <div class="floating-input-group">
                    <input 
                        type="tel" 
                        name="phone" 
                        id="phone"
                        class="floating-input @error('phone') error @enderror" 
                        value="{{ old('phone') }}"
                        autocomplete="tel"
                        placeholder=" "
                    >
                    <label for="phone" class="floating-label">{{ __('auth.phone') }} ({{ __('auth.optional') }})</label>
                    @error('phone')
                        <span class="error-message">{{ $message }}</span>
                    @enderror
                </div>

                <!-- Password Field -->
                <div class="floating-input-group">
                    <input 
                        type="password" 
                        name="password" 
                        id="password"
                        class="floating-input @error('password') error @enderror" 
                        required 
                        autocomplete="new-password"
                        placeholder=" "
                    >
                    <label for="password" class="floating-label">{{ __('auth.password') }}</label>
                    <div class="password-toggle">
                        <button type="button" class="password-toggle-btn" data-target="password">
                            <i class="password-icon" data-show="👁️" data-hide="🙈">👁️</i>
                        </button>
                    </div>
                    @error('password')
                        <span class="error-message">{{ $message }}</span>
                    @enderror
                    <div class="password-strength" id="password-strength"></div>
                </div>

                <!-- Confirm Password Field -->
                <div class="floating-input-group">
                    <input 
                        type="password" 
                        name="password_confirmation" 
                        id="password_confirmation"
                        class="floating-input @error('password_confirmation') error @enderror" 
                        required 
                        autocomplete="new-password"
                        placeholder=" "
                    >
                    <label for="password_confirmation" class="floating-label">{{ __('auth.password_confirmation') }}</label>
                    <div class="password-toggle">
                        <button type="button" class="password-toggle-btn" data-target="password_confirmation">
                            <i class="password-icon" data-show="👁️" data-hide="🙈">👁️</i>
                        </button>
                    </div>
                    @error('password_confirmation')
                        <span class="error-message">{{ $message }}</span>
                    @enderror
                </div>

                <!-- Terms and Conditions -->
                <div class="form-options">
                    <label class="checkbox-label">
                        <input type="hidden" name="terms" value="0">
                        <input type="checkbox" name="terms" id="terms" value="1" {{ old('terms') ? 'checked' : '' }}>
                        <span class="checkbox-custom"></span>
                        <span class="checkbox-text">
                            {{ __('auth.agree_to') }}
                            <a href="{{ route('terms') }}" target="_blank" class="terms-link">{{ __('auth.terms_and_conditions') }}</a>
                            {{ __('auth.and') }}
                            <a href="{{ route('privacy') }}" target="_blank" class="terms-link">{{ __('auth.privacy_policy') }}</a>
                        </span>
                    </label>
                    @error('terms')
                        <span class="error-message">{{ $message }}</span>
                    @enderror
                </div>

                <!-- Submit Button -->
                <button type="submit" class="auth-submit-btn" id="register-btn">
                    <span class="btn-text">{{ __('auth.create_account') }}</span>
                    <span class="btn-loading hidden">
                        <svg class="animate-spin h-5 w-5" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        {{ __('auth.creating_account') }}
                    </span>
                </button>

                <!-- Form Messages -->
                <div class="form-messages" id="form-messages"></div>

                <!-- Login Link -->
                <div class="auth-footer">
                    <span class="auth-footer-text">{{ __('auth.already_have_account') }}</span>
                    <a href="{{ route('login') }}" class="auth-footer-link">{{ __('auth.login') }}</a>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="{{ asset('js/auth-forms.js') }}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize register form with AJAX validation
    const registerForm = new AuthForm('register-form', {
        submitButton: 'register-btn',
        loadingText: '{{ __("auth.creating_account") }}',
        successRedirect: '{{ route("dashboard") }}',
        messages: {
            networkError: '{{ __("auth.network_error") }}',
            serverError: '{{ __("auth.server_error") }}',
            validationError: '{{ __("auth.validation_error") }}'
        }
    });

    // Initialize password strength checker
    const passwordStrengthChecker = new PasswordStrength('password', 'password-strength');
});
</script>
@endpush
