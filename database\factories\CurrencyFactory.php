<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Currency>
 */
class CurrencyFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $currencies = [
            ['code' => 'ZAR', 'name' => 'South African Rand', 'symbol' => 'R', 'rate' => 1.0000],
            ['code' => 'USD', 'name' => 'US Dollar', 'symbol' => '$', 'rate' => 0.055],
            ['code' => 'EUR', 'name' => 'Euro', 'symbol' => '€', 'rate' => 0.050],
            ['code' => 'GBP', 'name' => 'British Pound', 'symbol' => '£', 'rate' => 0.043],
        ];

        $currency = $this->faker->randomElement($currencies);

        return [
            'code' => $currency['code'],
            'name' => $currency['name'],
            'symbol' => $currency['symbol'],
            'exchange_rate' => $currency['rate'],
            'is_default' => $currency['code'] === 'ZAR',
            'is_active' => true,
        ];
    }

    /**
     * Indicate that the currency is the default currency.
     */
    public function default(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_default' => true,
            'code' => 'ZAR',
            'name' => 'South African Rand',
            'symbol' => 'R',
            'exchange_rate' => 1.0000,
        ]);
    }

    /**
     * Indicate that the currency is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }
}
