/**
 * Auth Forms JavaScript
 * Handles form validation, animations, and interactions for login/register forms
 */

class AuthForm {
    constructor(formId, options = {}) {
        this.form = document.getElementById(formId);
        this.options = {
            submitButton: options.submitButton || 'submit-btn',
            loadingText: options.loadingText || 'Loading...',
            successRedirect: options.successRedirect || '/',
            validateOnSubmit: options.validateOnSubmit !== false, // Default to true
            messages: options.messages || {},
            ...options
        };
        
        this.submitBtn = document.getElementById(this.options.submitButton);
        this.messagesContainer = document.getElementById('form-messages');
        
        this.init();
    }
    
    init() {
        this.setupFloatingLabels();
        this.setupPasswordToggles();
        this.setupFormValidation();
        this.setupFormSubmission();
    }
    
    setupFloatingLabels() {
        const inputs = this.form.querySelectorAll('.floating-input');
        
        inputs.forEach(input => {
            // Handle initial state
            this.updateFloatingLabel(input);
            
            // Handle focus events
            input.addEventListener('focus', () => {
                input.classList.add('focus');
                const label = input.parentElement.querySelector('.floating-label');
                if (label) label.classList.add('focus');
            });
            
            // Handle blur events
            input.addEventListener('blur', () => {
                input.classList.remove('focus');
                this.updateFloatingLabel(input);
            });
            
            // Handle input events
            input.addEventListener('input', () => {
                this.updateFloatingLabel(input);
                this.clearFieldError(input);
            });
        });
    }
    
    updateFloatingLabel(input) {
        const label = input.parentElement.querySelector('.floating-label');
        if (!label) return;
        
        if (input.value.trim() !== '') {
            input.classList.add('has-value');
            label.classList.add('focus');
        } else {
            input.classList.remove('has-value');
            if (!input.classList.contains('focus')) {
                label.classList.remove('focus');
            }
        }
    }
    
    setupPasswordToggles() {
        const toggleBtns = this.form.querySelectorAll('.password-toggle-btn');
        
        toggleBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = btn.getAttribute('data-target');
                const targetInput = document.getElementById(targetId);
                const icon = btn.querySelector('.password-icon');
                
                if (targetInput.type === 'password') {
                    targetInput.type = 'text';
                    icon.textContent = icon.getAttribute('data-hide');
                } else {
                    targetInput.type = 'password';
                    icon.textContent = icon.getAttribute('data-show');
                }
            });
        });
    }
    
    setupFormValidation() {
        const inputs = this.form.querySelectorAll('.floating-input');
        
        inputs.forEach(input => {
            input.addEventListener('blur', () => {
                this.validateField(input);
            });
            
            input.addEventListener('input', () => {
                if (input.classList.contains('error')) {
                    this.validateField(input);
                }
            });
        });
        
        // Real-time password confirmation validation
        const password = this.form.querySelector('#password');
        const passwordConfirmation = this.form.querySelector('#password_confirmation');
        
        if (password && passwordConfirmation) {
            passwordConfirmation.addEventListener('input', () => {
                this.validatePasswordConfirmation(password, passwordConfirmation);
            });
        }
    }
    
    validateField(input) {
        // Skip validation if validateOnSubmit is disabled (like for login forms)
        if (!this.options.validateOnSubmit) {
            return true;
        }

        const value = input.value.trim();
        const name = input.name;
        let isValid = true;
        let errorMessage = '';

        // Required field validation
        if (input.hasAttribute('required') && !value) {
            isValid = false;
            errorMessage = `${this.getFieldLabel(input)} is required.`;
        }

        // Email validation (only for registration, not login)
        if (isValid && input.type === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                isValid = false;
                errorMessage = 'Please enter a valid email address.';
            }
        }

        // Password validation (only for registration, not login)
        if (isValid && name === 'password' && value) {
            const passwordValidation = this.validatePassword(value);
            if (!passwordValidation.isValid) {
                isValid = false;
                errorMessage = passwordValidation.message;
            }
        }

        // Name validation
        if (isValid && (name === 'first_name' || name === 'last_name') && value) {
            const nameRegex = /^[a-zA-Z\s\-'\.]+$/;
            if (!nameRegex.test(value)) {
                isValid = false;
                errorMessage = 'Please enter a valid name (letters, spaces, hyphens, and apostrophes only).';
            }
        }

        this.setFieldValidation(input, isValid, errorMessage);
        return isValid;
    }
    
    validatePassword(password) {
        const minLength = 8;
        const hasLowerCase = /[a-z]/.test(password);
        const hasUpperCase = /[A-Z]/.test(password);
        const hasNumbers = /\d/.test(password);
        const hasSymbols = /[!@#$%^&*(),.?":{}|<>]/.test(password);
        
        if (password.length < minLength) {
            return { isValid: false, message: `Password must be at least ${minLength} characters long.` };
        }
        
        if (!hasLowerCase) {
            return { isValid: false, message: 'Password must contain at least one lowercase letter.' };
        }
        
        if (!hasUpperCase) {
            return { isValid: false, message: 'Password must contain at least one uppercase letter.' };
        }
        
        if (!hasNumbers) {
            return { isValid: false, message: 'Password must contain at least one number.' };
        }
        
        if (!hasSymbols) {
            return { isValid: false, message: 'Password must contain at least one special character.' };
        }
        
        return { isValid: true, message: '' };
    }
    
    validatePasswordConfirmation(password, confirmation) {
        const isValid = password.value === confirmation.value;
        const errorMessage = isValid ? '' : 'Passwords do not match.';
        
        this.setFieldValidation(confirmation, isValid, errorMessage);
        return isValid;
    }
    
    setFieldValidation(input, isValid, errorMessage) {
        const errorElement = input.parentElement.querySelector('.error-message');
        
        if (isValid) {
            input.classList.remove('error');
            input.classList.add('success');
            if (errorElement) {
                errorElement.remove();
            }
        } else {
            input.classList.remove('success');
            input.classList.add('error');
            
            if (errorMessage) {
                if (errorElement) {
                    errorElement.textContent = errorMessage;
                } else {
                    const errorSpan = document.createElement('span');
                    errorSpan.className = 'error-message';
                    errorSpan.textContent = errorMessage;
                    input.parentElement.appendChild(errorSpan);
                }
            }
        }
    }
    
    clearFieldError(input) {
        input.classList.remove('error');
        const errorElement = input.parentElement.querySelector('.error-message');
        if (errorElement) {
            errorElement.remove();
        }
    }
    
    getFieldLabel(input) {
        const label = input.parentElement.querySelector('.floating-label');
        return label ? label.textContent : input.name;
    }
    
    setupFormSubmission() {
        this.form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleSubmit();
        });
    }
    
    async handleSubmit() {
        // Only validate if validateOnSubmit is enabled
        if (this.options.validateOnSubmit) {
            // Validate all fields
            const inputs = this.form.querySelectorAll('.floating-input');
            let isFormValid = true;

            inputs.forEach(input => {
                if (!this.validateField(input)) {
                    isFormValid = false;
                }
            });

            // Check terms checkbox if present
            const termsCheckbox = this.form.querySelector('#terms');
            if (termsCheckbox && !termsCheckbox.checked) {
                isFormValid = false;
                // Show error on the checkbox
                const errorElement = termsCheckbox.parentElement.querySelector('.error-message');
                if (!errorElement) {
                    const errorSpan = document.createElement('span');
                    errorSpan.className = 'error-message';
                    errorSpan.textContent = 'You must accept the terms and conditions.';
                    termsCheckbox.parentElement.appendChild(errorSpan);
                }
                this.showMessage('Please accept the terms and conditions.', 'error');
            } else if (termsCheckbox) {
                // Clear error if checkbox is checked
                const errorElement = termsCheckbox.parentElement.querySelector('.error-message');
                if (errorElement) {
                    errorElement.remove();
                }
            }

            if (!isFormValid) {
                this.showMessage('Please correct the errors above.', 'error');
                return;
            }
        }
        
        this.setLoading(true);
        
        try {
            const formData = new FormData(this.form);

            const response = await fetch(this.form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });
            
            const data = await response.json();

            // Handle CSRF token mismatch (419 status)
            if (response.status === 419) {
                const message = data.message || 'CSRF token mismatch. Please refresh the page and try again.';
                this.showMessage(message, 'error');
                return;
            }

            if (response.ok) {
                // Handle different success scenarios
                if (this.options.successMessage) {
                    // For forms like forgot password that should show a message
                    this.showMessage(data.message || this.options.successMessage, 'success');

                    // Reset form if specified
                    if (this.options.resetOnSuccess !== false) {
                        this.form.reset();
                        // Update floating labels after reset
                        const inputs = this.form.querySelectorAll('.floating-input');
                        inputs.forEach(input => this.updateFloatingLabel(input));
                    }
                } else {
                    // For forms that should redirect (login, register)
                    this.showMessage('Success! Redirecting...', 'success');
                    setTimeout(() => {
                        window.location.href = data.redirect || this.options.successRedirect;
                    }, 1000);
                }
            } else {
                this.handleErrors(data);
            }
        } catch (error) {
            // Check if it's a CSRF token error
            if (error.response && error.response.status === 419) {
                this.showMessage('CSRF token mismatch. Please refresh the page and try again.', 'error');
            } else {
                this.showMessage(this.options.messages.networkError || 'Network error. Please try again.', 'error');
            }
        } finally {
            this.setLoading(false);
        }
    }
    
    handleErrors(data) {
        if (data.errors) {
            Object.keys(data.errors).forEach(field => {
                const input = this.form.querySelector(`[name="${field}"]`);
                if (input) {
                    this.setFieldValidation(input, false, data.errors[field][0]);
                }
            });
        }
        
        if (data.message) {
            this.showMessage(data.message, 'error');
        }
    }
    
    setLoading(loading) {
        const btnText = this.submitBtn.querySelector('.btn-text');
        const btnLoading = this.submitBtn.querySelector('.btn-loading');
        
        if (loading) {
            this.submitBtn.disabled = true;
            btnText.classList.add('hidden');
            btnLoading.classList.remove('hidden');
        } else {
            this.submitBtn.disabled = false;
            btnText.classList.remove('hidden');
            btnLoading.classList.add('hidden');
        }
    }
    
    showMessage(message, type) {
        if (this.messagesContainer) {
            this.messagesContainer.innerHTML = `<div class="${type}">${message}</div>`;
        }
        
        // Also show toast if available
        if (typeof showToast === 'function') {
            showToast(message, type);
        }
    }
}

// Password Strength Checker
class PasswordStrength {
    constructor(passwordInputId, strengthContainerId) {
        this.passwordInput = document.getElementById(passwordInputId);
        this.strengthContainer = document.getElementById(strengthContainerId);
        
        if (this.passwordInput && this.strengthContainer) {
            this.init();
        }
    }
    
    init() {
        this.createStrengthIndicator();
        this.passwordInput.addEventListener('input', () => {
            this.updateStrength();
        });
    }
    
    createStrengthIndicator() {
        this.strengthContainer.innerHTML = `
            <div class="strength-bar">
                <div class="strength-fill"></div>
            </div>
            <div class="strength-text"></div>
        `;
    }
    
    updateStrength() {
        const password = this.passwordInput.value;
        const strength = this.calculateStrength(password);
        
        this.strengthContainer.className = `password-strength ${strength.level}`;
        this.strengthContainer.querySelector('.strength-text').textContent = strength.text;
    }
    
    calculateStrength(password) {
        let score = 0;
        let feedback = [];

        if (password.length >= 8) score += 1;
        else feedback.push('at least 8 characters');

        if (/[a-z]/.test(password)) score += 1;
        else feedback.push('lowercase letters');

        if (/[A-Z]/.test(password)) score += 1;
        else feedback.push('uppercase letters');

        if (/\d/.test(password)) score += 1;
        else feedback.push('numbers');

        if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score += 1;
        else feedback.push('special characters');

        const levels = ['weak', 'weak', 'fair', 'good', 'strong'];
        const texts = [
            'Very weak',
            'Weak',
            'Fair',
            'Good',
            'Strong'
        ];

        // Ensure score doesn't exceed array bounds
        const levelIndex = Math.min(score, levels.length - 1);
        const textIndex = Math.min(score, texts.length - 1);

        return {
            level: levels[levelIndex],
            text: score === 5 ? texts[4] : `${texts[textIndex]} - Add ${feedback.slice(0, 2).join(', ')}`,
            score
        };
    }
}

// Initialize floating labels on page load
document.addEventListener('DOMContentLoaded', function() {
    // Initialize floating labels for any existing inputs
    const floatingInputs = document.querySelectorAll('.floating-input');
    floatingInputs.forEach(input => {
        if (input.value.trim() !== '') {
            input.classList.add('has-value');
            const label = input.parentElement.querySelector('.floating-label');
            if (label) label.classList.add('focus');
        }
    });
});
