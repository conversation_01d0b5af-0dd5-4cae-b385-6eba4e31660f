# ImageService Documentation

The ImageService is a comprehensive global service for handling image uploads, optimization, sanitization, virus scanning, and WebP conversion in your Laravel application.

## Features

- **Image Validation**: Validates file types, sizes, dimensions, and content
- **Virus Scanning**: Scans uploaded images for malware and viruses
- **Image Optimization**: Optimizes images for web delivery with configurable quality settings
- **WebP Conversion**: Automatically converts images to WebP format for better performance
- **Size Variants**: Creates multiple size variants (thumbnails, medium, large, etc.)
- **Security**: Sanitizes filenames and removes EXIF data
- **Logging**: Comprehensive logging of all image processing operations

## Installation

The ImageService is automatically registered as a singleton in Laravel's service container. No additional installation steps are required.

## Configuration

All configuration options are available in `config/image.php`. You can publish the configuration file using:

```bash
php artisan vendor:publish --tag=image-config
```

## Usage Examples

### 1. Basic Image Upload (Controller)

```php
<?php

namespace App\Http\Controllers;

use App\Services\ImageService;
use Illuminate\Http\Request;

class ImageUploadController extends Controller
{
    public function __construct(
        private ImageService $imageService
    ) {}
    
    public function upload(Request $request)
    {
        $request->validate([
            'image' => 'required|image|max:10240', // 10MB max
        ]);
        
        $result = $this->imageService->processUploadedImage(
            $request->file('image'),
            [
                'subdirectory' => 'user-uploads',
                'create_variants' => true,
                'create_webp' => true
            ]
        );
        
        if ($result['success']) {
            return response()->json([
                'success' => true,
                'message' => 'Image uploaded successfully',
                'data' => [
                    'original_path' => $result['original_path'],
                    'webp_path' => $result['webp_path'],
                    'variants' => $result['variants'],
                    'url' => $this->imageService->getImageUrl($result['original_path'])
                ]
            ]);
        }
        
        return response()->json([
            'success' => false,
            'message' => 'Image upload failed',
            'errors' => $result['errors']
        ], 422);
    }
}
```

### 2. Using Facade

```php
<?php

namespace App\Http\Controllers;

use App\Facades\ImageService;
use Illuminate\Http\Request;

class ProfileController extends Controller
{
    public function updateAvatar(Request $request)
    {
        $request->validate([
            'avatar' => 'required|image|max:5120', // 5MB max
        ]);
        
        // Quick upload with basic processing
        $result = ImageService::quickUpload(
            $request->file('avatar'),
            'avatars'
        );
        
        if ($result['success']) {
            // Update user avatar path in database
            auth()->user()->update([
                'avatar_path' => $result['original_path']
            ]);
            
            return redirect()->back()->with('success', 'Avatar updated successfully');
        }
        
        return redirect()->back()->withErrors($result['errors']);
    }
}
```

### 3. Using Helper Functions

```php
<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class GalleryController extends Controller
{
    public function store(Request $request)
    {
        $request->validate([
            'images.*' => 'required|image|max:10240',
        ]);
        
        $uploadedImages = [];
        
        foreach ($request->file('images') as $image) {
            // Use helper function for full upload with variants
            $result = full_image_upload($image, 'gallery');
            
            if ($result['success']) {
                $uploadedImages[] = [
                    'original_path' => $result['original_path'],
                    'webp_path' => $result['webp_path'],
                    'variants' => $result['variants'],
                    'url' => get_image_url($result['original_path'])
                ];
            }
        }
        
        return response()->json([
            'success' => true,
            'uploaded_images' => $uploadedImages
        ]);
    }
}
```

### 4. Processing Existing Images

```php
<?php

namespace App\Console\Commands;

use App\Services\ImageService;
use Illuminate\Console\Command;

class OptimizeExistingImages extends Command
{
    protected $signature = 'images:optimize {path}';
    protected $description = 'Optimize existing images in a directory';
    
    public function __construct(
        private ImageService $imageService
    ) {
        parent::__construct();
    }
    
    public function handle()
    {
        $path = $this->argument('path');
        $images = glob($path . '/*.{jpg,jpeg,png,gif,webp}', GLOB_BRACE);
        
        $this->info("Found " . count($images) . " images to optimize");
        
        foreach ($images as $imagePath) {
            $this->info("Processing: " . basename($imagePath));
            
            $result = $this->imageService->processImageFromPath($imagePath, [
                'create_variants' => true,
                'create_webp' => true
            ]);
            
            if ($result['success']) {
                $this->info("✓ Optimized successfully");
            } else {
                $this->error("✗ Failed: " . implode(', ', $result['errors']));
            }
        }
        
        $this->info("Image optimization completed");
    }
}
```

### 5. Custom Image Processing

```php
<?php

namespace App\Services;

use App\Services\ImageService;

class ProductImageService
{
    public function __construct(
        private ImageService $imageService
    ) {}
    
    public function processProductImage($imageFile, $productId)
    {
        // Custom processing for product images
        $result = $this->imageService->processUploadedImage($imageFile, [
            'subdirectory' => "products/{$productId}",
            'create_variants' => true,
            'create_webp' => true,
            'sizes' => [
                'thumbnail' => ['width' => 150, 'height' => 150, 'crop' => true],
                'medium' => ['width' => 400, 'height' => 400, 'crop' => false],
                'large' => ['width' => 800, 'height' => 800, 'crop' => false],
            ]
        ]);
        
        if ($result['success']) {
            // Add watermark to large variant if needed
            $largeVariant = $result['variants']['large'] ?? null;
            if ($largeVariant && config('image.watermark.enabled')) {
                $this->addWatermark($largeVariant);
            }
        }
        
        return $result;
    }
    
    private function addWatermark($imagePath)
    {
        // Custom watermark logic here
        // This could be added to the ImageService as well
    }
}
```

## Available Methods

### Main Processing Methods

- `processUploadedImage(UploadedFile $file, array $options = [])` - Full processing pipeline
- `processImageFromPath(string $imagePath, array $options = [])` - Process existing image
- `quickUpload(UploadedFile $file, string $subdirectory = 'uploads')` - Quick upload
- `fullUpload(UploadedFile $file, string $subdirectory = 'uploads')` - Full upload with variants

### Optimization Methods

- `optimizeImage(string $imagePath, array $options = [])` - Optimize image
- `resizeImage(string $imagePath, int $width, int $height, array $options = [])` - Resize image
- `createSizeVariants(string $originalPath, string $baseName, array $sizes = [])` - Create variants
- `convertToWebP(string $imagePath, int $quality = null)` - Convert to WebP

### Security Methods

- `validateImageFile(UploadedFile $file)` - Validate uploaded file
- `scanForViruses(string $filePath)` - Scan for viruses
- `sanitizeFilename(string $filename)` - Sanitize filename

### Utility Methods

- `getImageUrl(string $path)` - Get public URL
- `deleteImage(string $path, bool $deleteVariants = true)` - Delete image
- `getImageInfo(string $path)` - Get image information

## Configuration Options

Key configuration options in `config/image.php`:

```php
return [
    'max_file_size' => 10 * 1024 * 1024, // 10MB
    'allowed_mimes' => ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
    
    'enable_optimization' => true,
    'enable_webp_conversion' => true,
    
    'sizes' => [
        'thumbnail' => ['width' => 150, 'height' => 150, 'crop' => true],
        'medium' => ['width' => 400, 'height' => 400, 'crop' => false],
        'large' => ['width' => 800, 'height' => 800, 'crop' => false],
    ],
    
    'virus_scan' => [
        'enabled' => false,
        'method' => 'basic', // 'clamav', 'windows_defender', 'custom', 'basic'
    ],
    
    'security' => [
        'validate_image_content' => true,
        'check_file_signature' => true,
        'sanitize_filename' => true,
        'remove_exif_data' => true,
    ],
];
```

## Error Handling

The ImageService returns consistent response arrays:

```php
// Success response
[
    'success' => true,
    'original_path' => '/path/to/image.jpg',
    'webp_path' => '/path/to/image.webp',
    'variants' => ['thumbnail' => '/path/to/image_thumbnail.jpg'],
    'file_info' => [...],
    'processing_time_ms' => 150.25
]

// Error response
[
    'success' => false,
    'errors' => ['File size exceeds maximum allowed size'],
    'file_info' => [...]
]
```

## Performance Considerations

- Configure memory limits in `config/image.php`
- Use size variants instead of resizing on-the-fly
- Enable WebP conversion for better compression
- Consider using queues for batch processing
- Monitor disk space for image storage

## Security Best Practices

- Always validate file uploads
- Enable virus scanning in production
- Use sanitized filenames
- Remove EXIF data to prevent information leakage
- Store uploads outside the web root when possible
- Implement rate limiting for upload endpoints
