<?php

namespace Tests\Feature\Admin;

use App\Models\Order;
use App\Models\User;
use App\Models\Currency;
use App\Models\Product;
use App\Models\OrderItem;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class OrderManagementTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create admin user
        $this->admin = User::factory()->create([
            'role' => 'admin',
            'email_verified_at' => now(),
        ]);
        
        // Create currency
        $this->currency = Currency::factory()->create([
            'code' => 'USD',
            'symbol' => '$',
        ]);
    }

    /** @test */
    public function admin_can_view_orders_index()
    {
        // Create some orders
        $orders = Order::factory()->count(3)->create([
            'currency_id' => $this->currency->id,
        ]);

        $response = $this->actingAs($this->admin)
                         ->get(route('admin.orders.index'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.orders.index');
        $response->assertViewHas('orders');
        $response->assertViewHas('statusCounts');
        $response->assertViewHas('paymentStatusCounts');
    }

    /** @test */
    public function admin_can_view_specific_order()
    {
        $order = Order::factory()->create([
            'currency_id' => $this->currency->id,
        ]);

        $response = $this->actingAs($this->admin)
                         ->get(route('admin.orders.show', $order->uuid));

        $response->assertStatus(200);
        $response->assertViewIs('admin.orders.show');
        $response->assertViewHas('order');
    }

    /** @test */
    public function admin_cannot_view_deleted_order()
    {
        $order = Order::factory()->create([
            'currency_id' => $this->currency->id,
            'is_deleted' => true,
        ]);

        $response = $this->actingAs($this->admin)
                         ->get(route('admin.orders.show', $order->uuid));

        $response->assertStatus(404);
    }

    /** @test */
    public function admin_can_update_order_status()
    {
        $order = Order::factory()->create([
            'currency_id' => $this->currency->id,
            'status' => 'pending',
        ]);

        $response = $this->actingAs($this->admin)
                         ->patch(route('admin.orders.update', $order->uuid), [
                             'status' => 'processing',
                             'payment_status' => 'paid',
                             'notes' => 'Order is being processed',
                         ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $order->refresh();
        $this->assertEquals('processing', $order->status);
        $this->assertEquals('paid', $order->payment_status);
        $this->assertEquals('Order is being processed', $order->notes);
    }

    /** @test */
    public function admin_can_update_order_status_via_ajax()
    {
        $order = Order::factory()->create([
            'currency_id' => $this->currency->id,
            'status' => 'pending',
        ]);

        $response = $this->actingAs($this->admin)
                         ->patchJson(route('admin.orders.status', $order->uuid), [
                             'status' => 'shipped',
                         ]);

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'message' => 'Order status updated successfully.',
            'status' => 'shipped',
        ]);

        $order->refresh();
        $this->assertEquals('shipped', $order->status);
        $this->assertNotNull($order->shipped_at);
    }

    /** @test */
    public function admin_can_filter_orders_by_status()
    {
        Order::factory()->create([
            'currency_id' => $this->currency->id,
            'status' => 'pending',
        ]);
        
        Order::factory()->create([
            'currency_id' => $this->currency->id,
            'status' => 'shipped',
        ]);

        $response = $this->actingAs($this->admin)
                         ->get(route('admin.orders.index', ['status' => 'pending']));

        $response->assertStatus(200);
        $response->assertViewHas('orders');
    }

    /** @test */
    public function admin_can_search_orders()
    {
        $order = Order::factory()->create([
            'currency_id' => $this->currency->id,
            'order_number' => 'ORD-12345',
            'email' => '<EMAIL>',
        ]);

        $response = $this->actingAs($this->admin)
                         ->get(route('admin.orders.index', ['search' => 'ORD-12345']));

        $response->assertStatus(200);
        $response->assertViewHas('orders');
    }

    /** @test */
    public function order_status_validation_works()
    {
        $order = Order::factory()->create([
            'currency_id' => $this->currency->id,
        ]);

        $response = $this->actingAs($this->admin)
                         ->patch(route('admin.orders.update', $order->uuid), [
                             'status' => 'invalid_status',
                         ]);

        $response->assertSessionHasErrors('status');
    }

    /** @test */
    public function orders_exclude_soft_deleted_by_default()
    {
        // Create regular order
        $activeOrder = Order::factory()->create([
            'currency_id' => $this->currency->id,
            'is_deleted' => false,
        ]);

        // Create soft deleted order
        $deletedOrder = Order::factory()->create([
            'currency_id' => $this->currency->id,
            'is_deleted' => true,
        ]);

        $response = $this->actingAs($this->admin)
                         ->get(route('admin.orders.index'));

        $response->assertStatus(200);
        
        // Check that only active order is shown
        $orders = $response->viewData('orders');
        $this->assertCount(1, $orders);
        $this->assertEquals($activeOrder->id, $orders->first()->id);
    }
}
