<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('project_applications', function (Blueprint $table) {
            $table->decimal('estimated_cost', 10, 2)->nullable()->after('admin_notes');
            $table->integer('estimated_hours')->nullable()->after('estimated_cost');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('project_applications', function (Blueprint $table) {
            $table->dropColumn(['estimated_cost', 'estimated_hours']);
        });
    }
};
