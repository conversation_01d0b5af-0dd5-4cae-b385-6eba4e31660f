/* Professional Admin UI Components */

/* Custom Properties */
:root {
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;
    
    --secondary-50: #f8fafc;
    --secondary-100: #f1f5f9;
    --secondary-200: #e2e8f0;
    --secondary-300: #cbd5e1;
    --secondary-400: #94a3b8;
    --secondary-500: #64748b;
    --secondary-600: #475569;
    --secondary-700: #334155;
    --secondary-800: #1e293b;
    --secondary-900: #0f172a;
    
    --success-500: #10b981;
    --success-600: #059669;
    --warning-500: #f59e0b;
    --warning-600: #d97706;
    --danger-500: #ef4444;
    --danger-600: #dc2626;
    
    --shadow-soft: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-large: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Dashboard Components */
.dashboard-card {
    @apply bg-white rounded-xl p-6 border border-neutral-100;
    box-shadow: var(--shadow-soft);
}

.dashboard-stat {
    @apply flex items-center justify-between;
}

.dashboard-stat-content h3 {
    @apply text-2xl font-bold text-gray-900;
}

.dashboard-stat-content p {
    @apply text-sm text-gray-600;
}

.dashboard-stat-icon {
    @apply w-12 h-12 rounded-lg flex items-center justify-center;
}

/* Activity Items */
.activity-item {
    @apply flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200;
}

.activity-icon {
    @apply w-8 h-8 rounded-full flex items-center justify-center;
}

.activity-content h4 {
    @apply text-sm font-medium text-gray-900;
}

.activity-content p {
    @apply text-xs text-gray-500;
}

/* Quick Actions */
.quick-action {
    @apply flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200 text-center;
}

.quick-action-icon {
    @apply w-8 h-8 rounded-lg flex items-center justify-center mb-2;
}

.quick-action h3 {
    @apply text-sm font-medium text-gray-900 mb-1;
}

.quick-action p {
    @apply text-xs text-gray-500;
}

/* Navigation Items */
.nav-item {
    @apply flex items-center space-x-3 px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200;
    @apply text-neutral-700 hover:bg-primary-50 hover:text-primary-700;
}

.nav-item-active {
    @apply bg-primary-100 text-primary-800 border-r-2 border-primary-600;
}

.nav-item svg {
    @apply flex-shrink-0;
}

/* Form Components */
.form-group {
    @apply space-y-2;
}

.form-label {
    @apply block text-sm font-medium text-gray-700;
}

.form-label-required::after {
    content: " *";
    @apply text-red-500;
}

.form-input {
    @apply w-full px-4 py-2 border border-neutral-300 rounded-lg;
    @apply focus:ring-2 focus:ring-primary-500 focus:border-transparent;
    @apply transition-colors duration-200;
}

.form-input:invalid,
.form-input.error {
    @apply border-red-500 focus:ring-red-500;
}

.form-select {
    @apply w-full px-4 py-2 border border-neutral-300 rounded-lg;
    @apply focus:ring-2 focus:ring-primary-500 focus:border-transparent;
    @apply transition-colors duration-200;
}

.form-textarea {
    @apply w-full px-4 py-2 border border-neutral-300 rounded-lg;
    @apply focus:ring-2 focus:ring-primary-500 focus:border-transparent;
    @apply transition-colors duration-200 resize-vertical;
}

.form-checkbox,
.form-radio {
    @apply h-4 w-4 text-primary-600 focus:ring-primary-500 border-neutral-300 rounded;
}

.form-help {
    @apply text-xs text-gray-500;
}

.form-error {
    @apply text-sm text-red-600;
}

/* Button Variants */
.btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg;
    @apply transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
}

.btn-secondary {
    @apply bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500;
}

.btn-success {
    @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
}

.btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
}

.btn-warning {
    @apply bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500;
}

.btn-outline {
    @apply border border-neutral-300 text-neutral-700 hover:bg-neutral-50 focus:ring-neutral-500;
}

.btn-sm {
    @apply px-3 py-1.5 text-xs;
}

.btn-lg {
    @apply px-6 py-3 text-base;
}

/* Status Badges */
.badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-success {
    @apply bg-green-100 text-green-800;
}

.badge-warning {
    @apply bg-yellow-100 text-yellow-800;
}

.badge-danger {
    @apply bg-red-100 text-red-800;
}

.badge-info {
    @apply bg-blue-100 text-blue-800;
}

.badge-neutral {
    @apply bg-gray-100 text-gray-800;
}

/* Table Components */
.table-container {
    @apply bg-white rounded-xl shadow-soft border border-neutral-100 overflow-hidden;
}

.table-header {
    @apply px-6 py-4 border-b border-neutral-200;
}

.table-title {
    @apply text-lg font-semibold text-gray-900;
}

.table-subtitle {
    @apply text-sm text-gray-600;
}

.table-actions {
    @apply flex items-center space-x-2;
}

/* Card Components */
.card {
    @apply bg-white rounded-xl border border-neutral-100;
    box-shadow: var(--shadow-soft);
}

.card-header {
    @apply px-6 py-4 border-b border-neutral-200;
}

.card-body {
    @apply p-6;
}

.card-footer {
    @apply px-6 py-4 bg-gray-50 border-t border-neutral-200;
}

/* Alert Components */
.alert {
    @apply rounded-lg border p-4;
}

.alert-success {
    @apply bg-green-50 border-green-200 text-green-800;
}

.alert-warning {
    @apply bg-yellow-50 border-yellow-200 text-yellow-800;
}

.alert-danger {
    @apply bg-red-50 border-red-200 text-red-800;
}

.alert-info {
    @apply bg-blue-50 border-blue-200 text-blue-800;
}

/* Loading States */
.loading {
    @apply opacity-50 pointer-events-none;
}

.spinner {
    @apply animate-spin h-5 w-5 text-primary-600;
}

/* Utility Classes */
.shadow-soft {
    box-shadow: var(--shadow-soft);
}

.shadow-medium {
    box-shadow: var(--shadow-medium);
}

.shadow-large {
    box-shadow: var(--shadow-large);
}

.text-gradient {
    background: linear-gradient(135deg, var(--primary-600), var(--secondary-600));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Responsive Utilities */
@media (max-width: 640px) {
    .dashboard-card {
        @apply p-4;
    }
    
    .card-body {
        @apply p-4;
    }
    
    .table-container {
        @apply rounded-lg;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { 
        opacity: 0;
        transform: translateY(10px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}
