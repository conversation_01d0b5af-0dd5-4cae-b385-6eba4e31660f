<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if column doesn't exist before adding
        if (!Schema::hasColumn('orders', 'is_deleted')) {
            Schema::table('orders', function (Blueprint $table) {
                $table->boolean('is_deleted')->default(false)->after('delivered_at');

                // Add index for soft delete queries
                $table->index(['user_id', 'is_deleted']);
                $table->index(['status', 'is_deleted']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropIndex(['user_id', 'is_deleted']);
            $table->dropIndex(['status', 'is_deleted']);
            $table->dropColumn('is_deleted');
        });
    }
};
