@extends('emails.layout')

@section('title', 'Payment Received - ' . $order->order_number)

@section('content')
<div class="greeting">
    Payment Confirmed, {{ $customer_name }}!
</div>

<p class="content-text">
    Great news! We've successfully received your payment for order <strong>#{{ $order->order_number }}</strong>.
</p>

<!-- Payment Confirmation Box -->
<div style="background-color: #f0fff4; border: 1px solid #9ae6b4; border-radius: 8px; padding: 20px; margin: 20px 0;">
    <div style="text-align: center;">
        <div style="font-size: 48px; margin-bottom: 10px;">✅</div>
        <h3 style="margin: 0 0 10px 0; color: #2f855a; font-size: 20px;">Payment Successfully Processed</h3>
        <p style="margin: 0; color: #38a169; font-size: 16px;">
            Amount: <strong>{{ $order->formatted_total }}</strong>
        </p>
    </div>
</div>

<!-- Payment Details -->
@if($payment)
<div style="background-color: #f7fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px; margin: 20px 0;">
    <h3 style="margin: 0 0 15px 0; color: #2d3748; font-size: 18px;">Payment Details</h3>
    
    <table style="width: 100%; border-collapse: collapse;">
        <tr>
            <td style="padding: 8px 0; color: #4a5568; font-weight: 600;">Payment Method:</td>
            <td style="padding: 8px 0; color: #2d3748; text-align: right; text-transform: capitalize;">{{ $payment->payment_method ?? 'Credit Card' }}</td>
        </tr>
        <tr>
            <td style="padding: 8px 0; color: #4a5568; font-weight: 600;">Transaction ID:</td>
            <td style="padding: 8px 0; color: #2d3748; text-align: right; font-family: monospace; font-size: 14px;">{{ $payment->transaction_id ?? 'N/A' }}</td>
        </tr>
        <tr>
            <td style="padding: 8px 0; color: #4a5568; font-weight: 600;">Payment Date:</td>
            <td style="padding: 8px 0; color: #2d3748; text-align: right;">{{ $payment->created_at ? $payment->created_at->format('M d, Y \a\t g:i A') : $order->updated_at->format('M d, Y \a\t g:i A') }}</td>
        </tr>
        <tr style="border-top: 1px solid #e2e8f0;">
            <td style="padding: 12px 0 8px 0; color: #2d3748; font-weight: 700; font-size: 16px;">Amount Paid:</td>
            <td style="padding: 12px 0 8px 0; color: #2d3748; font-weight: 700; font-size: 16px; text-align: right;">{{ $order->formatted_total }}</td>
        </tr>
    </table>
</div>
@endif

<!-- Order Summary -->
<div style="background-color: #f7fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px; margin: 20px 0;">
    <h3 style="margin: 0 0 15px 0; color: #2d3748; font-size: 18px;">Order Summary</h3>
    
    <table style="width: 100%; border-collapse: collapse;">
        <tr>
            <td style="padding: 8px 0; color: #4a5568; font-weight: 600;">Order Number:</td>
            <td style="padding: 8px 0; color: #2d3748; text-align: right;">#{{ $order->order_number }}</td>
        </tr>
        <tr>
            <td style="padding: 8px 0; color: #4a5568; font-weight: 600;">Order Date:</td>
            <td style="padding: 8px 0; color: #2d3748; text-align: right;">{{ $order->created_at->format('M d, Y') }}</td>
        </tr>
        <tr>
            <td style="padding: 8px 0; color: #4a5568; font-weight: 600;">Status:</td>
            <td style="padding: 8px 0; text-align: right;">
                <span style="background-color: #3182ce; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; text-transform: uppercase;">
                    {{ ucfirst($order->status) }}
                </span>
            </td>
        </tr>
    </table>
</div>

<!-- Order Items Summary -->
<h3 style="color: #2d3748; margin: 30px 0 15px 0;">Items in Your Order</h3>

@foreach($order->items as $item)
<div style="border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; margin-bottom: 10px; background-color: #ffffff;">
    <table style="width: 100%;">
        <tr>
            <td style="width: 70%;">
                <h4 style="margin: 0 0 5px 0; color: #2d3748; font-size: 16px;">{{ $item->product_name }}</h4>
                @if($item->variant_name)
                <p style="margin: 0 0 5px 0; color: #718096; font-size: 14px;">{{ $item->variant_name }}</p>
                @endif
                <p style="margin: 0; color: #4a5568; font-size: 14px;">Quantity: {{ $item->quantity }}</p>
            </td>
            <td style="width: 30%; text-align: right; vertical-align: top;">
                <p style="margin: 0; color: #2d3748; font-weight: 600; font-size: 16px;">{{ $item->formatted_total_price }}</p>
            </td>
        </tr>
    </table>
</div>
@endforeach

<!-- Payment Breakdown -->
<div style="background-color: #f7fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px; margin: 20px 0;">
    <h3 style="margin: 0 0 15px 0; color: #2d3748; font-size: 18px;">Payment Breakdown</h3>
    
    <table style="width: 100%; border-collapse: collapse;">
        <tr>
            <td style="padding: 5px 0; color: #4a5568;">Subtotal:</td>
            <td style="padding: 5px 0; color: #2d3748; text-align: right;">{{ $order->formatted_subtotal }}</td>
        </tr>
        
        @if($order->discount_amount > 0)
        <tr>
            <td style="padding: 5px 0; color: #38a169;">
                Discount @if($order->coupon_code)({{ $order->coupon_code }})@endif:
            </td>
            <td style="padding: 5px 0; color: #38a169; text-align: right;">-{{ $order->formatted_discount_amount }}</td>
        </tr>
        @endif
        
        <tr>
            <td style="padding: 5px 0; color: #4a5568;">Shipping:</td>
            <td style="padding: 5px 0; color: #2d3748; text-align: right;">{{ $order->formatted_shipping_amount }}</td>
        </tr>
        
        <tr>
            <td style="padding: 5px 0; color: #4a5568;">Tax:</td>
            <td style="padding: 5px 0; color: #2d3748; text-align: right;">{{ $order->formatted_tax_amount }}</td>
        </tr>
        
        <tr style="border-top: 2px solid #2d3748;">
            <td style="padding: 10px 0 5px 0; color: #2d3748; font-weight: 700; font-size: 18px;">Total Paid:</td>
            <td style="padding: 10px 0 5px 0; color: #2d3748; font-weight: 700; font-size: 18px; text-align: right;">{{ $order->formatted_total }}</td>
        </tr>
    </table>
</div>

<!-- Next Steps -->
<div class="info-box">
    <p><strong>What Happens Next?</strong></p>
    <p>
        Now that your payment has been processed, we'll begin preparing your order for shipment. You can expect:
    </p>
    <ul style="margin: 10px 0; padding-left: 20px; color: #2c5282;">
        <li>Order processing within 1-2 business days</li>
        <li>Shipping notification with tracking information</li>
        <li>Delivery within 3-5 business days (standard shipping)</li>
    </ul>
</div>

<!-- Action Buttons -->
<div class="button-container">
    @if($order->user_id)
    <a href="{{ route('orders.show', $order->uuid) }}" class="btn">
        Track Your Order
    </a>
    @endif
    
    <a href="{{ route('shop.index') }}" class="btn btn-secondary" style="margin-left: 10px;">
        Continue Shopping
    </a>
</div>

<!-- Receipt Information -->
<div style="background-color: #fffbeb; border-left: 4px solid #f59e0b; padding: 20px; margin: 20px 0; border-radius: 0 8px 8px 0;">
    <p style="margin: 0; color: #92400e; font-size: 14px;">
        <strong>Receipt:</strong> This email serves as your receipt for this transaction. Please keep it for your records.
    </p>
</div>

<p class="content-text">
    Thank you for your business! If you have any questions about your payment or order, please contact our support team.
</p>

<p class="content-text">
    Best regards,<br>
    The {{ $company_name }} Team
</p>
@endsection
