<?php

namespace Tests\Unit;

use App\Models\Job;
use App\Models\JobApplication;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class JobTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test job creation with required fields.
     */
    public function test_job_can_be_created_with_required_fields(): void
    {
        $job = Job::create([
            'title' => 'Software Developer',
            'description' => 'We are looking for a skilled software developer.',
            'requirements' => 'Bachelor degree in Computer Science',
            'responsibilities' => 'Develop and maintain software applications',
            'location' => 'Cape Town',
            'employment_type' => 'full-time',
            'experience_level' => 'mid',
            'department' => 'Engineering',
            'salary_period' => 'monthly',
        ]);

        $this->assertDatabaseHas('career_jobs', [
            'title' => 'Software Developer',
            'location' => 'Cape Town',
            'employment_type' => 'full-time',
        ]);

        $this->assertNotNull($job->uuid);
        $this->assertNotNull($job->slug);
    }

    /**
     * Test job slug generation.
     */
    public function test_job_slug_is_generated_automatically(): void
    {
        $job = Job::create([
            'title' => 'Senior Full Stack Developer',
            'description' => 'Test description',
            'requirements' => 'Test requirements',
            'responsibilities' => 'Test responsibilities',
            'location' => 'Johannesburg',
            'employment_type' => 'full-time',
            'experience_level' => 'senior',
            'department' => 'Engineering',
            'salary_period' => 'monthly',
        ]);

        $this->assertEquals('senior-full-stack-developer', $job->slug);
    }

    /**
     * Test job UUID generation.
     */
    public function test_job_uuid_is_generated_automatically(): void
    {
        $job = Job::create([
            'title' => 'Test Job',
            'description' => 'Test description',
            'requirements' => 'Test requirements',
            'responsibilities' => 'Test responsibilities',
            'location' => 'Test Location',
            'employment_type' => 'full-time',
            'experience_level' => 'entry',
            'department' => 'Test',
            'salary_period' => 'monthly',
        ]);

        $this->assertNotNull($job->uuid);
        $this->assertIsString($job->uuid);
        $this->assertEquals(36, strlen($job->uuid)); // UUID length
    }

    /**
     * Test job route key name.
     */
    public function test_job_route_key_name_is_slug(): void
    {
        $job = new Job();
        $this->assertEquals('slug', $job->getRouteKeyName());
    }

    /**
     * Test active scope.
     */
    public function test_active_scope_returns_only_active_jobs(): void
    {
        // Create active job
        $activeJob = Job::create([
            'title' => 'Active Job',
            'description' => 'Test description',
            'requirements' => 'Test requirements',
            'responsibilities' => 'Test responsibilities',
            'location' => 'Test Location',
            'employment_type' => 'full-time',
            'experience_level' => 'entry',
            'department' => 'Test',
            'salary_period' => 'monthly',
            'is_active' => true,
            'is_deleted' => false,
        ]);

        // Create inactive job
        Job::create([
            'title' => 'Inactive Job',
            'description' => 'Test description',
            'requirements' => 'Test requirements',
            'responsibilities' => 'Test responsibilities',
            'location' => 'Test Location',
            'employment_type' => 'full-time',
            'experience_level' => 'entry',
            'department' => 'Test',
            'salary_period' => 'monthly',
            'is_active' => false,
            'is_deleted' => false,
        ]);

        // Create deleted job
        Job::create([
            'title' => 'Deleted Job',
            'description' => 'Test description',
            'requirements' => 'Test requirements',
            'responsibilities' => 'Test responsibilities',
            'location' => 'Test Location',
            'employment_type' => 'full-time',
            'experience_level' => 'entry',
            'department' => 'Test',
            'salary_period' => 'monthly',
            'is_active' => true,
            'is_deleted' => true,
        ]);

        $activeJobs = Job::active()->get();

        $this->assertCount(1, $activeJobs);
        $this->assertEquals('Active Job', $activeJobs->first()->title);
    }

    /**
     * Test featured scope.
     */
    public function test_featured_scope_returns_only_featured_jobs(): void
    {
        // Create featured job
        Job::create([
            'title' => 'Featured Job',
            'description' => 'Test description',
            'requirements' => 'Test requirements',
            'responsibilities' => 'Test responsibilities',
            'location' => 'Test Location',
            'employment_type' => 'full-time',
            'experience_level' => 'entry',
            'department' => 'Test',
            'salary_period' => 'monthly',
            'is_featured' => true,
        ]);

        // Create non-featured job
        Job::create([
            'title' => 'Regular Job',
            'description' => 'Test description',
            'requirements' => 'Test requirements',
            'responsibilities' => 'Test responsibilities',
            'location' => 'Test Location',
            'employment_type' => 'full-time',
            'experience_level' => 'entry',
            'department' => 'Test',
            'salary_period' => 'monthly',
            'is_featured' => false,
        ]);

        $featuredJobs = Job::featured()->get();

        $this->assertCount(1, $featuredJobs);
        $this->assertEquals('Featured Job', $featuredJobs->first()->title);
    }

    /**
     * Test job applications relationship.
     */
    public function test_job_has_many_job_applications(): void
    {
        $job = Job::create([
            'title' => 'Test Job',
            'description' => 'Test description',
            'requirements' => 'Test requirements',
            'responsibilities' => 'Test responsibilities',
            'location' => 'Test Location',
            'employment_type' => 'full-time',
            'experience_level' => 'entry',
            'department' => 'Test',
            'salary_period' => 'monthly',
        ]);

        $application = JobApplication::create([
            'job_id' => $job->id,
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'phone' => '123456789',
            'country' => 'South Africa',
            'cover_letter' => 'Test cover letter',
            'experience_summary' => 'Test experience',
            'highest_qualification' => 'Bachelor Degree',
        ]);

        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Collection::class, $job->jobApplications);
        $this->assertCount(1, $job->jobApplications);
        $this->assertEquals($application->id, $job->jobApplications->first()->id);
    }

    /**
     * Test formatted salary attribute.
     */
    public function test_formatted_salary_attribute(): void
    {
        // Test with both min and max salary
        $job = Job::create([
            'title' => 'Test Job',
            'description' => 'Test description',
            'requirements' => 'Test requirements',
            'responsibilities' => 'Test responsibilities',
            'location' => 'Test Location',
            'employment_type' => 'full-time',
            'experience_level' => 'entry',
            'department' => 'Test',
            'salary_period' => 'monthly',
            'salary_min' => 25000,
            'salary_max' => 35000,
            'salary_currency' => 'ZAR',
        ]);

        $this->assertEquals('ZAR 25,000 - 35,000 Per Month', $job->formatted_salary);

        // Test with only min salary
        $job->salary_max = null;
        $this->assertEquals('ZAR 25,000+ Per Month', $job->formatted_salary);

        // Test with no salary
        $job->salary_min = null;
        $this->assertEquals('Competitive', $job->formatted_salary);
    }

    /**
     * Test employment type options.
     */
    public function test_employment_type_options(): void
    {
        $options = Job::getEmploymentTypeOptions();

        $this->assertIsArray($options);
        $this->assertArrayHasKey('full-time', $options);
        $this->assertArrayHasKey('part-time', $options);
        $this->assertArrayHasKey('contract', $options);
        $this->assertArrayHasKey('internship', $options);
        $this->assertArrayHasKey('freelance', $options);
    }

    /**
     * Test experience level options.
     */
    public function test_experience_level_options(): void
    {
        $options = Job::getExperienceLevelOptions();

        $this->assertIsArray($options);
        $this->assertArrayHasKey('entry', $options);
        $this->assertArrayHasKey('mid', $options);
        $this->assertArrayHasKey('senior', $options);
        $this->assertArrayHasKey('executive', $options);
    }

    /**
     * Test salary period options.
     */
    public function test_salary_period_options(): void
    {
        $options = Job::getSalaryPeriodOptions();

        $this->assertIsArray($options);
        $this->assertArrayHasKey('hourly', $options);
        $this->assertArrayHasKey('monthly', $options);
        $this->assertArrayHasKey('annually', $options);
    }
}
