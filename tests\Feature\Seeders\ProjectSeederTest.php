<?php

namespace Tests\Feature\Seeders;

use App\Models\Project;
use App\Models\Service;
use App\Models\User;
use App\Models\UserAddress;
use App\Models\Role;
use Database\Seeders\ProjectSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ProjectSeederTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function project_seeder_creates_services()
    {
        $this->artisan('db:seed', ['--class' => 'Database\Seeders\ProjectSeeder']);

        $this->assertDatabaseCount('services', 6);
        
        $expectedServices = [
            'Web Development',
            'Mobile App Development',
            'E-commerce Solutions',
            'Digital Marketing',
            'UI/UX Design',
            'Brand Identity',
        ];

        foreach ($expectedServices as $serviceName) {
            $this->assertDatabaseHas('services', ['name' => $serviceName]);
        }
    }

    /** @test */
    public function project_seeder_creates_client_users()
    {
        // Create client role first
        Role::factory()->create(['name' => 'client']);

        $this->artisan('db:seed', ['--class' => 'Database\Seeders\ProjectSeeder']);

        $this->assertDatabaseCount('users', 5);
        
        $expectedEmails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
        ];

        foreach ($expectedEmails as $email) {
            $this->assertDatabaseHas('users', ['email' => $email]);
        }
    }

    /** @test */
    public function project_seeder_creates_user_addresses_with_company_info()
    {
        Role::factory()->create(['name' => 'client']);

        $this->artisan('db:seed', ['--class' => 'Database\Seeders\ProjectSeeder']);

        $this->assertDatabaseCount('user_addresses', 5);
        
        $expectedCompanies = [
            'TechStartup Inc.',
            'RetailPlus Solutions',
            'HealthCare App Co.',
            'Food Delivery Express',
            'Fashion Brand Studio',
        ];

        foreach ($expectedCompanies as $company) {
            $this->assertDatabaseHas('user_addresses', ['company' => $company]);
        }
    }

    /** @test */
    public function project_seeder_creates_projects_for_each_service()
    {
        Role::factory()->create(['name' => 'client']);

        $this->artisan('db:seed', ['--class' => 'Database\Seeders\ProjectSeeder']);

        // Should create multiple projects (at least 2-3 per service)
        $this->assertGreaterThan(10, Project::count());

        // Check that projects have proper relationships
        $projects = Project::with(['client', 'service'])->get();
        
        foreach ($projects as $project) {
            $this->assertNotNull($project->title);
            $this->assertNotNull($project->description);
            $this->assertNotNull($project->client_id);
            $this->assertNotNull($project->client_name);
            $this->assertInstanceOf(User::class, $project->client);
        }
    }

    /** @test */
    public function project_seeder_creates_projects_with_realistic_data()
    {
        Role::factory()->create(['name' => 'client']);

        $this->artisan('db:seed', ['--class' => 'Database\Seeders\ProjectSeeder']);

        $projects = Project::all();

        foreach ($projects as $project) {
            // Check required fields
            $this->assertNotEmpty($project->title);
            $this->assertNotEmpty($project->description);
            $this->assertNotEmpty($project->slug);
            $this->assertNotNull($project->status);
            $this->assertNotNull($project->priority);
            
            // Check valid status values
            $this->assertContains($project->status, [
                'planning', 'in_progress', 'review', 'completed', 'on_hold', 'cancelled'
            ]);
            
            // Check valid priority values
            $this->assertContains($project->priority, ['low', 'medium', 'high']);
            
            // Check boolean fields
            $this->assertIsBool($project->is_published);
            $this->assertIsBool($project->is_featured);
            $this->assertIsBool($project->is_deleted);
            
            // Check that is_deleted is false (not soft deleted)
            $this->assertFalse($project->is_deleted);
        }
    }

    /** @test */
    public function project_seeder_creates_projects_with_seo_data()
    {
        Role::factory()->create(['name' => 'client']);

        $this->artisan('db:seed', ['--class' => 'Database\Seeders\ProjectSeeder']);

        $projects = Project::all();

        foreach ($projects as $project) {
            $this->assertNotEmpty($project->meta_title);
            $this->assertNotEmpty($project->meta_description);
            $this->assertNotEmpty($project->meta_keywords);
            
            // Check that meta title includes project title
            $this->assertStringContainsString($project->title, $project->meta_title);
        }
    }

    /** @test */
    public function project_seeder_creates_projects_with_financial_data()
    {
        Role::factory()->create(['name' => 'client']);

        $this->artisan('db:seed', ['--class' => 'Database\Seeders\ProjectSeeder']);

        $projects = Project::all();

        foreach ($projects as $project) {
            if ($project->estimated_hours) {
                $this->assertIsNumeric($project->estimated_hours);
                $this->assertGreaterThan(0, $project->estimated_hours);
            }
            
            if ($project->actual_hours) {
                $this->assertIsNumeric($project->actual_hours);
                $this->assertGreaterThan(0, $project->actual_hours);
            }
            
            if ($project->hourly_rate) {
                $this->assertIsNumeric($project->hourly_rate);
                $this->assertGreaterThan(0, $project->hourly_rate);
            }
            
            if ($project->total_amount) {
                $this->assertIsNumeric($project->total_amount);
                $this->assertGreaterThan(0, $project->total_amount);
            }
            
            // Check currency code
            $this->assertEquals('ZAR', $project->currency_code);
        }
    }

    /** @test */
    public function project_seeder_creates_projects_with_dates()
    {
        Role::factory()->create(['name' => 'client']);

        $this->artisan('db:seed', ['--class' => 'Database\Seeders\ProjectSeeder']);

        $projects = Project::all();

        foreach ($projects as $project) {
            if ($project->start_date) {
                $this->assertInstanceOf(\Carbon\Carbon::class, $project->start_date);
                $this->assertLessThanOrEqual(now(), $project->start_date);
            }
            
            if ($project->end_date) {
                $this->assertInstanceOf(\Carbon\Carbon::class, $project->end_date);
                
                // End date should be after start date if both exist
                if ($project->start_date) {
                    $this->assertGreaterThanOrEqual($project->start_date, $project->end_date);
                }
            }
        }
    }

    /** @test */
    public function project_seeder_creates_specific_named_projects()
    {
        Role::factory()->create(['name' => 'client']);

        $this->artisan('db:seed', ['--class' => 'Database\Seeders\ProjectSeeder']);

        // Check for some specific projects that should be created
        $expectedProjects = [
            'Corporate Website Redesign',
            'E-learning Platform Development',
            'Fitness Tracking Mobile App',
            'Food Delivery App',
            'Fashion E-commerce Platform',
            'Banking App UI/UX Redesign',
            'Tech Startup Brand Identity',
        ];

        foreach ($expectedProjects as $projectTitle) {
            $this->assertDatabaseHas('projects', ['title' => $projectTitle]);
        }
    }

    /** @test */
    public function project_seeder_handles_missing_client_role_gracefully()
    {
        // Don't create client role to test graceful handling
        
        $this->artisan('db:seed', ['--class' => 'Database\Seeders\ProjectSeeder']);

        // Should still create services
        $this->assertDatabaseCount('services', 6);
        
        // Should not create users or projects without client role
        $this->assertDatabaseCount('users', 0);
    }

    /** @test */
    public function project_seeder_can_be_run_multiple_times()
    {
        Role::factory()->create(['name' => 'client']);

        // Run seeder twice
        $this->artisan('db:seed', ['--class' => 'Database\Seeders\ProjectSeeder']);
        $this->artisan('db:seed', ['--class' => 'Database\Seeders\ProjectSeeder']);

        // Should not create duplicates due to firstOrCreate
        $this->assertDatabaseCount('services', 6);
        $this->assertDatabaseCount('users', 5);
        
        // Projects might be duplicated since they use title as unique key
        // and we generate random suffixes for slugs
        $projects = Project::all();
        $this->assertGreaterThan(10, $projects->count());
    }

    /** @test */
    public function project_seeder_creates_projects_with_proper_client_names()
    {
        Role::factory()->create(['name' => 'client']);

        $this->artisan('db:seed', ['--class' => 'Database\Seeders\ProjectSeeder']);

        $projects = Project::with('client.addresses')->get();

        foreach ($projects as $project) {
            $this->assertNotEmpty($project->client_name);
            
            // Client name should either be the company name or full name
            if ($project->client && $project->client->addresses->where('is_default', true)->first()) {
                $defaultAddress = $project->client->addresses->where('is_default', true)->first();
                if ($defaultAddress->company) {
                    $this->assertEquals($defaultAddress->company, $project->client_name);
                } else {
                    $expectedName = $project->client->first_name . ' ' . $project->client->last_name;
                    $this->assertEquals($expectedName, $project->client_name);
                }
            }
        }
    }
}
