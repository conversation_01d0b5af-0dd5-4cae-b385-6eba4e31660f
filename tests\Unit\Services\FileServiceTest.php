<?php

namespace Tests\Unit\Services;

use App\Services\FileService;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Config;
use Tests\TestCase;

class FileServiceTest extends TestCase
{
    private FileService $fileService;
    
    protected function setUp(): void
    {
        parent::setUp();
        $this->fileService = new FileService();
        
        // Set up test configuration
        Config::set('file.max_file_size', 50 * 1024 * 1024); // 50MB
        Config::set('file.allowed_mimes', [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain',
            'text/csv',
            'application/zip',
        ]);
        Config::set('file.allowed_extensions', ['pdf', 'doc', 'docx', 'txt', 'csv', 'zip']);
        Config::set('file.forbidden_extensions', ['exe', 'bat', 'cmd', 'php', 'js']);
        Config::set('file.virus_scan.enabled', false);
        Config::set('file.storage.disk', 'testing');
        Config::set('file.storage.path', 'files');
        Config::set('file.storage.temp_path', 'temp/files');
        Config::set('file.logging.enabled', false);
        Config::set('file.security.validate_file_content', true);
        Config::set('file.security.check_file_signature', true);
        Config::set('file.security.sanitize_filename', true);
        
        Config::set('filesystems.disks.testing', [
            'driver' => 'local',
            'root' => storage_path('app/testing'),
        ]);
        
        Storage::fake('testing');
    }
    
    protected function tearDown(): void
    {
        Storage::disk('testing')->deleteDirectory('');
        parent::tearDown();
    }
    
    /** @test */
    public function it_can_validate_valid_pdf_file()
    {
        // Create a fake PDF file with proper header
        $tempPath = tempnam(sys_get_temp_dir(), 'test_pdf');
        file_put_contents($tempPath, '%PDF-1.4' . str_repeat('a', 1000));
        
        $file = new UploadedFile($tempPath, 'test.pdf', 'application/pdf', null, true);
        
        $result = $this->fileService->validateFile($file);
        
        $this->assertTrue($result['valid']);
        $this->assertEmpty($result['errors']);
        $this->assertEquals('test.pdf', $result['file_info']['original_name']);
        
        unlink($tempPath);
    }
    
    /** @test */
    public function it_rejects_oversized_files()
    {
        Config::set('file.max_file_size', 1024); // 1KB limit
        
        $file = UploadedFile::fake()->create('large.pdf', 2048, 'application/pdf'); // 2KB
        
        $result = $this->fileService->validateFile($file);
        
        $this->assertFalse($result['valid']);
        $this->assertContains('File size exceeds maximum allowed size', $result['errors'][0]);
    }
    
    /** @test */
    public function it_rejects_forbidden_file_types()
    {
        $file = UploadedFile::fake()->create('malware.exe', 1024, 'application/octet-stream');
        
        $result = $this->fileService->validateFile($file);
        
        $this->assertFalse($result['valid']);
        $this->assertContains('File type is not allowed for security reasons', $result['errors']);
    }
    
    /** @test */
    public function it_rejects_invalid_mime_types()
    {
        $file = UploadedFile::fake()->create('document.unknown', 1024, 'application/unknown');
        
        $result = $this->fileService->validateFile($file);
        
        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('Invalid file type', $result['errors'][0]);
    }
    
    /** @test */
    public function it_sanitizes_dangerous_filenames()
    {
        $dangerous = '../../../etc/passwd.pdf';
        $sanitized = $this->fileService->sanitizeFilename($dangerous);
        
        $this->assertStringNotContainsString('..', $sanitized);
        $this->assertStringNotContainsString('/', $sanitized);
        $this->assertStringContainsString('.pdf', $sanitized);
    }
    
    /** @test */
    public function it_sanitizes_special_characters_in_filenames()
    {
        $filename = 'test file with spaces & special chars!@#$.pdf';
        $sanitized = $this->fileService->sanitizeFilename($filename);
        
        $this->assertStringNotContainsString(' ', $sanitized);
        $this->assertStringNotContainsString('&', $sanitized);
        $this->assertStringNotContainsString('!', $sanitized);
        $this->assertStringContainsString('.pdf', $sanitized);
    }
    
    /** @test */
    public function it_generates_unique_filenames()
    {
        $filename1 = $this->fileService->sanitizeFilename('test.pdf');
        $filename2 = $this->fileService->sanitizeFilename('test.pdf');
        
        $this->assertNotEquals($filename1, $filename2);
    }
    
    /** @test */
    public function it_handles_empty_filenames()
    {
        $sanitized = $this->fileService->sanitizeFilename('');
        
        $this->assertNotEmpty($sanitized);
        $this->assertStringContainsString('file_', $sanitized);
    }
    
    /** @test */
    public function virus_scan_returns_clean_when_disabled()
    {
        Config::set('file.virus_scan.enabled', false);
        
        $tempPath = tempnam(sys_get_temp_dir(), 'test_file');
        file_put_contents($tempPath, 'test content');
        
        $result = $this->fileService->scanForViruses($tempPath);
        
        $this->assertTrue($result['clean']);
        $this->assertEquals('Virus scanning disabled', $result['message']);
        
        unlink($tempPath);
    }
    
    /** @test */
    public function basic_security_check_passes_for_clean_content()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_file');
        file_put_contents($tempPath, 'clean document content');
        
        $reflection = new \ReflectionClass($this->fileService);
        $method = $reflection->getMethod('performBasicSecurityCheck');
        $method->setAccessible(true);
        
        $result = $method->invoke($this->fileService, $tempPath);
        
        $this->assertTrue($result['clean']);
        $this->assertEquals('Basic security check passed', $result['message']);
        
        unlink($tempPath);
    }
    
    /** @test */
    public function basic_security_check_fails_for_suspicious_content()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_file');
        file_put_contents($tempPath, '<script>alert("xss")</script>');
        
        $reflection = new \ReflectionClass($this->fileService);
        $method = $reflection->getMethod('performBasicSecurityCheck');
        $method->setAccessible(true);
        
        $result = $method->invoke($this->fileService, $tempPath);
        
        $this->assertFalse($result['clean']);
        $this->assertEquals('Suspicious content detected', $result['message']);
        
        unlink($tempPath);
    }
    
    /** @test */
    public function it_validates_pdf_file_signature()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_pdf');
        file_put_contents($tempPath, '%PDF-1.4' . str_repeat('a', 100));
        
        $file = new UploadedFile($tempPath, 'test.pdf', 'application/pdf', null, true);
        
        $reflection = new \ReflectionClass($this->fileService);
        $method = $reflection->getMethod('validateFileSignature');
        $method->setAccessible(true);
        
        $result = $method->invoke($this->fileService, $file);
        
        $this->assertTrue($result);
        
        unlink($tempPath);
    }
    
    /** @test */
    public function it_rejects_invalid_pdf_signature()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_file');
        file_put_contents($tempPath, 'not a real PDF file');
        
        $file = new UploadedFile($tempPath, 'fake.pdf', 'application/pdf', null, true);
        
        $reflection = new \ReflectionClass($this->fileService);
        $method = $reflection->getMethod('validateFileSignature');
        $method->setAccessible(true);
        
        $result = $method->invoke($this->fileService, $file);
        
        $this->assertFalse($result);
        
        unlink($tempPath);
    }
    
    /** @test */
    public function it_validates_zip_file_signature()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_zip');
        file_put_contents($tempPath, 'PK' . str_repeat('a', 100));
        
        $file = new UploadedFile($tempPath, 'test.zip', 'application/zip', null, true);
        
        $reflection = new \ReflectionClass($this->fileService);
        $method = $reflection->getMethod('validateFileSignature');
        $method->setAccessible(true);
        
        $result = $method->invoke($this->fileService, $file);
        
        $this->assertTrue($result);
        
        unlink($tempPath);
    }
    
    /** @test */
    public function it_allows_text_files_without_specific_signatures()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_txt');
        file_put_contents($tempPath, 'This is a plain text file');
        
        $file = new UploadedFile($tempPath, 'test.txt', 'text/plain', null, true);
        
        $reflection = new \ReflectionClass($this->fileService);
        $method = $reflection->getMethod('validateFileSignature');
        $method->setAccessible(true);
        
        $result = $method->invoke($this->fileService, $file);
        
        $this->assertTrue($result);
        
        unlink($tempPath);
    }
    
    /** @test */
    public function it_detects_file_type_category()
    {
        $pdfFile = UploadedFile::fake()->create('test.pdf', 1024, 'application/pdf');
        $docFile = UploadedFile::fake()->create('test.docx', 1024, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');
        $zipFile = UploadedFile::fake()->create('test.zip', 1024, 'application/zip');
        
        $reflection = new \ReflectionClass($this->fileService);
        $method = $reflection->getMethod('getFileType');
        $method->setAccessible(true);
        
        $this->assertEquals('document', $method->invoke($this->fileService, $pdfFile));
        $this->assertEquals('document', $method->invoke($this->fileService, $docFile));
        $this->assertEquals('archive', $method->invoke($this->fileService, $zipFile));
    }
    
    /** @test */
    public function it_formats_bytes_correctly()
    {
        $reflection = new \ReflectionClass($this->fileService);
        $method = $reflection->getMethod('formatBytes');
        $method->setAccessible(true);
        
        $this->assertEquals('1 KB', $method->invoke($this->fileService, 1024));
        $this->assertEquals('1 MB', $method->invoke($this->fileService, 1024 * 1024));
        $this->assertEquals('1 GB', $method->invoke($this->fileService, 1024 * 1024 * 1024));
    }
    
    /** @test */
    public function it_scans_for_suspicious_patterns()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_file');
        file_put_contents($tempPath, 'eval(malicious_code)');
        
        $reflection = new \ReflectionClass($this->fileService);
        $method = $reflection->getMethod('scanForSuspiciousPatterns');
        $method->setAccessible(true);
        
        $result = $method->invoke($this->fileService, $tempPath);
        
        $this->assertFalse($result);
        
        unlink($tempPath);
    }
    
    /** @test */
    public function it_passes_clean_content_pattern_scan()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_file');
        file_put_contents($tempPath, 'This is clean document content with no suspicious patterns.');
        
        $reflection = new \ReflectionClass($this->fileService);
        $method = $reflection->getMethod('scanForSuspiciousPatterns');
        $method->setAccessible(true);
        
        $result = $method->invoke($this->fileService, $tempPath);
        
        $this->assertTrue($result);
        
        unlink($tempPath);
    }
}
