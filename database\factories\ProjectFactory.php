<?php

namespace Database\Factories;

use App\Models\Project;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Project>
 */
class ProjectFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $title = $this->faker->sentence(3);
        
        return [
            'uuid' => $this->faker->uuid(),
            'client_id' => User::factory(),
            'title' => $title,
            'slug' => Str::slug($title) . '-' . $this->faker->randomNumber(4),
            'description' => $this->faker->paragraph(),
            'status' => $this->faker->randomElement(['planning', 'in_progress', 'review', 'completed', 'on_hold', 'cancelled']),
            'start_date' => $this->faker->dateTimeBetween('-6 months', 'now'),
            'end_date' => $this->faker->optional()->dateTimeBetween('now', '+6 months'),
            'content' => $this->faker->optional()->paragraphs(3, true),
            'client_name' => $this->faker->optional()->company(),
            'featured_image' => $this->faker->optional()->imageUrl(800, 600, 'business'),
            'gallery' => [
                $this->faker->imageUrl(800, 600, 'business'),
                $this->faker->imageUrl(800, 600, 'technology'),
            ],
            'project_url' => $this->faker->optional()->url(),
            'estimated_hours' => $this->faker->optional()->randomFloat(2, 10, 500),
            'actual_hours' => $this->faker->optional()->randomFloat(2, 5, 600),
            'hourly_rate' => $this->faker->optional()->randomFloat(2, 25, 150),
            'total_amount' => $this->faker->optional()->randomFloat(2, 500, 50000),
            'currency_code' => $this->faker->randomElement(['ZAR', 'USD', 'EUR', 'GBP']),
            'priority' => $this->faker->randomElement(['low', 'medium', 'high', 'urgent']),
            'is_featured' => $this->faker->boolean(20), // 20% chance of being featured
            'is_published' => $this->faker->boolean(80), // 80% chance of being published
            'meta_title' => $this->faker->optional()->sentence(),
            'meta_description' => $this->faker->optional()->paragraph(),
            'meta_keywords' => $this->faker->optional()->words(5, true),
            'is_deleted' => false,
            'created_at' => $this->faker->dateTimeBetween('-1 year', 'now'),
            'updated_at' => function (array $attributes) {
                return $this->faker->dateTimeBetween($attributes['created_at'], 'now');
            },
        ];
    }

    /**
     * Indicate that the project is in progress.
     */
    public function inProgress(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'in_progress',
            'is_published' => true,
        ]);
    }

    /**
     * Indicate that the project is completed.
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
            'end_date' => $this->faker->dateTimeBetween('-3 months', 'now'),
            'is_published' => true,
        ]);
    }

    /**
     * Indicate that the project is featured.
     */
    public function featured(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_featured' => true,
            'is_published' => true,
        ]);
    }
}
