<?php

namespace App\Services;

use App\Services\Image\Traits\ImageOptimization;
use App\Services\Image\Traits\ImageSecurity;
use App\Services\Image\Traits\ImageLogging;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ImageService
{
    use ImageOptimization, ImageSecurity, ImageLogging;
    
    /**
     * Process uploaded image with full pipeline
     */
    public function processUploadedImage(UploadedFile $file, array $options = []): array
    {
        $startTime = microtime(true);
        
        try {
            // Step 1: Validate the uploaded file
            $validation = $this->validateImageFile($file);
            if (!$validation['valid']) {
                return [
                    'success' => false,
                    'errors' => $validation['errors'],
                    'file_info' => $validation['file_info']
                ];
            }
            
            // Step 2: Sanitize filename
            $sanitizedName = $this->sanitizeFilename($file->getClientOriginalName());
            $baseName = pathinfo($sanitizedName, PATHINFO_FILENAME);
            
            // Step 3: Store temporary file
            $tempPath = $this->storeTempFile($file, $sanitizedName);
            if (!$tempPath) {
                return [
                    'success' => false,
                    'errors' => ['Failed to store temporary file']
                ];
            }
            
            // Step 4: Virus scan
            $scanResult = $this->scanForViruses($tempPath);
            if (!$scanResult['clean']) {
                $this->cleanupTempFile($tempPath);
                return [
                    'success' => false,
                    'errors' => ['Virus scan failed: ' . $scanResult['message']]
                ];
            }
            
            // Step 5: Optimize original image
            $optimized = $this->optimizeImage($tempPath, $options);
            if (!$optimized) {
                $this->cleanupTempFile($tempPath);
                return [
                    'success' => false,
                    'errors' => ['Image optimization failed']
                ];
            }
            
            // Step 6: Move to final location
            $finalPath = $this->moveToFinalLocation($tempPath, $sanitizedName, $options);
            if (!$finalPath) {
                $this->cleanupTempFile($tempPath);
                return [
                    'success' => false,
                    'errors' => ['Failed to move file to final location']
                ];
            }
            
            // Step 7: Create size variants if requested
            $variants = [];
            if ($options['create_variants'] ?? true) {
                $variants = $this->createSizeVariants($finalPath, $baseName, $options['sizes'] ?? []);
            }
            
            // Step 8: Create WebP version if enabled
            $webpPath = null;
            if (config('image.enable_webp_conversion', true) && ($options['create_webp'] ?? true)) {
                $webpPath = $this->convertToWebP($finalPath);
            }
            
            // Log processing time
            $this->logProcessingTime('full_image_processing', $startTime, [
                'original_name' => $file->getClientOriginalName(),
                'final_name' => $sanitizedName,
                'file_size' => $file->getSize(),
                'variants_created' => count($variants)
            ]);
            
            // Log file details
            $this->logFileDetails('image_processed', array_merge($validation['file_info'], [
                'final_path' => $finalPath,
                'variants' => array_keys($variants),
                'webp_created' => $webpPath !== null
            ]));
            
            return [
                'success' => true,
                'original_path' => $finalPath,
                'webp_path' => $webpPath,
                'variants' => $variants,
                'file_info' => $validation['file_info'],
                'processing_time_ms' => round((microtime(true) - $startTime) * 1000, 2)
            ];
            
        } catch (\Exception $e) {
            $this->logError("Image processing failed: " . $e->getMessage(), [
                'original_name' => $file->getClientOriginalName(),
                'exception' => $e->getTraceAsString()
            ]);
            
            return [
                'success' => false,
                'errors' => ['Image processing failed: ' . $e->getMessage()]
            ];
        }
    }
    
    /**
     * Process image from path (for existing files)
     */
    public function processImageFromPath(string $imagePath, array $options = []): array
    {
        $startTime = microtime(true);
        
        try {
            if (!file_exists($imagePath)) {
                return [
                    'success' => false,
                    'errors' => ['File does not exist: ' . $imagePath]
                ];
            }
            
            // Get file info
            $fileInfo = [
                'path' => $imagePath,
                'size' => filesize($imagePath),
                'mime_type' => mime_content_type($imagePath),
                'dimensions' => getimagesize($imagePath)
            ];
            
            // Virus scan
            $scanResult = $this->scanForViruses($imagePath);
            if (!$scanResult['clean']) {
                return [
                    'success' => false,
                    'errors' => ['Virus scan failed: ' . $scanResult['message']]
                ];
            }
            
            // Optimize image
            $optimized = $this->optimizeImage($imagePath, $options);
            if (!$optimized) {
                return [
                    'success' => false,
                    'errors' => ['Image optimization failed']
                ];
            }
            
            // Create variants if requested
            $variants = [];
            if ($options['create_variants'] ?? false) {
                $baseName = pathinfo($imagePath, PATHINFO_FILENAME);
                $variants = $this->createSizeVariants($imagePath, $baseName, $options['sizes'] ?? []);
            }
            
            // Create WebP version if requested
            $webpPath = null;
            if ($options['create_webp'] ?? false) {
                $webpPath = $this->convertToWebP($imagePath);
            }
            
            $this->logProcessingTime('path_image_processing', $startTime, [
                'path' => $imagePath,
                'variants_created' => count($variants)
            ]);
            
            return [
                'success' => true,
                'original_path' => $imagePath,
                'webp_path' => $webpPath,
                'variants' => $variants,
                'file_info' => $fileInfo,
                'processing_time_ms' => round((microtime(true) - $startTime) * 1000, 2)
            ];
            
        } catch (\Exception $e) {
            $this->logError("Path image processing failed: " . $e->getMessage(), [
                'path' => $imagePath,
                'exception' => $e->getTraceAsString()
            ]);
            
            return [
                'success' => false,
                'errors' => ['Image processing failed: ' . $e->getMessage()]
            ];
        }
    }
    
    /**
     * Store temporary file
     */
    protected function storeTempFile(UploadedFile $file, string $filename): ?string
    {
        try {
            $tempPath = config('image.storage.temp_path', 'temp/images');
            $disk = config('image.storage.disk', 'public');
            
            $storedPath = $file->storeAs($tempPath, $filename, $disk);
            
            if ($storedPath) {
                return Storage::disk($disk)->path($storedPath);
            }
            
            return null;
            
        } catch (\Exception $e) {
            $this->logError("Temp file storage failed: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Move file to final location
     */
    protected function moveToFinalLocation(string $tempPath, string $filename, array $options = []): ?string
    {
        try {
            $finalPath = $options['path'] ?? config('image.storage.path', 'images');
            $disk = config('image.storage.disk', 'public');
            
            // Create subdirectory if specified
            if (isset($options['subdirectory'])) {
                $finalPath .= '/' . trim($options['subdirectory'], '/');
            }
            
            $finalFullPath = $finalPath . '/' . $filename;
            
            // Ensure directory exists
            $fullDiskPath = Storage::disk($disk)->path($finalPath);
            if (!is_dir($fullDiskPath)) {
                mkdir($fullDiskPath, 0755, true);
            }
            
            $finalSystemPath = Storage::disk($disk)->path($finalFullPath);
            
            if (rename($tempPath, $finalSystemPath)) {
                return $finalSystemPath;
            }
            
            return null;
            
        } catch (\Exception $e) {
            $this->logError("File move to final location failed: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Clean up temporary file
     */
    protected function cleanupTempFile(string $tempPath): void
    {
        try {
            if (file_exists($tempPath)) {
                unlink($tempPath);
            }
        } catch (\Exception $e) {
            $this->logError("Temp file cleanup failed: " . $e->getMessage());
        }
    }
    
    /**
     * Get public URL for image
     */
    public function getImageUrl(string $path): string
    {
        $disk = config('image.storage.disk', 'public');
        
        if ($disk === 'public') {
            return Storage::url($path);
        }
        
        return Storage::disk($disk)->url($path);
    }
    
    /**
     * Delete image and its variants
     */
    public function deleteImage(string $path, bool $deleteVariants = true): bool
    {
        try {
            $disk = config('image.storage.disk', 'public');
            $deleted = Storage::disk($disk)->delete($path);
            
            if ($deleteVariants) {
                $this->deleteImageVariants($path);
            }
            
            $this->logProcessing("Image deleted: {$path}");
            return $deleted;
            
        } catch (\Exception $e) {
            $this->logError("Image deletion failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete image variants
     */
    protected function deleteImageVariants(string $originalPath): void
    {
        try {
            $pathInfo = pathinfo($originalPath);
            $baseName = $pathInfo['filename'];
            $directory = $pathInfo['dirname'];
            $extension = $pathInfo['extension'];
            
            $sizes = array_keys(config('image.sizes', []));
            
            foreach ($sizes as $size) {
                $variantPath = $directory . '/' . $baseName . '_' . $size . '.' . $extension;
                $webpPath = $directory . '/' . $baseName . '_' . $size . '.webp';
                
                Storage::disk(config('image.storage.disk', 'public'))->delete($variantPath);
                Storage::disk(config('image.storage.disk', 'public'))->delete($webpPath);
            }
            
            // Delete main WebP version
            $mainWebpPath = $directory . '/' . $baseName . '.webp';
            Storage::disk(config('image.storage.disk', 'public'))->delete($mainWebpPath);
            
        } catch (\Exception $e) {
            $this->logError("Variant deletion failed: " . $e->getMessage());
        }
    }

    /**
     * Quick image upload with basic processing
     */
    public function quickUpload(UploadedFile $file, string $subdirectory = 'uploads'): array
    {
        return $this->processUploadedImage($file, [
            'subdirectory' => $subdirectory,
            'create_variants' => false,
            'create_webp' => true
        ]);
    }

    /**
     * Full image upload with all variants
     */
    public function fullUpload(UploadedFile $file, string $subdirectory = 'uploads'): array
    {
        return $this->processUploadedImage($file, [
            'subdirectory' => $subdirectory,
            'create_variants' => true,
            'create_webp' => true
        ]);
    }

    /**
     * Get image information
     */
    public function getImageInfo(string $path): array
    {
        try {
            if (!file_exists($path)) {
                return ['exists' => false];
            }

            $imageInfo = getimagesize($path);
            $fileInfo = [
                'exists' => true,
                'path' => $path,
                'size' => filesize($path),
                'mime_type' => mime_content_type($path),
                'width' => $imageInfo[0] ?? null,
                'height' => $imageInfo[1] ?? null,
                'type' => $imageInfo[2] ?? null,
                'bits' => $imageInfo['bits'] ?? null,
                'channels' => $imageInfo['channels'] ?? null,
            ];

            return $fileInfo;

        } catch (\Exception $e) {
            $this->logError("Get image info failed: " . $e->getMessage());
            return ['exists' => false, 'error' => $e->getMessage()];
        }
    }
}
