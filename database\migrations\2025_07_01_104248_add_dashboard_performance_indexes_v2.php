<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add performance indexes using raw SQL to avoid conflicts
        $this->addIndexIfNotExists('orders', 'orders_created_status_perf_idx', ['created_at', 'status']);
        $this->addIndexIfNotExists('orders', 'orders_revenue_perf_idx', ['created_at', 'payment_status', 'total_amount']);

        $this->addIndexIfNotExists('projects', 'projects_client_created_perf_idx', ['client_id', 'created_at']);
        $this->addIndexIfNotExists('projects', 'projects_status_deleted_perf_idx', ['status', 'is_deleted']);
        $this->addIndexIfNotExists('projects', 'projects_value_perf_idx', ['client_id', 'is_deleted', 'total_amount']);

        $this->addIndexIfNotExists('users', 'users_status_perf_idx', ['is_deleted', 'is_active']);
        $this->addIndexIfNotExists('users', 'users_role_deleted_perf_idx', ['role_id', 'is_deleted']);

        $this->addIndexIfNotExists('products', 'products_status_perf_idx', ['is_deleted', 'is_active']);
        $this->addIndexIfNotExists('products', 'products_inventory_perf_idx', ['is_deleted', 'track_inventory']);
        $this->addIndexIfNotExists('products', 'products_stock_management_perf_idx', ['track_inventory', 'inventory_quantity', 'low_stock_threshold']);
        $this->addIndexIfNotExists('products', 'products_featured_perf_idx', ['is_featured', 'is_active', 'is_deleted']);

        $this->addIndexIfNotExists('product_categories', 'categories_status_perf_idx', ['is_deleted', 'is_active']);
        $this->addIndexIfNotExists('product_categories', 'categories_parent_perf_idx', ['parent_id', 'is_deleted']);

        $this->addIndexIfNotExists('activity_logs', 'activity_logs_occurred_perf_idx', ['occurred_at']);
        $this->addIndexIfNotExists('activity_logs', 'activity_logs_user_occurred_perf_idx', ['user_id', 'occurred_at']);
        $this->addIndexIfNotExists('activity_logs', 'activity_logs_suspicious_perf_idx', ['is_suspicious', 'occurred_at']);
        $this->addIndexIfNotExists('activity_logs', 'activity_logs_type_occurred_perf_idx', ['activity_type', 'occurred_at']);

        $this->addIndexIfNotExists('order_items', 'order_items_order_product_perf_idx', ['order_id', 'product_id']);
        $this->addIndexIfNotExists('order_items', 'order_items_product_created_perf_idx', ['product_id', 'created_at']);

        $this->addIndexIfNotExists('product_category_relations', 'category_relations_category_created_perf_idx', ['category_id', 'created_at']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $this->dropIndexIfExists('orders', 'orders_created_status_perf_idx');
        $this->dropIndexIfExists('orders', 'orders_revenue_perf_idx');

        $this->dropIndexIfExists('projects', 'projects_client_created_perf_idx');
        $this->dropIndexIfExists('projects', 'projects_status_deleted_perf_idx');
        $this->dropIndexIfExists('projects', 'projects_value_perf_idx');

        $this->dropIndexIfExists('users', 'users_status_perf_idx');
        $this->dropIndexIfExists('users', 'users_role_deleted_perf_idx');

        $this->dropIndexIfExists('products', 'products_status_perf_idx');
        $this->dropIndexIfExists('products', 'products_inventory_perf_idx');
        $this->dropIndexIfExists('products', 'products_stock_management_perf_idx');
        $this->dropIndexIfExists('products', 'products_featured_perf_idx');

        $this->dropIndexIfExists('product_categories', 'categories_status_perf_idx');
        $this->dropIndexIfExists('product_categories', 'categories_parent_perf_idx');

        $this->dropIndexIfExists('activity_logs', 'activity_logs_occurred_perf_idx');
        $this->dropIndexIfExists('activity_logs', 'activity_logs_user_occurred_perf_idx');
        $this->dropIndexIfExists('activity_logs', 'activity_logs_suspicious_perf_idx');
        $this->dropIndexIfExists('activity_logs', 'activity_logs_type_occurred_perf_idx');

        $this->dropIndexIfExists('order_items', 'order_items_order_product_perf_idx');
        $this->dropIndexIfExists('order_items', 'order_items_product_created_perf_idx');

        $this->dropIndexIfExists('product_category_relations', 'category_relations_category_created_perf_idx');
    }

    /**
     * Add index if it doesn't exist.
     */
    private function addIndexIfNotExists(string $table, string $indexName, array $columns): void
    {
        try {
            // Use Laravel's Schema builder for database-agnostic index creation
            Schema::table($table, function (Blueprint $table) use ($indexName, $columns) {
                $table->index($columns, $indexName);
            });
        } catch (\Exception $e) {
            // Index might already exist, ignore the error
            if (!str_contains($e->getMessage(), 'already exists') &&
                !str_contains($e->getMessage(), 'Duplicate key name')) {
                throw $e;
            }
        }
    }

    /**
     * Drop index if it exists.
     */
    private function dropIndexIfExists(string $table, string $indexName): void
    {
        try {
            Schema::table($table, function (Blueprint $table) use ($indexName) {
                $table->dropIndex($indexName);
            });
        } catch (\Exception $e) {
            // Index might not exist, ignore the error
            if (!str_contains($e->getMessage(), 'does not exist') &&
                !str_contains($e->getMessage(), 'check that column/key exists')) {
                throw $e;
            }
        }
    }
};
