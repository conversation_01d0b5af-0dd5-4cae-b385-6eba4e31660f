<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('shopping_carts', function (Blueprint $table) {
            $table->uuid('coupon_id')->nullable()->after('currency');
            $table->string('coupon_code', 50)->nullable()->after('coupon_id');

            $table->foreign('coupon_id')->references('uuid')->on('coupons')->onDelete('set null');
            $table->index(['coupon_id', 'coupon_code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('shopping_carts', function (Blueprint $table) {
            $table->dropForeign(['coupon_id']);
            $table->dropIndex(['coupon_id', 'coupon_code']);
            $table->dropColumn(['coupon_id', 'coupon_code']);
        });
    }
};
