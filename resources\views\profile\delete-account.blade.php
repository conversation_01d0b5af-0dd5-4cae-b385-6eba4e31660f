@extends('layouts.dashboard')

@section('title', 'Delete Account')

@section('content')
<div class="max-w-2xl mx-auto">
    <!-- <PERSON> Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-red-900">Delete Account</h1>
        <p class="mt-2 text-gray-600">This action cannot be undone. Please read carefully before proceeding.</p>
    </div>

    <!-- Warning Card -->
    <div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-8">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-lg font-semibold text-red-900">Warning: Account Deletion</h3>
                <div class="mt-2 text-red-700">
                    <p class="mb-2">Deleting your account will permanently remove:</p>
                    <ul class="list-disc list-inside space-y-1 text-sm">
                        <li>Your profile information and settings</li>
                        <li>Order history and transaction records</li>
                        @if(auth()->user()->isClient())
                            <li>Project data and communication history</li>
                            <li>Uploaded files and project deliverables</li>
                        @endif
                        <li>Saved addresses and payment methods</li>
                        <li>Account preferences and customizations</li>
                    </ul>
                    <p class="mt-3 font-medium">This action cannot be reversed.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Account Summary -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">Account Summary</h2>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-sm font-medium text-gray-700 mb-2">Account Information</h3>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Name:</span>
                            <span class="text-gray-900">{{ auth()->user()->full_name }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Email:</span>
                            <span class="text-gray-900">{{ auth()->user()->email }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Account Type:</span>
                            <span class="text-gray-900 capitalize">{{ auth()->user()->role->name }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Member Since:</span>
                            <span class="text-gray-900">{{ auth()->user()->created_at->format('M d, Y') }}</span>
                        </div>
                    </div>
                </div>
                <div>
                    <h3 class="text-sm font-medium text-gray-700 mb-2">Data Summary</h3>
                    <div class="space-y-2 text-sm">
                        @if(auth()->user()->isCustomer())
                            <div class="flex justify-between">
                                <span class="text-gray-600">Total Orders:</span>
                                <span class="text-gray-900">{{ auth()->user()->orders()->count() }}</span>
                            </div>
                        @endif
                        @if(auth()->user()->isClient())
                            <div class="flex justify-between">
                                <span class="text-gray-600">Active Projects:</span>
                                <span class="text-gray-900">{{ auth()->user()->projects()->where('status', '!=', 'completed')->count() }}</span>
                            </div>
                        @endif
                        <div class="flex justify-between">
                            <span class="text-gray-600">Saved Addresses:</span>
                            <span class="text-gray-900">{{ auth()->user()->addresses()->count() }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Deletion Form -->
    <div class="bg-white rounded-lg shadow-sm border border-red-200">
        <div class="px-6 py-4 border-b border-red-200">
            <h2 class="text-lg font-semibold text-red-900">Confirm Account Deletion</h2>
        </div>

        <form action="{{ route('profile.destroy') }}" method="POST" class="p-6 space-y-6">
            @csrf
            @method('DELETE')

            <div>
                <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                    Enter your password to confirm
                </label>
                <input type="password" id="password" name="password" required
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 @error('password') border-red-500 @enderror">
                @error('password')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="confirmation" class="block text-sm font-medium text-gray-700 mb-2">
                    Type "DELETE" to confirm (case sensitive)
                </label>
                <input type="text" id="confirmation" name="confirmation" required
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 @error('confirmation') border-red-500 @enderror">
                @error('confirmation')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div class="flex items-center">
                <input type="checkbox" id="understand" name="understand" required
                       class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded">
                <label for="understand" class="ml-2 text-sm text-gray-700">
                    I understand that this action is permanent and cannot be undone
                </label>
            </div>

            <div class="flex justify-between items-center pt-6 border-t border-gray-200">
                <a href="{{ route('profile.edit') }}" 
                   class="text-gray-600 hover:text-gray-800 transition-colors">
                    ← Back to Profile
                </a>
                <button type="submit" 
                        class="bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors"
                        onclick="return confirm('Are you absolutely sure you want to delete your account? This action cannot be undone.')">
                    Delete My Account
                </button>
            </div>
        </form>
    </div>
</div>
@endsection
