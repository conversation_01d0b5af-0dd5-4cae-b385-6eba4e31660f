<?php

namespace App\Services;

use App\Mail\JobApplicationReceived;
use App\Mail\OrderConfirmation;
use App\Mail\OrderStatusUpdate;
use App\Mail\PaymentReceived;
use App\Mail\ProjectApplicationReceived;
use App\Models\JobApplication;
use App\Models\Order;
use App\Models\Payment;
use App\Models\ProjectApplication;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class EmailNotificationService
{
    /**
     * Send order confirmation email.
     */
    public function sendOrderConfirmation(Order $order): bool
    {
        try {
            Mail::to($order->email)->send(new OrderConfirmation($order));
            
            Log::info('Order confirmation email sent', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'email' => $order->email,
            ]);
            
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send order confirmation email', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'email' => $order->email,
                'error' => $e->getMessage(),
            ]);
            
            return false;
        }
    }

    /**
     * Send order status update email.
     */
    public function sendOrderStatusUpdate(Order $order, string $previousStatus = null): bool
    {
        try {
            Mail::to($order->email)->send(new OrderStatusUpdate($order, $previousStatus));
            
            Log::info('Order status update email sent', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'email' => $order->email,
                'status' => $order->status,
                'previous_status' => $previousStatus,
            ]);
            
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send order status update email', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'email' => $order->email,
                'status' => $order->status,
                'error' => $e->getMessage(),
            ]);
            
            return false;
        }
    }

    /**
     * Send payment received email.
     */
    public function sendPaymentReceived(Order $order, Payment $payment = null): bool
    {
        try {
            Mail::to($order->email)->send(new PaymentReceived($order, $payment));
            
            Log::info('Payment received email sent', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'email' => $order->email,
                'payment_id' => $payment?->id,
            ]);
            
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send payment received email', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'email' => $order->email,
                'payment_id' => $payment?->id,
                'error' => $e->getMessage(),
            ]);
            
            return false;
        }
    }

    /**
     * Send job application received email.
     */
    public function sendJobApplicationReceived(JobApplication $application): bool
    {
        try {
            Mail::to($application->email)->send(new JobApplicationReceived($application));
            
            Log::info('Job application received email sent', [
                'application_id' => $application->id,
                'reference_number' => $application->reference_number,
                'email' => $application->email,
            ]);
            
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send job application received email', [
                'application_id' => $application->id,
                'reference_number' => $application->reference_number,
                'email' => $application->email,
                'error' => $e->getMessage(),
            ]);
            
            return false;
        }
    }

    /**
     * Send project application received email.
     */
    public function sendProjectApplicationReceived(ProjectApplication $application): bool
    {
        try {
            Mail::to($application->email)->send(new ProjectApplicationReceived($application));
            
            Log::info('Project application received email sent', [
                'application_id' => $application->id,
                'reference_number' => $application->reference_number,
                'email' => $application->email,
            ]);
            
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send project application received email', [
                'application_id' => $application->id,
                'reference_number' => $application->reference_number,
                'email' => $application->email,
                'error' => $e->getMessage(),
            ]);
            
            return false;
        }
    }

    /**
     * Send admin notification for new order.
     */
    public function sendAdminOrderNotification(Order $order): bool
    {
        try {
            $adminEmail = config('mail.admin_notifications.orders', config('mail.from.address'));
            
            Mail::to($adminEmail)->send(new \App\Mail\Admin\NewOrderNotification($order));
            
            Log::info('Admin order notification sent', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'admin_email' => $adminEmail,
            ]);
            
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send admin order notification', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'error' => $e->getMessage(),
            ]);
            
            return false;
        }
    }

    /**
     * Send admin notification for new job application.
     */
    public function sendAdminJobApplicationNotification(JobApplication $application): bool
    {
        try {
            $adminEmail = config('mail.admin_notifications.applications', config('mail.from.address'));
            
            Mail::to($adminEmail)->send(new \App\Mail\Admin\NewJobApplicationNotification($application));
            
            Log::info('Admin job application notification sent', [
                'application_id' => $application->id,
                'reference_number' => $application->reference_number,
                'admin_email' => $adminEmail,
            ]);
            
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send admin job application notification', [
                'application_id' => $application->id,
                'reference_number' => $application->reference_number,
                'error' => $e->getMessage(),
            ]);
            
            return false;
        }
    }

    /**
     * Send admin notification for new project application.
     */
    public function sendAdminProjectApplicationNotification(ProjectApplication $application): bool
    {
        try {
            $adminEmail = config('mail.admin_notifications.applications', config('mail.from.address'));
            
            Mail::to($adminEmail)->send(new \App\Mail\Admin\NewProjectApplicationNotification($application));
            
            Log::info('Admin project application notification sent', [
                'application_id' => $application->id,
                'reference_number' => $application->reference_number,
                'admin_email' => $adminEmail,
            ]);
            
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send admin project application notification', [
                'application_id' => $application->id,
                'reference_number' => $application->reference_number,
                'error' => $e->getMessage(),
            ]);
            
            return false;
        }
    }

    /**
     * Send bulk email notifications.
     */
    public function sendBulkNotifications(array $emails, $mailable): bool
    {
        try {
            foreach ($emails as $email) {
                Mail::to($email)->queue($mailable);
            }
            
            Log::info('Bulk email notifications queued', [
                'count' => count($emails),
                'mailable' => get_class($mailable),
            ]);
            
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to queue bulk email notifications', [
                'count' => count($emails),
                'mailable' => get_class($mailable),
                'error' => $e->getMessage(),
            ]);
            
            return false;
        }
    }
}
