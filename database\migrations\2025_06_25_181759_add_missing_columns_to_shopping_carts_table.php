<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('shopping_carts', function (Blueprint $table) {
            $table->uuid('uuid')->unique()->after('id');
            $table->decimal('subtotal', 10, 2)->default(0)->after('session_id');
            $table->decimal('tax_amount', 10, 2)->default(0)->after('subtotal');
            $table->decimal('shipping_amount', 10, 2)->default(0)->after('tax_amount');
            $table->decimal('discount_amount', 10, 2)->default(0)->after('shipping_amount');
            $table->decimal('total', 10, 2)->default(0)->after('discount_amount');
            $table->string('currency', 3)->default('ZAR')->after('total');
            $table->timestamp('expires_at')->nullable()->after('currency');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('shopping_carts', function (Blueprint $table) {
            $table->dropColumn([
                'uuid',
                'subtotal',
                'tax_amount',
                'shipping_amount',
                'discount_amount',
                'total',
                'currency',
                'expires_at'
            ]);
        });
    }
};
