<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Job;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Str;

class JobController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:admin,staff');
    }

    /**
     * Display a listing of jobs.
     */
    public function index(Request $request): View
    {
        $query = Job::query();

        // Search functionality
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                  ->orWhere('description', 'like', "%{$searchTerm}%")
                  ->orWhere('department', 'like', "%{$searchTerm}%")
                  ->orWhere('location', 'like', "%{$searchTerm}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->where('is_active', true)->where('is_deleted', false);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false)->where('is_deleted', false);
            } elseif ($request->status === 'deleted') {
                $query->where('is_deleted', true);
            }
        } else {
            // Default: show only non-deleted jobs
            $query->where('is_deleted', false);
        }

        // Filter by employment type
        if ($request->filled('employment_type') && $request->employment_type !== 'all') {
            $query->where('employment_type', $request->employment_type);
        }

        // Filter by department
        if ($request->filled('department') && $request->department !== 'all') {
            $query->where('department', $request->department);
        }

        $jobs = $query->with(['jobApplications'])
                     ->orderBy('is_featured', 'desc')
                     ->orderBy('sort_order', 'asc')
                     ->orderBy('created_at', 'desc')
                     ->paginate(15);

        // Get filter options
        $employmentTypes = Job::getEmploymentTypeOptions();
        $departments = Job::where('is_deleted', false)->distinct()->pluck('department')->filter()->sort();

        return view('admin.jobs.index', compact('jobs', 'employmentTypes', 'departments'));
    }

    /**
     * Show the form for creating a new job.
     */
    public function create(): View
    {
        $employmentTypes = Job::getEmploymentTypeOptions();
        $experienceLevels = Job::getExperienceLevelOptions();
        $salaryPeriods = Job::getSalaryPeriodOptions();

        return view('admin.jobs.create', compact('employmentTypes', 'experienceLevels', 'salaryPeriods'));
    }

    /**
     * Store a newly created job.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:career_jobs,slug',
            'description' => 'required|string',
            'requirements' => 'required|string',
            'responsibilities' => 'required|string',
            'location' => 'required|string|max:255',
            'employment_type' => 'required|string|in:' . implode(',', array_keys(Job::getEmploymentTypeOptions())),
            'experience_level' => 'required|string|in:' . implode(',', array_keys(Job::getExperienceLevelOptions())),
            'department' => 'required|string|max:255',
            'salary_min' => 'nullable|numeric|min:0',
            'salary_max' => 'nullable|numeric|min:0|gte:salary_min',
            'salary_currency' => 'nullable|string|max:3',
            'salary_period' => 'required|string|in:' . implode(',', array_keys(Job::getSalaryPeriodOptions())),
            'benefits' => 'nullable|string',
            'application_deadline' => 'nullable|date|after:today',
            'is_remote' => 'boolean',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:500',
        ]);

        // Generate slug if not provided
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['title']);
        }

        // Set defaults
        $validated['is_remote'] = $request->has('is_remote');
        $validated['is_featured'] = $request->has('is_featured');
        $validated['is_active'] = $request->has('is_active');
        $validated['salary_currency'] = $validated['salary_currency'] ?? 'ZAR';
        $validated['sort_order'] = $validated['sort_order'] ?? 0;

        Job::create($validated);

        return redirect()->route('admin.jobs.index')
                        ->with('success', 'Job created successfully.');
    }

    /**
     * Display the specified job.
     */
    public function show(Job $job): View
    {
        $job->load(['jobApplications.user']);

        return view('admin.jobs.show', compact('job'));
    }

    /**
     * Show the form for editing the specified job.
     */
    public function edit(Job $job): View
    {
        $employmentTypes = Job::getEmploymentTypeOptions();
        $experienceLevels = Job::getExperienceLevelOptions();
        $salaryPeriods = Job::getSalaryPeriodOptions();

        return view('admin.jobs.edit', compact('job', 'employmentTypes', 'experienceLevels', 'salaryPeriods'));
    }

    /**
     * Update the specified job.
     */
    public function update(Request $request, Job $job): RedirectResponse
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:career_jobs,slug,' . $job->id,
            'description' => 'required|string',
            'requirements' => 'required|string',
            'responsibilities' => 'required|string',
            'location' => 'required|string|max:255',
            'employment_type' => 'required|string|in:' . implode(',', array_keys(Job::getEmploymentTypeOptions())),
            'experience_level' => 'required|string|in:' . implode(',', array_keys(Job::getExperienceLevelOptions())),
            'department' => 'required|string|max:255',
            'salary_min' => 'nullable|numeric|min:0',
            'salary_max' => 'nullable|numeric|min:0|gte:salary_min',
            'salary_currency' => 'nullable|string|max:3',
            'salary_period' => 'required|string|in:' . implode(',', array_keys(Job::getSalaryPeriodOptions())),
            'benefits' => 'nullable|string',
            'application_deadline' => 'nullable|date|after:today',
            'is_remote' => 'boolean',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:500',
        ]);

        // Generate slug if not provided
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['title']);
        }

        // Set defaults
        $validated['is_remote'] = $request->has('is_remote');
        $validated['is_featured'] = $request->has('is_featured');
        $validated['is_active'] = $request->has('is_active');
        $validated['salary_currency'] = $validated['salary_currency'] ?? 'ZAR';
        $validated['sort_order'] = $validated['sort_order'] ?? 0;

        $job->update($validated);

        return redirect()->route('admin.jobs.index')
                        ->with('success', 'Job updated successfully.');
    }

    /**
     * Remove the specified job (soft delete).
     */
    public function destroy(Job $job): RedirectResponse
    {
        $job->update(['is_deleted' => true]);

        return redirect()->route('admin.jobs.index')
                        ->with('success', 'Job deleted successfully.');
    }

    /**
     * Toggle featured status.
     */
    public function toggleFeatured(Job $job): JsonResponse
    {
        $job->update(['is_featured' => !$job->is_featured]);

        return response()->json([
            'success' => true,
            'is_featured' => $job->is_featured,
            'message' => $job->is_featured ? 'Job marked as featured.' : 'Job removed from featured.'
        ]);
    }

    /**
     * Toggle active status.
     */
    public function toggleActive(Job $job): JsonResponse
    {
        $job->update(['is_active' => !$job->is_active]);

        return response()->json([
            'success' => true,
            'is_active' => $job->is_active,
            'message' => $job->is_active ? 'Job activated.' : 'Job deactivated.'
        ]);
    }
}
