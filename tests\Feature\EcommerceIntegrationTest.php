<?php

namespace Tests\Feature;

use App\Models\Coupon;
use App\Models\Order;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\ShoppingCart;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class EcommerceIntegrationTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function complete_ecommerce_flow_works()
    {
        // 1. Create test data
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        $category = ProductCategory::factory()->create([
            'name' => 'Electronics',
            'is_active' => true,
        ]);

        $product = Product::factory()->create([
            'name' => 'Test Laptop',
            'price' => 1000.00,
            'is_active' => true,
            'track_inventory' => false,
        ]);

        $product->categories()->attach($category);

        $coupon = Coupon::factory()->create([
            'code' => 'SAVE10',
            'type' => 'percentage',
            'value' => 10,
            'is_active' => true,
        ]);

        // 2. Browse shop and view product
        $this->get('/shop')
            ->assertStatus(200)
            ->assertSee($product->name);

        $this->get("/shop/product/{$product->slug}")
            ->assertStatus(200)
            ->assertSee($product->name)
            ->assertSee($product->formatted_price);

        // 3. Add product to cart
        $this->actingAs($user)
            ->postJson('/cart/add', [
                'product_id' => $product->id,
                'quantity' => 1,
            ])
            ->assertJson(['success' => true]);

        // 4. View cart
        $this->actingAs($user)
            ->get('/cart')
            ->assertStatus(200)
            ->assertSee($product->name);

        // 5. Apply coupon
        $this->actingAs($user)
            ->postJson('/cart/coupon', ['coupon_code' => 'SAVE10'])
            ->assertJson(['success' => true]);

        // 6. Proceed to checkout
        $this->actingAs($user)
            ->get('/checkout')
            ->assertStatus(200);

        // 7. Complete checkout
        $checkoutData = [
            'billing_first_name' => 'John',
            'billing_last_name' => 'Doe',
            'billing_email' => '<EMAIL>',
            'billing_phone' => '+27123456789',
            'billing_address_line_1' => '123 Main St',
            'billing_city' => 'Cape Town',
            'billing_state' => 'Western Cape',
            'billing_postal_code' => '8001',
            'billing_country' => 'South Africa',
            'shipping_first_name' => 'John',
            'shipping_last_name' => 'Doe',
            'shipping_address_line_1' => '123 Main St',
            'shipping_city' => 'Cape Town',
            'shipping_state' => 'Western Cape',
            'shipping_postal_code' => '8001',
            'shipping_country' => 'South Africa',
            'payment_method' => 'stripe',
            'terms_accepted' => true,
        ];

        $response = $this->actingAs($user)
            ->post('/checkout/process', $checkoutData);

        $response->assertRedirect();

        // 8. Verify order was created
        $order = Order::where('user_id', $user->id)->first();
        $this->assertNotNull($order);
        $this->assertEquals('pending', $order->payment_status);
        $this->assertEquals(1, $order->items->count());
        
        // Verify coupon was applied
        $this->assertEquals('SAVE10', $order->coupon_code);
        $this->assertEquals(100.00, $order->discount_amount); // 10% of 1000
        
        // 9. View payment page
        $this->actingAs($user)
            ->get("/checkout/payment/{$order->uuid}")
            ->assertStatus(200)
            ->assertSee($order->formatted_total);

        // 10. Simulate successful payment
        $order->update([
            'payment_status' => 'paid',
            'status' => 'processing',
        ]);

        // 11. View success page
        $this->actingAs($user)
            ->get("/checkout/success/{$order->uuid}")
            ->assertStatus(200)
            ->assertSee('Order Confirmed!')
            ->assertSee($order->order_number);

        // 12. Verify cart was cleared
        $cart = ShoppingCart::where('user_id', $user->id)->first();
        $this->assertNull($cart);
    }

    /** @test */
    public function guest_checkout_flow_works()
    {
        // 1. Create test data
        $category = ProductCategory::factory()->create([
            'name' => 'Books',
            'is_active' => true,
        ]);

        $product = Product::factory()->create([
            'name' => 'Test Book',
            'price' => 50.00,
            'is_active' => true,
            'track_inventory' => false,
        ]);

        $product->categories()->attach($category);

        // 2. Add product to cart as guest
        $this->postJson('/cart/add', [
            'product_id' => $product->id,
            'quantity' => 2,
        ])
        ->assertJson(['success' => true]);

        // 3. View cart
        $this->get('/cart')
            ->assertStatus(200)
            ->assertSee($product->name);

        // 4. Proceed to checkout
        $this->get('/checkout')
            ->assertStatus(200);

        // 5. Complete checkout as guest
        $checkoutData = [
            'billing_first_name' => 'Jane',
            'billing_last_name' => 'Smith',
            'billing_email' => '<EMAIL>',
            'billing_phone' => '+27987654321',
            'billing_address_line_1' => '456 Oak Ave',
            'billing_city' => 'Johannesburg',
            'billing_state' => 'Gauteng',
            'billing_postal_code' => '2000',
            'billing_country' => 'South Africa',
            'shipping_first_name' => 'Jane',
            'shipping_last_name' => 'Smith',
            'shipping_address_line_1' => '456 Oak Ave',
            'shipping_city' => 'Johannesburg',
            'shipping_state' => 'Gauteng',
            'shipping_postal_code' => '2000',
            'shipping_country' => 'South Africa',
            'payment_method' => 'stripe',
            'terms_accepted' => true,
            'create_account' => false,
        ];

        $response = $this->post('/checkout/process', $checkoutData);
        $response->assertRedirect();

        // 6. Verify guest order was created
        $order = Order::where('email', '<EMAIL>')->first();
        $this->assertNotNull($order);
        $this->assertNull($order->user_id); // Guest order
        $this->assertEquals(2, $order->items->count());
        $this->assertEquals(100.00, $order->subtotal); // 2 * 50
    }

    /** @test */
    public function admin_can_manage_orders()
    {
        // Create admin user
        $admin = User::factory()->create();
        $admin->assignRole('admin');

        // Create test order
        $customer = User::factory()->create();
        $order = Order::factory()->create([
            'user_id' => $customer->id,
            'status' => 'pending',
            'payment_status' => 'pending',
        ]);

        // Admin can view orders list
        $this->actingAs($admin)
            ->get('/admin/orders')
            ->assertStatus(200)
            ->assertSee($order->order_number);

        // Admin can view order details
        $this->actingAs($admin)
            ->get("/admin/orders/{$order->id}")
            ->assertStatus(200)
            ->assertSee($order->order_number);

        // Admin can update order status
        $this->actingAs($admin)
            ->patch("/admin/orders/{$order->id}/status", [
                'status' => 'processing',
            ])
            ->assertRedirect();

        $order->refresh();
        $this->assertEquals('processing', $order->status);
    }

    /** @test */
    public function admin_can_manage_coupons()
    {
        // Create admin user
        $admin = User::factory()->create();
        $admin->assignRole('admin');

        // Admin can view coupons list
        $this->actingAs($admin)
            ->get('/admin/coupons')
            ->assertStatus(200);

        // Admin can create coupon
        $couponData = [
            'name' => 'Test Coupon',
            'code' => 'TEST20',
            'type' => 'percentage',
            'value' => 20,
            'is_active' => true,
        ];

        $this->actingAs($admin)
            ->post('/admin/coupons', $couponData)
            ->assertRedirect();

        $coupon = Coupon::where('code', 'TEST20')->first();
        $this->assertNotNull($coupon);
        $this->assertEquals('Test Coupon', $coupon->name);

        // Admin can edit coupon
        $this->actingAs($admin)
            ->get("/admin/coupons/{$coupon->id}/edit")
            ->assertStatus(200);

        $this->actingAs($admin)
            ->put("/admin/coupons/{$coupon->id}", [
                'name' => 'Updated Coupon',
                'code' => 'TEST20',
                'type' => 'percentage',
                'value' => 25,
                'is_active' => true,
            ])
            ->assertRedirect();

        $coupon->refresh();
        $this->assertEquals('Updated Coupon', $coupon->name);
        $this->assertEquals(25, $coupon->value);
    }

    /** @test */
    public function shop_pages_are_accessible()
    {
        $category = ProductCategory::factory()->create([
            'name' => 'Test Category',
            'is_active' => true,
        ]);

        $product = Product::factory()->create([
            'name' => 'Test Product',
            'is_active' => true,
        ]);

        $product->categories()->attach($category);

        // Shop index
        $this->get('/shop')
            ->assertStatus(200)
            ->assertSee('Test Product');

        // Category page
        $this->get("/shop/category/{$category->slug}")
            ->assertStatus(200)
            ->assertSee($category->name)
            ->assertSee('Test Product');

        // Product page
        $this->get("/shop/product/{$product->slug}")
            ->assertStatus(200)
            ->assertSee($product->name);
    }
}
