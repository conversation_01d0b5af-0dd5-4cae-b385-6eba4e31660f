<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\RegisterRequest;
use App\Models\User;
use App\Models\Role;
use App\Notifications\WelcomeNotification;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\View\View;
use Illuminate\Auth\Events\Registered;

class RegisterController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('guest');
    }

    /**
     * Show the application registration form.
     */
    public function showRegistrationForm(): View
    {
        return view('auth.register');
    }

    /**
     * Handle a registration request for the application.
     */
    public function register(RegisterRequest $request): JsonResponse|RedirectResponse
    {
        try {
            DB::beginTransaction();

            $user = $this->create($request->validated());

            event(new Registered($user));

            Auth::login($user);

            DB::commit();

            // Handle AJAX requests
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => __('auth.registration_success'),
                    'redirect' => $this->redirectPath(),
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->full_name,
                        'email' => $user->email,
                        'role' => $user->role->name,
                    ]
                ]);
            }

            // Handle regular form submissions
            return $this->registered($request, $user)
                ?: redirect($this->redirectPath())->with('success', __('auth.registration_success'));

        } catch (\Exception $e) {
            DB::rollBack();

            \Log::error('Registration failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'email' => $request->input('email'),
                'ip' => $request->ip(),
                'request_data' => $request->except(['password', 'password_confirmation']),
            ]);

            // Handle AJAX requests
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => __('auth.registration_failed') . ' Error: ' . $e->getMessage(),
                    'errors' => ['general' => [__('auth.registration_failed') . ' Error: ' . $e->getMessage()]]
                ], 422);
            }

            // Handle regular form submissions
            return back()->withInput($request->except('password', 'password_confirmation'))
                        ->withErrors(['email' => __('auth.registration_failed')]);
        }
    }

    /**
     * Create a new user instance after a valid registration.
     */
    protected function create(array $data): User
    {
        // Get the default customer role
        $customerRole = Role::where('name', 'customer')->first();
        
        if (!$customerRole) {
            throw new \Exception('Customer role not found. Please contact administrator.');
        }

        return User::create([
            'first_name' => $data['first_name'],
            'last_name' => $data['last_name'],
            'email' => $data['email'],
            'password' => $data['password'], // Will be hashed automatically
            'phone' => $data['phone'] ?? null,
            'role_id' => $customerRole->id,
            'is_active' => true,
            'is_deleted' => false,
        ]);
    }

    /**
     * The user has been registered.
     */
    protected function registered(Request $request, User $user): ?RedirectResponse
    {
        // Send welcome email notification
        $user->notify(new WelcomeNotification());

        // Log successful registration
        \Log::info('User registered successfully', [
            'user_id' => $user->id,
            'email' => $user->email,
            'role' => $user->role->name,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);

        return null; // Return null to use default redirect
    }

    /**
     * Where to redirect users after registration.
     */
    protected function redirectPath(): string
    {
        $user = auth()->user();

        // Role-based redirection
        if ($user->isAdminOrStaff()) {
            return route('admin.dashboard');
        }

        // New users are typically customers, redirect to customer/client dashboard
        return route('dashboard');
    }
}
