@extends('emails.layout')

@section('title', 'Order Status Update - ' . $order->order_number)

@section('content')
<div class="greeting">
    Order Update for {{ $customer_name }}
</div>

<p class="content-text">
    We have an update on your order <strong>#{{ $order->order_number }}</strong>.
</p>

<!-- Status Update Box -->
<div style="background-color: {{ $order->status === 'shipped' ? '#f0fff4' : ($order->status === 'delivered' ? '#f0fff4' : '#f7fafc') }}; 
            border: 1px solid {{ $order->status === 'shipped' ? '#9ae6b4' : ($order->status === 'delivered' ? '#9ae6b4' : '#e2e8f0') }}; 
            border-radius: 8px; padding: 20px; margin: 20px 0;">
    
    <h3 style="margin: 0 0 10px 0; color: #2d3748; font-size: 18px;">
        Status: 
        <span style="color: {{ $order->status === 'shipped' ? '#38a169' : ($order->status === 'delivered' ? '#38a169' : '#3182ce') }}; text-transform: capitalize;">
            {{ str_replace('_', ' ', $order->status) }}
        </span>
    </h3>
    
    <p style="margin: 0; color: #4a5568; font-size: 16px; line-height: 1.5;">
        {{ $status_message }}
    </p>
</div>

<!-- Order Summary -->
<div style="background-color: #f7fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px; margin: 20px 0;">
    <h3 style="margin: 0 0 15px 0; color: #2d3748; font-size: 18px;">Order Details</h3>
    
    <table style="width: 100%; border-collapse: collapse;">
        <tr>
            <td style="padding: 8px 0; color: #4a5568; font-weight: 600;">Order Number:</td>
            <td style="padding: 8px 0; color: #2d3748; text-align: right;">#{{ $order->order_number }}</td>
        </tr>
        <tr>
            <td style="padding: 8px 0; color: #4a5568; font-weight: 600;">Order Date:</td>
            <td style="padding: 8px 0; color: #2d3748; text-align: right;">{{ $order->created_at->format('M d, Y') }}</td>
        </tr>
        <tr>
            <td style="padding: 8px 0; color: #4a5568; font-weight: 600;">Total Amount:</td>
            <td style="padding: 8px 0; color: #2d3748; text-align: right; font-weight: 600;">{{ $order->formatted_total }}</td>
        </tr>
        @if($order->tracking_number)
        <tr>
            <td style="padding: 8px 0; color: #4a5568; font-weight: 600;">Tracking Number:</td>
            <td style="padding: 8px 0; color: #2d3748; text-align: right; font-family: monospace;">{{ $order->tracking_number }}</td>
        </tr>
        @endif
    </table>
</div>

<!-- Items in Order -->
<h3 style="color: #2d3748; margin: 30px 0 15px 0;">Items in This Order</h3>

@foreach($order->items as $item)
<div style="border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; margin-bottom: 10px; background-color: #ffffff;">
    <table style="width: 100%;">
        <tr>
            <td style="width: 70%;">
                <h4 style="margin: 0 0 5px 0; color: #2d3748; font-size: 16px;">{{ $item->product_name }}</h4>
                @if($item->variant_name)
                <p style="margin: 0 0 5px 0; color: #718096; font-size: 14px;">{{ $item->variant_name }}</p>
                @endif
                <p style="margin: 0; color: #4a5568; font-size: 14px;">Quantity: {{ $item->quantity }}</p>
            </td>
            <td style="width: 30%; text-align: right; vertical-align: top;">
                <p style="margin: 0; color: #2d3748; font-weight: 600; font-size: 16px;">{{ $item->formatted_total_price }}</p>
            </td>
        </tr>
    </table>
</div>
@endforeach

@if($order->status === 'shipped' && $order->tracking_number)
<!-- Tracking Information -->
<div class="info-box">
    <p><strong>Track Your Package</strong></p>
    <p>
        Your order has been shipped! You can track your package using the tracking number: 
        <strong>{{ $order->tracking_number }}</strong>
    </p>
    <p>
        Estimated delivery: 3-5 business days from ship date.
    </p>
</div>
@endif

@if($order->status === 'delivered')
<!-- Delivery Confirmation -->
<div style="background-color: #f0fff4; border-left: 4px solid #38a169; padding: 20px; margin: 20px 0; border-radius: 0 8px 8px 0;">
    <p style="margin: 0; color: #2f855a; font-size: 16px; font-weight: 600;">
        🎉 Your order has been delivered!
    </p>
    <p style="margin: 10px 0 0 0; color: #2f855a; font-size: 14px;">
        We hope you love your purchase! If you have any issues, please contact our support team.
    </p>
</div>
@endif

@if($order->status === 'cancelled')
<!-- Cancellation Information -->
<div class="warning-box">
    <p><strong>Order Cancelled</strong></p>
    <p>
        Your order has been cancelled. If you have any questions about this cancellation or need assistance with a new order, please contact our support team.
    </p>
    @if($order->payment_status === 'paid')
    <p>
        <strong>Refund Information:</strong> Your refund will be processed within 3-5 business days and will appear in your original payment method.
    </p>
    @endif
</div>
@endif

<!-- Action Buttons -->
<div class="button-container">
    @if($order->user_id)
    <a href="{{ route('orders.show', $order->uuid) }}" class="btn">
        View Full Order Details
    </a>
    @endif
    
    @if($order->status !== 'cancelled')
    <a href="{{ route('shop.index') }}" class="btn btn-secondary" style="margin-left: 10px;">
        Shop More Items
    </a>
    @endif
</div>

<!-- Contact Information -->
<p class="content-text">
    If you have any questions about your order or need assistance, please don't hesitate to contact our support team at 
    <a href="mailto:{{ config('mail.from.address') }}" style="color: #667eea;">{{ config('mail.from.address') }}</a>.
</p>

<p class="content-text">
    Thank you for choosing {{ $company_name }}!
</p>
@endsection
