<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Currency extends Model
{
    use HasFactory;
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'code',
        'name',
        'symbol',
        'exchange_rate',
        'is_default',
        'is_active',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'exchange_rate' => 'decimal:4',
            'is_default' => 'boolean',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Scope a query to only include active currencies.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get the default currency.
     */
    public static function getDefault()
    {
        return static::where('is_default', true)->first();
    }

    /**
     * Get currency by code.
     */
    public static function findByCode(string $code)
    {
        return static::where('code', $code)->where('is_active', true)->first();
    }

    /**
     * Convert amount from base currency to this currency.
     */
    public function convertFromBase(float $amount): float
    {
        return $amount * $this->exchange_rate;
    }

    /**
     * Convert amount from this currency to base currency.
     */
    public function convertToBase(float $amount): float
    {
        return $amount / $this->exchange_rate;
    }

    /**
     * Format amount with currency symbol.
     */
    public function formatAmount(float $amount): string
    {
        $convertedAmount = $this->convertFromBase($amount);
        return $this->symbol . ' ' . number_format($convertedAmount, 2);
    }
}
