<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\ForgotPasswordRequest;
use App\Http\Requests\Auth\ResetPasswordRequest;
use App\Models\User;
use App\Notifications\ResetPasswordNotification;
use App\Services\ActivityLogger;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Str;
use Illuminate\View\View;
use Illuminate\Validation\ValidationException;

class ForgotPasswordController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('guest');
    }

    /**
     * Display the form to request a password reset link.
     */
    public function showLinkRequestForm(): View
    {
        return view('auth.forgot-password');
    }

    /**
     * Send a reset link to the given user.
     */
    public function sendResetLinkEmail(ForgotPasswordRequest $request): JsonResponse|RedirectResponse
    {
        $email = $request->input('email');
        $emailExists = User::where('email', $email)->exists();
        $wasRateLimited = false;

        // Check rate limiting
        try {
            $this->checkTooManyAttempts($request);
        } catch (ValidationException $e) {
            $wasRateLimited = true;

            // Log the rate-limited attempt
            app(ActivityLogger::class)->logPasswordResetRequest(
                $email,
                $emailExists,
                true,
                'Rate limited - too many requests'
            );

            throw $e; // Re-throw the rate limiting exception
        }

        // Always attempt to send the password reset link
        // This prevents email enumeration attacks by not revealing if email exists
        $status = Password::sendResetLink(
            $request->only('email')
        );

        // Log the password reset request
        app(ActivityLogger::class)->logPasswordResetRequest(
            $email,
            $emailExists,
            false
        );

        // Always clear attempts and return success message
        // This prevents attackers from determining if an email exists in the system
        $this->clearAttempts($request);

        $successMessage = __('passwords.sent_secure');

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => $successMessage,
            ]);
        }

        return back()->with('success', $successMessage);
    }

    /**
     * Display the password reset view for the given token.
     */
    public function showResetForm(Request $request, string $token): View
    {
        return view('auth.reset-password', [
            'token' => $token,
            'email' => $request->email
        ]);
    }

    /**
     * Reset the given user's password.
     */
    public function reset(ResetPasswordRequest $request): JsonResponse|RedirectResponse
    {
        $email = $request->input('email');
        $token = $request->input('token');

        // Check rate limiting
        try {
            $this->checkTooManyResetAttempts($request);
        } catch (ValidationException $e) {
            // Log the rate-limited reset attempt
            app(ActivityLogger::class)->logPasswordResetFailed(
                $email,
                'Rate limited - too many reset attempts',
                $token
            );
            throw $e;
        }

        $resetUser = null;
        $status = Password::reset(
            $request->only('email', 'password', 'password_confirmation', 'token'),
            function (User $user, string $password) use (&$resetUser) {
                $resetUser = $user;
                $this->resetPassword($user, $password);
            }
        );

        if ($status === Password::PASSWORD_RESET) {
            $this->clearResetAttempts($request);

            // Log successful password reset
            if ($resetUser) {
                app(ActivityLogger::class)->logPasswordResetSuccess($resetUser, $token);
            }

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => __('passwords.reset'),
                ]);
            }

            return redirect()->route('login')->with('success', __('passwords.reset'));
        }

        // If we reach here, the password reset failed
        $this->incrementResetAttempts($request);

        // Log failed password reset
        $failureReason = $this->getFailureReason($status);
        app(ActivityLogger::class)->logPasswordResetFailed($email, $failureReason, $token);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => false,
                'message' => __('passwords.token'),
                'errors' => ['email' => [__('passwords.token')]]
            ], 422);
        }

        throw ValidationException::withMessages([
            'email' => [__('passwords.token')],
        ]);
    }

    /**
     * Reset the given user's password.
     */
    protected function resetPassword(User $user, string $password): void
    {
        $user->forceFill([
            'password' => Hash::make($password),
            'remember_token' => Str::random(60),
        ])->save();

        // Log the user out of all devices
        DB::table('sessions')->where('user_id', $user->id)->delete();
    }

    /**
     * Check if too many password reset link requests have been made.
     */
    protected function checkTooManyAttempts(Request $request): void
    {
        $key = $this->throttleKey($request);

        if (RateLimiter::tooManyAttempts($key, 5)) {
            $seconds = RateLimiter::availableIn($key);

            throw ValidationException::withMessages([
                'email' => [__('passwords.throttled', ['seconds' => $seconds])],
            ]);
        }
    }

    /**
     * Check if too many password reset attempts have been made.
     */
    protected function checkTooManyResetAttempts(Request $request): void
    {
        $key = $this->resetThrottleKey($request);

        if (RateLimiter::tooManyAttempts($key, 5)) {
            $seconds = RateLimiter::availableIn($key);

            throw ValidationException::withMessages([
                'email' => [__('passwords.throttled', ['seconds' => $seconds])],
            ]);
        }
    }

    /**
     * Increment the login attempts for the user.
     */
    protected function incrementAttempts(Request $request): void
    {
        RateLimiter::hit($this->throttleKey($request), 3600); // 1 hour
    }

    /**
     * Increment the reset attempts for the user.
     */
    protected function incrementResetAttempts(Request $request): void
    {
        RateLimiter::hit($this->resetThrottleKey($request), 3600); // 1 hour
    }

    /**
     * Clear the login attempts for the user.
     */
    protected function clearAttempts(Request $request): void
    {
        RateLimiter::clear($this->throttleKey($request));
    }

    /**
     * Clear the reset attempts for the user.
     */
    protected function clearResetAttempts(Request $request): void
    {
        RateLimiter::clear($this->resetThrottleKey($request));
    }

    /**
     * Get the throttle key for the given request.
     */
    protected function throttleKey(Request $request): string
    {
        return 'password_reset_' . strtolower($request->input('email')) . '|' . $request->ip();
    }

    /**
     * Get the reset throttle key for the given request.
     */
    protected function resetThrottleKey(Request $request): string
    {
        return 'password_reset_attempt_' . strtolower($request->input('email')) . '|' . $request->ip();
    }

    /**
     * Get human-readable failure reason from password reset status.
     */
    protected function getFailureReason(string $status): string
    {
        return match ($status) {
            Password::INVALID_TOKEN => 'Invalid or expired token',
            Password::INVALID_USER => 'Invalid email address',
            Password::THROTTLED => 'Too many attempts',
            default => 'Password reset failed',
        };
    }
}
