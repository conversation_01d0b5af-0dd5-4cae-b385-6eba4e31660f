

<div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Products</h1>
            <p class="text-gray-600">Manage your product catalog</p>
        </div>
        <div class="flex space-x-3">
            <a href="http://localhost:8000/admin/categories" 
               class="inline-flex items-center px-4 py-2 bg-neutral-600 text-white rounded-lg hover:bg-neutral-700 transition-colors duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                </svg>
                Categories
            </a>
            <a href="http://localhost:8000/admin/products/create" 
               class="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                </svg>
                Add Product
            </a>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <span class="text-white text-sm font-bold">📦</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Products</dt>
                            <dd class="text-lg font-medium text-gray-900">0</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <span class="text-white text-sm font-bold">✅</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Active</dt>
                            <dd class="text-lg font-medium text-gray-900">0</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                            <span class="text-white text-sm font-bold">⭐</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Featured</dt>
                            <dd class="text-lg font-medium text-gray-900">0</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                            <span class="text-white text-sm font-bold">📉</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Low Stock</dt>
                            <dd class="text-lg font-medium text-gray-900">0</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
        <form method="GET" action="http://localhost:8000/admin/products" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Search -->
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                    <input type="text" 
                           id="search" 
                           name="search" 
                           value=""
                           class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                           placeholder="Search products...">
                </div>

                <!-- Category Filter -->
                <div>
                    <label for="category" class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                    <select id="category" 
                            name="category" 
                            class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                        <option value="">All Categories</option>
                                            </select>
                </div>

                <!-- Status Filter -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select id="status" 
                            name="status" 
                            class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                        <option value="">All Status</option>
                        <option value="active" >Active</option>
                        <option value="inactive" >Inactive</option>
                    </select>
                </div>

                <!-- Stock Filter -->
                <div>
                    <label for="stock" class="block text-sm font-medium text-gray-700 mb-2">Stock</label>
                    <select id="stock" 
                            name="stock" 
                            class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                        <option value="">All Stock</option>
                        <option value="in_stock" >In Stock</option>
                        <option value="low_stock" >Low Stock</option>
                        <option value="out_of_stock" >Out of Stock</option>
                    </select>
                </div>
            </div>

            <div class="flex items-center space-x-3">
                <button type="submit" 
                        class="px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200">
                    Apply Filters
                </button>
                <a href="http://localhost:8000/admin/products" 
                   class="px-6 py-2 border border-neutral-300 text-neutral-700 rounded-lg hover:bg-neutral-50 transition-colors duration-200">
                    Clear Filters
                </a>
            </div>
        </form>
    </div>

    <!-- Products Table -->
    <div class="bg-white rounded-xl shadow-soft border border-neutral-100 overflow-hidden">
        <div class="px-6 py-4 border-b border-neutral-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">
                    Products (0)
                </h3>
                <div class="text-sm text-gray-500">
                    Showing 0 to 0 of 0 results
                </div>
            </div>
        </div>
        
                    <div class="p-12 text-center">
                <div class="w-16 h-16 bg-neutral-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No products found</h3>
                <p class="text-gray-500 mb-6">Get started by creating your first product.</p>
                <a href="http://localhost:8000/admin/products/create" 
                   class="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                    </svg>
                    Create Product
                </a>
            </div>
            </div>
</div>


<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="re0QwHah6LRhmZ3a0LPsssPlCHcVnoByw93M3595">

    <!-- SEO Meta Tags -->
    <title>Products - Admin Dashboard</title>
    <meta name="description" content="Manage your account, orders, and preferences">
    <meta name="robots" content="noindex, nofollow">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="http://localhost:8000/favicon.ico">

    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Styles -->
    <link rel="preload" as="style" href="http://localhost:8000/build/assets/app-D9hANXTT.css" /><link rel="modulepreload" href="http://localhost:8000/build/assets/app-DNxiirP_.js" /><link rel="stylesheet" href="http://localhost:8000/build/assets/app-D9hANXTT.css" /><script type="module" src="http://localhost:8000/build/assets/app-DNxiirP_.js"></script>
    <!-- Admin Components CSS -->
    <link href="http://localhost:8000/css/admin-components.css" rel="stylesheet">

    <!-- Additional Styles -->
    </head>
<body class="font-inter antialiased bg-neutral-50 text-primary-900">
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-accent-600 text-white px-4 py-2 rounded-md z-50">
        Skip to main content
    </a>

    <div class="min-h-screen bg-neutral-50">
        <!-- Sidebar -->
        <aside class="fixed inset-y-0 left-0 z-50 w-64 bg-white border-r border-neutral-200 transform -translate-x-full transition-transform duration-300 ease-in-out lg:translate-x-0 shadow-lg" id="dashboard-sidebar">
    
    <div class="flex items-center justify-center h-16 px-6 bg-gradient-to-r from-primary-800 to-primary-700 border-b border-primary-600">
        <a href="http://localhost:8000" class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center backdrop-blur-sm">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 2L3 7v11a1 1 0 001 1h3v-7h6v7h3a1 1 0 001-1V7l-7-5z"/>
                </svg>
            </div>
            <span class="text-lg font-semibold text-white">ChiSolution</span>
        </a>
    </div>

    
    <div class="p-6 bg-gradient-to-br from-neutral-50 to-primary-50 border-b border-neutral-200">
        <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-gradient-to-br from-primary-600 to-primary-700 rounded-full flex items-center justify-center shadow-md">
                <span class="text-sm font-medium text-white">
                    AU
                </span>
            </div>
            <div class="flex-1 min-w-0">
                <p class="text-sm font-semibold text-primary-900 truncate">
                    Admin User
                </p>
                <p class="text-xs text-primary-600 truncate">
                    Admin
                </p>
            </div>
        </div>
    </div>

    
    <nav class="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
        
        <a href="http://localhost:8000/admin"
           class="nav-item ">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"/>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"/>
            </svg>
            <span>Dashboard</span>
        </a>

                    
            <div class="pt-4">
                <h3 class="px-3 text-xs font-semibold text-primary-500 uppercase tracking-wider">Ecommerce</h3>
                <div class="mt-2 space-y-1">
                    <a href="http://localhost:8000/admin/categories"
                       class="nav-item ">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                        </svg>
                        <span>Categories</span>
                    </a>

                    <a href="http://localhost:8000/admin/products"
                       class="nav-item nav-item-active">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>
                        </svg>
                        <span>Products</span>
                    </a>

                    <a href="http://localhost:8000/admin/orders"
                       class="nav-item ">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                        <span>Orders</span>
                    </a>

                    <a href="http://localhost:8000/admin/coupons"
                       class="nav-item ">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
                        </svg>
                        <span>Coupons</span>
                    </a>
                </div>
            </div>

            <div class="pt-4">
                <h3 class="px-3 text-xs font-semibold text-primary-500 uppercase tracking-wider">Management</h3>
                <div class="mt-2 space-y-1">
                    <a href="http://localhost:8000/admin/users"
                       class="nav-item ">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
                        </svg>
                        <span>Users</span>
                    </a>

                    <a href="http://localhost:8000/admin/activity-logs"
                       class="nav-item ">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                        <span>Activity Logs</span>
                    </a>
                </div>
            </div>
        
        
        <div class="pt-4">
            <h3 class="px-3 text-xs font-semibold text-primary-500 uppercase tracking-wider">Account</h3>
            <div class="mt-2 space-y-1">
                <a href="http://localhost:8000/profile/edit" 
                   class="nav-item ">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                    </svg>
                    <span>Profile Settings</span>
                </a>

                <a href="http://localhost:8000" 
                   class="nav-item">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                    </svg>
                    <span>Back to Website</span>
                </a>
            </div>
        </div>
    </nav>

    
    <div class="p-4 border-t border-neutral-200">
        <form method="POST" action="http://localhost:8000/logout">
            <input type="hidden" name="_token" value="re0QwHah6LRhmZ3a0LPsssPlCHcVnoByw93M3595" autocomplete="off">            <button type="submit" class="w-full flex items-center px-3 py-2 text-sm font-medium text-danger-600 rounded-lg hover:bg-danger-50 hover:text-danger-700 transition-colors duration-200">
                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                </svg>
                <span>Sign Out</span>
            </button>
        </form>
    </div>
</aside>


<style>
.nav-item {
    @apply flex items-center px-3 py-2.5 text-sm font-medium text-neutral-700 rounded-lg hover:bg-gradient-to-r hover:from-primary-50 hover:to-secondary-50 hover:text-primary-800 transition-all duration-200 group;
}

.nav-item-active {
    @apply bg-gradient-to-r from-primary-100 to-secondary-100 text-primary-900 border-l-4 border-secondary-600 shadow-sm;
}

.nav-item svg {
    @apply mr-3 flex-shrink-0 group-hover:text-primary-700;
}

.nav-item-active svg {
    @apply text-primary-800;
}
</style>

        <!-- Main Content -->
        <div class="lg:ml-64 min-h-screen flex flex-col">
            <!-- Header -->
            <header class="bg-white border-b border-neutral-200 sticky top-0 z-40 shadow-sm">
    <div class="flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8">
        
        <button type="button"
                class="lg:hidden p-2 rounded-lg text-primary-600 hover:text-primary-800 hover:bg-gradient-to-r hover:from-primary-50 hover:to-secondary-50 transition-all duration-200"
                id="mobile-menu-button">
            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
            </svg>
        </button>
        
        
        <div class="flex-1 min-w-0 ml-4 lg:ml-0">
            <div class="flex items-center space-x-2">
                <h1 class="text-xl font-semibold text-primary-900 truncate">
                    Products                </h1>
                            </div>
        </div>

        
        <div class="flex items-center space-x-4">
            
            <div class="hidden md:block relative">
                <div class="relative">
                    <input type="text"
                           id="dashboard-search"
                           placeholder="Search orders, projects..."
                           class="w-64 pl-10 pr-4 py-2 border border-neutral-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-4 w-4 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                    </div>
                </div>
                
                <div id="search-results" class="absolute top-full left-0 right-0 mt-1 bg-white rounded-lg shadow-strong border border-neutral-200 max-h-64 overflow-y-auto hidden z-50">
                    <div class="p-3">
                        <div class="text-sm text-neutral-500">Start typing to search...</div>
                    </div>
                </div>
            </div>

            
            <div class="relative">
                <button type="button" 
                        class="p-2 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-lg transition-colors duration-200"
                        id="notifications-button">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM11 19H6.5A2.5 2.5 0 014 16.5v-9A2.5 2.5 0 016.5 5h11A2.5 2.5 0 0120 7.5v3"/>
                    </svg>
                    
                                                        </button>
                
                
                <div class="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-strong border border-neutral-200 opacity-0 invisible transition-all duration-200"
                     id="notifications-dropdown">
                    <div class="p-4 border-b border-neutral-200">
                        <h3 class="text-sm font-semibold text-primary-900">Notifications</h3>
                    </div>
                    <div class="max-h-64 overflow-y-auto">
                        
                                                    <div class="p-8 text-center">
                                <div class="w-12 h-12 bg-neutral-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <svg class="w-6 h-6 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM11 19H6.5A2.5 2.5 0 014 16.5v-9A2.5 2.5 0 016.5 5h11A2.5 2.5 0 0120 7.5v3"/>
                                    </svg>
                                </div>
                                <p class="text-sm text-neutral-500">No new notifications</p>
                            </div>
                                            </div>
                    <div class="p-3 border-t border-neutral-200">
                        <a href="#" class="text-sm text-accent-600 hover:text-accent-700 font-medium transition-colors duration-200">
                            View all notifications
                        </a>
                    </div>
                </div>
            </div>

            
            <div class="relative">
                <button type="button" 
                        class="flex items-center space-x-3 p-2 rounded-lg hover:bg-primary-50 transition-colors duration-200"
                        id="user-menu-button">
                    <div class="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
                        <span class="text-sm font-medium text-white">
                            AU
                        </span>
                    </div>
                    <div class="hidden md:block text-left">
                        <p class="text-sm font-medium text-primary-900">
                            Admin
                        </p>
                        <p class="text-xs text-primary-500">
                            Customer
                        </p>
                    </div>
                    <svg class="w-4 h-4 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                    </svg>
                </button>

                
                <div class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-strong border border-neutral-200 opacity-0 invisible transition-all duration-200" 
                     id="user-menu-dropdown">
                    <div class="py-2">
                        <a href="http://localhost:8000/profile/edit" 
                           class="block px-4 py-2 text-sm text-primary-700 hover:bg-primary-50 hover:text-primary-900 transition-colors duration-200">
                            <div class="flex items-center space-x-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                </svg>
                                <span>Profile Settings</span>
                            </div>
                        </a>
                        <a href="http://localhost:8000" 
                           class="block px-4 py-2 text-sm text-primary-700 hover:bg-primary-50 hover:text-primary-900 transition-colors duration-200">
                            <div class="flex items-center space-x-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                                </svg>
                                <span>Back to Website</span>
                            </div>
                        </a>
                        <hr class="my-1 border-neutral-200">
                        <form method="POST" action="http://localhost:8000/logout">
                            <input type="hidden" name="_token" value="re0QwHah6LRhmZ3a0LPsssPlCHcVnoByw93M3595" autocomplete="off">                            <button type="submit" 
                                    class="w-full text-left px-4 py-2 text-sm text-danger-600 hover:bg-danger-50 hover:text-danger-700 transition-colors duration-200">
                                <div class="flex items-center space-x-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                                    </svg>
                                    <span>Sign Out</span>
                                </div>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>


<script>
document.addEventListener('DOMContentLoaded', function() {
    // Notifications dropdown
    const notificationsButton = document.getElementById('notifications-button');
    const notificationsDropdown = document.getElementById('notifications-dropdown');

    if (notificationsButton && notificationsDropdown) {
        notificationsButton.addEventListener('click', function(e) {
            e.stopPropagation();
            notificationsDropdown.classList.toggle('opacity-0');
            notificationsDropdown.classList.toggle('invisible');
        });
    }

    // User menu dropdown
    const userMenuButton = document.getElementById('user-menu-button');
    const userMenuDropdown = document.getElementById('user-menu-dropdown');

    if (userMenuButton && userMenuDropdown) {
        userMenuButton.addEventListener('click', function(e) {
            e.stopPropagation();
            userMenuDropdown.classList.toggle('opacity-0');
            userMenuDropdown.classList.toggle('invisible');
        });
    }

    // Dashboard search functionality
    const searchInput = document.getElementById('dashboard-search');
    const searchResults = document.getElementById('search-results');
    let searchTimeout;

    if (searchInput && searchResults) {
        searchInput.addEventListener('input', function() {
            const query = this.value.trim();

            clearTimeout(searchTimeout);

            if (query.length < 2) {
                searchResults.classList.add('hidden');
                return;
            }

            searchTimeout = setTimeout(() => {
                fetch(`http://localhost:8000/dashboard/search?q=${encodeURIComponent(query)}`)
                    .then(response => response.json())
                    .then(data => {
                        displaySearchResults(data.results);
                    })
                    .catch(error => {
                        console.error('Search error:', error);
                    });
            }, 300);
        });

        searchInput.addEventListener('focus', function() {
            if (this.value.trim().length >= 2) {
                searchResults.classList.remove('hidden');
            }
        });

        function displaySearchResults(results) {
            if (results.length === 0) {
                searchResults.innerHTML = '<div class="p-3"><div class="text-sm text-neutral-500">No results found</div></div>';
            } else {
                let html = '<div class="py-2">';
                results.forEach(result => {
                    html += `
                        <a href="${result.url}" class="block px-4 py-2 hover:bg-neutral-50 transition-colors duration-200">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-${result.type === 'order' ? 'primary' : 'secondary'}-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-4 h-4 text-${result.type === 'order' ? 'primary' : 'secondary'}-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        ${result.type === 'order' ?
                                            '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>' :
                                            '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>'
                                        }
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <div class="text-sm font-medium text-primary-900">${result.title}</div>
                                    <div class="text-xs text-neutral-500">${result.description}</div>
                                </div>
                            </div>
                        </a>
                    `;
                });
                html += '</div>';
                searchResults.innerHTML = html;
            }
            searchResults.classList.remove('hidden');
        }
    }

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        if (notificationsDropdown && !notificationsButton.contains(e.target)) {
            notificationsDropdown.classList.add('opacity-0', 'invisible');
        }
        if (userMenuDropdown && !userMenuButton.contains(e.target)) {
            userMenuDropdown.classList.add('opacity-0', 'invisible');
        }
        if (searchResults && !searchInput.contains(e.target) && !searchResults.contains(e.target)) {
            searchResults.classList.add('hidden');
        }
    });
});
</script>

            <!-- Page Content -->
            <main id="main-content" class="flex-1">
                            </main>
        </div>
    </div>

    <!-- Mobile sidebar overlay -->
    <div class="fixed inset-0 bg-primary-900 bg-opacity-75 z-40 lg:hidden hidden" id="sidebar-overlay"></div>

    <!-- Toast Notifications -->
    <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- Scripts -->
    <script>
document.addEventListener('DOMContentLoaded', function() {
    // Delete confirmation
    const deleteButtons = document.querySelectorAll('.delete-product');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            if (confirm('Are you sure you want to delete this product? This action cannot be undone.')) {
                const form = this.closest('form');
                form.submit();
            }
        });
    });
});
</script>

    <!-- Dashboard JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile menu toggle
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const sidebar = document.getElementById('dashboard-sidebar');
            const overlay = document.getElementById('sidebar-overlay');

            if (mobileMenuButton && sidebar && overlay) {
                mobileMenuButton.addEventListener('click', function() {
                    sidebar.classList.toggle('-translate-x-full');
                    overlay.classList.toggle('hidden');
                });

                // Close sidebar when clicking overlay
                overlay.addEventListener('click', function() {
                    sidebar.classList.add('-translate-x-full');
                    overlay.classList.add('hidden');
                });
            }

            // CSRF token for AJAX requests
            window.Laravel = {
                csrfToken: 're0QwHah6LRhmZ3a0LPsssPlCHcVnoByw93M3595'
            };

            // Set up AJAX defaults
            if (typeof axios !== 'undefined') {
                axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
                axios.defaults.headers.common['X-CSRF-TOKEN'] = window.Laravel.csrfToken;
            }
        });
    </script>
</body>
</html>
