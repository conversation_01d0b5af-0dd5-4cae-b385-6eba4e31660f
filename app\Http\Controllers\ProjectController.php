<?php

namespace App\Http\Controllers;

use App\Models\Project;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

class ProjectController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of the user's projects.
     */
    public function index(Request $request): View
    {
        $user = auth()->user();

        // Only clients, admin, and staff can access projects
        if (!$user->isAdminOrStaff() && !$user->isClient()) {
            abort(403, 'Unauthorized access to projects.');
        }

        // For admin/staff: show all projects
        // For clients: show only their own projects
        if ($user->isAdminOrStaff()) {
            $query = Project::where('is_deleted', false)
                           ->with(['service', 'client']);
        } else {
            $query = Project::where('client_id', $user->id)
                           ->where('is_deleted', false)
                           ->with(['service']);
        }

        // Search functionality
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                  ->orWhere('description', 'like', "%{$searchTerm}%")
                  ->orWhere('client_name', 'like', "%{$searchTerm}%");
            });
        }

        // Status filtering
        if ($request->filled('status') && $request->status !== 'all') {
            $query->where('status', $request->status);
        }

        // Priority filtering
        if ($request->filled('priority') && $request->priority !== 'all') {
            $query->where('priority', $request->priority);
        }

        $projects = $query->orderBy('created_at', 'desc')->paginate(12);

        // Get status counts based on user role
        if ($user->isAdminOrStaff()) {
            $statusCounts = [
                'all' => Project::where('is_deleted', false)->count(),
                'planning' => Project::where('is_deleted', false)->where('status', 'planning')->count(),
                'in_progress' => Project::where('is_deleted', false)->where('status', 'in_progress')->count(),
                'review' => Project::where('is_deleted', false)->where('status', 'review')->count(),
                'completed' => Project::where('is_deleted', false)->where('status', 'completed')->count(),
                'on_hold' => Project::where('is_deleted', false)->where('status', 'on_hold')->count(),
                'cancelled' => Project::where('is_deleted', false)->where('status', 'cancelled')->count(),
            ];
        } else {
            $statusCounts = [
                'all' => Project::where('client_id', $user->id)->where('is_deleted', false)->count(),
                'planning' => Project::where('client_id', $user->id)->where('is_deleted', false)->where('status', 'planning')->count(),
                'in_progress' => Project::where('client_id', $user->id)->where('is_deleted', false)->where('status', 'in_progress')->count(),
                'review' => Project::where('client_id', $user->id)->where('is_deleted', false)->where('status', 'review')->count(),
                'completed' => Project::where('client_id', $user->id)->where('is_deleted', false)->where('status', 'completed')->count(),
                'on_hold' => Project::where('client_id', $user->id)->where('is_deleted', false)->where('status', 'on_hold')->count(),
                'cancelled' => Project::where('client_id', $user->id)->where('is_deleted', false)->where('status', 'cancelled')->count(),
            ];
        }

        return view('projects.index', compact('projects', 'statusCounts'));
    }

    /**
     * Display the specified project.
     */
    public function show(Project $project): View
    {
        $user = auth()->user();

        // Only clients, admin, and staff can access projects
        if (!$user->isAdminOrStaff() && !$user->isClient()) {
            abort(403, 'Unauthorized access to projects.');
        }

        // Check if project is soft deleted
        if ($project->is_deleted) {
            abort(404, 'Project not found.');
        }

        // Admin/staff can view all projects, clients can only view their own
        if (!$user->isAdminOrStaff() && $project->client_id !== $user->id) {
            abort(403, 'Unauthorized access to project.');
        }

        // Load relationships
        $project->load(['service']);

        return view('projects.show', compact('project'));
    }

    /**
     * Soft delete the specified project.
     */
    public function destroy(Project $project): RedirectResponse
    {
        $user = auth()->user();

        // Only clients, admin, and staff can access projects
        if (!$user->isAdminOrStaff() && !$user->isClient()) {
            abort(403, 'Unauthorized access to projects.');
        }

        // Check if project is already soft deleted
        if ($project->is_deleted) {
            abort(404, 'Project not found.');
        }

        // Ensure user can only delete their own projects (clients only, admin/staff have different rules)
        if (!$user->isAdminOrStaff() && $project->client_id !== $user->id) {
            abort(403, 'Unauthorized access to project.');
        }

        // Business rule: Only allow deletion of projects in certain statuses
        $deletableStatuses = ['planning', 'cancelled', 'on_hold'];
        if (!in_array($project->status, $deletableStatuses)) {
            return redirect()->back()->with('error', 'Projects in progress, review, or completed status cannot be deleted.');
        }

        // Soft delete the project
        $project->update(['is_deleted' => true]);

        return redirect()->route('my-projects.index')->with('success', 'Project deleted successfully.');
    }
}
