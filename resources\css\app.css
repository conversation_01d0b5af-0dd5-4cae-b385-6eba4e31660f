@import "tailwindcss";

/* Custom CSS */
html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', sans-serif;
}

/* Button Components */
.btn-primary {
  background-color: #2563eb;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background-color: #1d4ed8;
}

.btn-secondary {
  background-color: #4b5563;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.btn-secondary:hover {
  background-color: #374151;
}

.btn-outline {
  border: 2px solid #2563eb;
  color: #2563eb;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.btn-outline:hover {
  background-color: #2563eb;
  color: white;
}

/* Card Components */
.card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
}

.card-hover {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  transition: box-shadow 0.2s;
}

.card-hover:hover {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Global Floating Label Form Styles */
.floating-input-group {
  position: relative;
  margin-bottom: 2rem;
}

.floating-input {
  width: 100%;
  padding: 1.25rem 0 0.75rem 0;
  border: none;
  border-bottom: 2px solid #e5e7eb;
  background: transparent;
  outline: none;
  font-size: 1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: #374151;
  font-weight: 400;
}

.floating-input:focus {
  border-bottom-color: #3b82f6;
  box-shadow: 0 1px 0 0 #3b82f6;
}

.floating-input:focus ~ .floating-label,
.floating-input:not(:placeholder-shown) ~ .floating-label,
.floating-input.has-value ~ .floating-label {
  transform: translateY(-1.75rem) scale(0.85);
  color: #3b82f6;
  font-weight: 500;
}

.floating-label {
  position: absolute;
  top: 1.25rem;
  left: 0;
  font-size: 1rem;
  color: #9ca3af;
  pointer-events: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: left top;
  background: transparent;
  z-index: 1;
}

/* Select specific styles */
.floating-input-group select.floating-input {
  padding: 1.25rem 2.5rem 0.75rem 0;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.25em 1.25em;
  cursor: pointer;
}

.floating-input-group select.floating-input:focus ~ .floating-label,
.floating-input-group select.floating-input:valid ~ .floating-label,
.floating-input-group select.floating-input.has-value ~ .floating-label {
  transform: translateY(-1.75rem) scale(0.85);
  color: #3b82f6;
  font-weight: 500;
}

/* Textarea specific styles */
.floating-input-group textarea.floating-input {
  min-height: 140px;
  resize: vertical;
  padding-top: 1.75rem;
  line-height: 1.5;
}

/* Hover effects */
.floating-input-group:hover .floating-input:not(:focus) {
  border-bottom-color: #d1d5db;
}

.floating-input-group:hover .floating-label {
  color: #6b7280;
}

/* Error states */
.floating-input.error {
  border-bottom-color: #ef4444;
}

.floating-input.error ~ .floating-label {
  color: #ef4444;
}

/* Success states */
.floating-input.success {
  border-bottom-color: #10b981;
}

.floating-input.success ~ .floating-label {
  color: #10b981;
}

/* Disabled states */
.floating-input:disabled {
  border-bottom-color: #d1d5db;
  color: #9ca3af;
  cursor: not-allowed;
}

.floating-input:disabled ~ .floating-label {
  color: #d1d5db;
}

/* Legacy form styles for backward compatibility */
.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Navigation Components */
.nav-link {
  color: #374151;
  font-weight: 500;
  transition: color 0.2s;
}

.nav-link:hover {
  color: #2563eb;
}

.nav-link-active {
  color: #2563eb;
  font-weight: 500;
}

/* Text Components */
.heading-1 {
  font-size: 2.25rem;
  font-weight: 700;
  color: #111827;
  line-height: 1.2;
}

@media (min-width: 1024px) {
  .heading-1 {
    font-size: 3rem;
  }
}

.heading-2 {
  font-size: 1.875rem;
  font-weight: 700;
  color: #111827;
  line-height: 1.2;
}

@media (min-width: 1024px) {
  .heading-2 {
    font-size: 2.25rem;
  }
}

.text-lead {
  font-size: 1.125rem;
  color: #4b5563;
  line-height: 1.6;
}

@media (min-width: 1024px) {
  .text-lead {
    font-size: 1.25rem;
  }
}

/* Custom utilities */
.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.bg-gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bg-gradient-blue {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

/* Animation utilities */
@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes slideUp {
  0% { transform: translateY(20px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

@keyframes slideDown {
  0% { transform: translateY(-20px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

@keyframes scaleIn {
  0% { transform: scale(0.95); opacity: 0; }
  100% { transform: scale(1); opacity: 1; }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.5s ease-out;
}

.animate-slide-down {
  animation: slideDown 0.5s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out;
}

/* Hover effects */
.hover-lift {
  transition: transform 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-4px);
}

/* Filter and Category Buttons */
.filter-btn, .category-btn {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s;
  background-color: #f3f4f6;
  color: #4b5563;
}

.filter-btn:hover, .category-btn:hover {
  background-color: #e5e7eb;
}

.filter-btn.active, .category-btn.active {
  background-color: #2563eb;
  color: white;
}

/* Project and Blog Card Animations */
.project-card, .blog-card {
  transition: transform 0.3s ease;
}

.project-card:hover, .blog-card:hover {
  transform: translateY(-4px);
}

/* Navigation Active States */
.nav-link {
  position: relative;
  transition: all 0.3s ease;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #2563eb, #3b82f6);
  transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 100%;
}

.nav-link:hover {
  color: #2563eb;
  transform: translateY(-1px);
}

.nav-link.active {
  color: #2563eb;
  font-weight: 600;
}

/* Mobile Navigation */
.mobile-nav-link {
  position: relative;
  transition: all 0.3s ease;
}

.mobile-nav-link::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 0;
  height: 100%;
  background: linear-gradient(90deg, #2563eb, #3b82f6);
  opacity: 0.1;
  transition: width 0.3s ease;
}

.mobile-nav-link:hover::before,
.mobile-nav-link.active::before {
  width: 4px;
}

.mobile-nav-link:hover,
.mobile-nav-link.active {
  color: #2563eb;
  background-color: #eff6ff;
}

/* Dashboard Styles */
.nav-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: #6b7280;
    text-decoration: none;
    border-radius: 0.5rem;
    transition: all 0.2s;
    font-weight: 500;
    gap: 0.75rem;
}

.nav-item:hover {
    background-color: #f3f4f6;
    color: #374151;
}

.nav-item.active {
    background-color: #eff6ff;
    color: #2563eb;
    font-weight: 600;
}

.nav-item-active {
    background-color: #eff6ff;
    color: #2563eb;
    font-weight: 600;
    position: relative;
}

.nav-item-active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 1rem;
    right: 1rem;
    height: 2px;
    background-color: #2563eb;
    border-radius: 1px;
}

.nav-item svg {
    flex-shrink: 0;
}

/* Dashboard Cards */
.dashboard-card {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    transition: box-shadow 0.2s;
}

.dashboard-card:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.dashboard-stat {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.dashboard-stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dashboard-stat-content h3 {
    font-size: 1.875rem;
    font-weight: 700;
    color: #111827;
    margin: 0;
}

.dashboard-stat-content p {
    color: #6b7280;
    font-size: 0.875rem;
    margin: 0;
}

/* Recent Activity */
.activity-item {
    display: flex;
    align-items: start;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid #f3f4f6;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.activity-content h4 {
    font-weight: 600;
    color: #111827;
    margin: 0 0 0.25rem 0;
    font-size: 0.875rem;
}

.activity-content p {
    color: #6b7280;
    font-size: 0.75rem;
    margin: 0;
}

/* Quick Actions */
.quick-action {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.5rem;
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    text-decoration: none;
    transition: all 0.2s;
    text-align: center;
}

.quick-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.quick-action-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.quick-action h3 {
    font-weight: 600;
    color: #111827;
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
}

.quick-action p {
    color: #6b7280;
    font-size: 0.875rem;
    margin: 0;
}

/* Hide scrollbar but keep functionality */
.scrollbar-hide {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Safari and Chrome */
}

/* Ensure sidebar scrolling works properly */
#dashboard-sidebar {
    height: 100vh;
    overflow: hidden;
}

#dashboard-sidebar nav {
    overflow-y: auto;
    overflow-x: hidden;
    max-height: calc(100vh - 200px); /* Adjust based on header and profile section height */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* Internet Explorer 10+ */
}

#dashboard-sidebar nav::-webkit-scrollbar {
    display: none; /* Safari and Chrome */
}

@theme {
    --font-sans: 'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
        'Segoe UI Symbol', 'Noto Color Emoji';
}
