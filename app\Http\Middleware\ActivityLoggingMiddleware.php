<?php

namespace App\Http\Middleware;

use App\Services\ActivityLogger;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response as BaseResponse;

class ActivityLoggingMiddleware
{
    protected ActivityLogger $activityLogger;

    public function __construct(ActivityLogger $activityLogger)
    {
        $this->activityLogger = $activityLogger;
    }

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): BaseResponse
    {
        // Only log for authenticated users
        if (!Auth::check()) {
            return $next($request);
        }

        $startTime = microtime(true);
        $response = $next($request);
        $endTime = microtime(true);

        // Log the activity after the response is generated
        $this->logActivity($request, $response, $endTime - $startTime);

        return $response;
    }

    /**
     * Log the customer dashboard activity.
     */
    protected function logActivity(Request $request, BaseResponse $response, float $processingTime): void
    {
        try {
            $routeName = $request->route()?->getName();
            $method = $request->method();
            $uri = $request->getRequestUri();
            $statusCode = $response->getStatusCode();
            
            // Determine activity type and description based on route and method
            $activityData = $this->determineActivityType($request, $response);
            
            if (!$activityData) {
                return; // Skip logging for certain routes
            }

            $success = $statusCode >= 200 && $statusCode < 400;
            $failureReason = $success ? null : $this->getFailureReason($statusCode, $response);

            // Log the activity
            $this->activityLogger->logCustomerActivity(
                $activityData['type'],
                $activityData['description'],
                $success ? 'success' : 'failed',
                $failureReason,
                [
                    'http_method' => $method,
                    'status_code' => $statusCode,
                    'processing_time_ms' => round($processingTime * 1000, 2),
                    'route_name' => $routeName,
                    'uri' => $uri,
                    'request_size' => strlen($request->getContent()),
                    'user_role' => Auth::user()?->role?->name,
                ],
                [
                    'response_successful' => $success,
                    'response_size' => $this->getResponseSize($response),
                    'redirect_location' => $response instanceof \Illuminate\Http\RedirectResponse 
                        ? $response->getTargetUrl() : null,
                ]
            );

        } catch (\Exception $e) {
            // Don't let logging errors break the application
            \Log::warning('Activity logging failed', [
                'error' => $e->getMessage(),
                'route' => $request->route()?->getName(),
                'user_id' => Auth::id(),
            ]);
        }
    }

    /**
     * Determine activity type and description based on request.
     */
    protected function determineActivityType(Request $request, BaseResponse $response): ?array
    {
        $routeName = $request->route()?->getName();
        $method = $request->method();
        $uri = $request->getRequestUri();

        // Skip certain routes that shouldn't be logged
        $skipRoutes = [
            'dashboard.search', // AJAX search requests
            'cart.count', // AJAX cart count requests
        ];

        if (in_array($routeName, $skipRoutes)) {
            return null;
        }

        // Dashboard access
        if ($routeName === 'dashboard') {
            return [
                'type' => 'customer_dashboard_access',
                'description' => 'Accessed customer dashboard'
            ];
        }

        // Profile management
        if (str_starts_with($routeName, 'profile.')) {
            return $this->getProfileActivityData($routeName, $method);
        }

        // Project applications
        if (str_starts_with($routeName, 'project-applications.')) {
            return $this->getProjectApplicationActivityData($routeName, $method, $request);
        }

        // Orders
        if (str_starts_with($routeName, 'orders.')) {
            return $this->getOrderActivityData($routeName, $method, $request);
        }

        // Projects (for clients)
        if (str_starts_with($routeName, 'projects.')) {
            return $this->getProjectActivityData($routeName, $method, $request);
        }

        // Cart activities
        if (str_starts_with($routeName, 'cart.')) {
            return $this->getCartActivityData($routeName, $method);
        }

        // Generic customer activity for other authenticated routes
        return [
            'type' => 'customer_activity',
            'description' => "Customer accessed: {$uri}"
        ];
    }

    /**
     * Get profile activity data.
     */
    protected function getProfileActivityData(string $routeName, string $method): array
    {
        $actions = [
            'profile.edit' => ['type' => 'profile_view', 'description' => 'Profile edit page accessed'],
            'profile.update' => ['type' => 'profile_update', 'description' => 'Profile information updated'],
            'profile.password.update' => ['type' => 'profile_password_update', 'description' => 'Profile password updated'],
            'profile.avatar.delete' => ['type' => 'profile_avatar_delete', 'description' => 'Profile avatar deleted'],
            'profile.delete-account' => ['type' => 'profile_delete_view', 'description' => 'Account deletion page accessed'],
            'profile.destroy' => ['type' => 'profile_delete', 'description' => 'Account deletion attempted'],
        ];

        return $actions[$routeName] ?? [
            'type' => 'profile_activity',
            'description' => "Profile action: {$routeName}"
        ];
    }

    /**
     * Get project application activity data.
     */
    protected function getProjectApplicationActivityData(string $routeName, string $method, Request $request): array
    {
        $applicationId = $request->route('project_application')?->id ?? $request->route('project-application')?->id;
        
        $actions = [
            'project-applications.index' => ['type' => 'project_application_list', 'description' => 'Project applications list accessed'],
            'project-applications.create' => ['type' => 'project_application_create_form', 'description' => 'Project application creation form accessed'],
            'project-applications.store' => ['type' => 'project_application_create', 'description' => 'Project application submitted'],
            'project-applications.show' => ['type' => 'project_application_view', 'description' => "Project application viewed (ID: {$applicationId})"],
            'project-applications.edit' => ['type' => 'project_application_edit_form', 'description' => "Project application edit form accessed (ID: {$applicationId})"],
            'project-applications.update' => ['type' => 'project_application_update', 'description' => "Project application updated (ID: {$applicationId})"],
            'project-applications.destroy' => ['type' => 'project_application_delete', 'description' => "Project application deleted (ID: {$applicationId})"],
        ];

        return $actions[$routeName] ?? [
            'type' => 'project_application_activity',
            'description' => "Project application action: {$routeName}"
        ];
    }

    /**
     * Get order activity data.
     */
    protected function getOrderActivityData(string $routeName, string $method, Request $request): array
    {
        $orderId = $request->route('order')?->id;
        
        $actions = [
            'orders.index' => ['type' => 'order_list', 'description' => 'Orders list accessed'],
            'orders.show' => ['type' => 'order_view', 'description' => "Order viewed (ID: {$orderId})"],
            'orders.destroy' => ['type' => 'order_cancel', 'description' => "Order cancelled (ID: {$orderId})"],
        ];

        return $actions[$routeName] ?? [
            'type' => 'order_activity',
            'description' => "Order action: {$routeName}"
        ];
    }

    /**
     * Get project activity data.
     */
    protected function getProjectActivityData(string $routeName, string $method, Request $request): array
    {
        $projectId = $request->route('project')?->id;
        
        $actions = [
            'projects.index' => ['type' => 'project_list', 'description' => 'Projects list accessed'],
            'projects.show' => ['type' => 'project_view', 'description' => "Project viewed (ID: {$projectId})"],
            'projects.destroy' => ['type' => 'project_delete', 'description' => "Project deleted (ID: {$projectId})"],
        ];

        return $actions[$routeName] ?? [
            'type' => 'project_activity',
            'description' => "Project action: {$routeName}"
        ];
    }

    /**
     * Get cart activity data.
     */
    protected function getCartActivityData(string $routeName, string $method): array
    {
        $actions = [
            'cart.index' => ['type' => 'cart_view', 'description' => 'Shopping cart accessed'],
            'cart.add' => ['type' => 'cart_add_item', 'description' => 'Item added to cart'],
            'cart.update' => ['type' => 'cart_update_item', 'description' => 'Cart item updated'],
            'cart.remove' => ['type' => 'cart_remove_item', 'description' => 'Item removed from cart'],
            'cart.clear' => ['type' => 'cart_clear', 'description' => 'Cart cleared'],
            'cart.coupon' => ['type' => 'cart_apply_coupon', 'description' => 'Coupon applied to cart'],
        ];

        return $actions[$routeName] ?? [
            'type' => 'cart_activity',
            'description' => "Cart action: {$routeName}"
        ];
    }

    /**
     * Get failure reason based on status code.
     */
    protected function getFailureReason(int $statusCode, BaseResponse $response): string
    {
        return match ($statusCode) {
            400 => 'Bad Request',
            401 => 'Unauthorized',
            403 => 'Forbidden',
            404 => 'Not Found',
            422 => 'Validation Failed',
            429 => 'Too Many Requests',
            500 => 'Internal Server Error',
            503 => 'Service Unavailable',
            default => "HTTP {$statusCode}"
        };
    }

    /**
     * Get response size.
     */
    protected function getResponseSize(BaseResponse $response): ?int
    {
        $content = $response->getContent();
        return $content ? strlen($content) : null;
    }
}
