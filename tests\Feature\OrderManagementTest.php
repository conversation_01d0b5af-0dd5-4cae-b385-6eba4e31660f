<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\Currency;
use App\Models\Role;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class OrderManagementTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $otherUser;
    protected $currency;

    protected function setUp(): void
    {
        parent::setUp();

        // Create roles first
        Role::create(['name' => 'admin', 'slug' => 'admin', 'is_active' => true]);
        Role::create(['name' => 'staff', 'slug' => 'staff', 'is_active' => true]);
        Role::create(['name' => 'client', 'slug' => 'client', 'is_active' => true]);
        Role::create(['name' => 'customer', 'slug' => 'customer', 'is_active' => true]);

        $this->user = User::factory()->create();
        $this->otherUser = User::factory()->create();
        $this->currency = Currency::factory()->create(['code' => 'ZAR', 'symbol' => 'R']);
    }

    /** @test */
    public function authenticated_user_can_view_orders_index()
    {
        $response = $this->actingAs($this->user)->get('/orders');

        $response->assertStatus(200);
        $response->assertViewIs('orders.index');
        $response->assertSee('My Orders');
    }

    /** @test */
    public function unauthenticated_user_cannot_access_orders()
    {
        $response = $this->get('/orders');

        $response->assertRedirect('/login');
    }

    /** @test */
    public function user_can_view_their_own_orders()
    {
        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'is_deleted' => false,
        ]);

        $response = $this->actingAs($this->user)->get('/orders');

        $response->assertStatus(200);
        $response->assertSee($order->order_number);
    }

    /** @test */
    public function user_cannot_see_other_users_orders()
    {
        $userOrder = Order::factory()->create([
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'is_deleted' => false,
        ]);

        $otherUserOrder = Order::factory()->create([
            'user_id' => $this->otherUser->id,
            'currency_id' => $this->currency->id,
            'is_deleted' => false,
        ]);

        $response = $this->actingAs($this->user)->get('/orders');

        $response->assertStatus(200);
        $response->assertSee($userOrder->order_number);
        $response->assertDontSee($otherUserOrder->order_number);
    }

    /** @test */
    public function deleted_orders_are_not_shown_in_index()
    {
        $activeOrder = Order::factory()->create([
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'order_number' => 'ACTIVE-001',
            'is_deleted' => false,
        ]);

        $deletedOrder = Order::factory()->create([
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'order_number' => 'DELETED-001',
            'is_deleted' => true,
        ]);

        $response = $this->actingAs($this->user)->get('/orders');

        $response->assertStatus(200);
        $response->assertSee('ACTIVE-001');
        $response->assertDontSee('DELETED-001');
    }

    /** @test */
    public function user_can_filter_orders_by_status()
    {
        $pendingOrder = Order::factory()->create([
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'status' => 'pending',
            'order_number' => 'PENDING-001',
            'is_deleted' => false,
        ]);

        $shippedOrder = Order::factory()->create([
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'status' => 'shipped',
            'order_number' => 'SHIPPED-001',
            'is_deleted' => false,
        ]);

        $response = $this->actingAs($this->user)->get('/orders?status=pending');

        $response->assertStatus(200);
        $response->assertSee('PENDING-001');
        $response->assertDontSee('SHIPPED-001');
    }

    /** @test */
    public function user_can_search_orders()
    {
        $order1 = Order::factory()->create([
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'order_number' => 'SEARCH-001',
            'is_deleted' => false,
        ]);

        $order2 = Order::factory()->create([
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'order_number' => 'OTHER-002',
            'is_deleted' => false,
        ]);

        $response = $this->actingAs($this->user)->get('/orders?search=SEARCH');

        $response->assertStatus(200);
        $response->assertSee('SEARCH-001');
        $response->assertDontSee('OTHER-002');
    }

    /** @test */
    public function user_can_view_order_details()
    {
        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'is_deleted' => false,
        ]);

        $response = $this->actingAs($this->user)->get("/orders/{$order->uuid}");

        $response->assertStatus(200);
        $response->assertViewIs('orders.show');
        $response->assertSee($order->order_number);
    }

    /** @test */
    public function user_cannot_view_other_users_order_details()
    {
        $order = Order::factory()->create([
            'user_id' => $this->otherUser->id,
            'currency_id' => $this->currency->id,
            'is_deleted' => false,
        ]);

        $response = $this->actingAs($this->user)->get("/orders/{$order->uuid}");

        $response->assertStatus(403);
    }

    /** @test */
    public function accessing_deleted_order_returns_404()
    {
        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'is_deleted' => true,
        ]);

        $response = $this->actingAs($this->user)->get("/orders/{$order->uuid}");

        $response->assertStatus(404);
    }

    /** @test */
    public function user_can_delete_pending_order()
    {
        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'status' => 'pending',
            'is_deleted' => false,
        ]);

        $response = $this->actingAs($this->user)->delete("/orders/{$order->uuid}");

        $response->assertRedirect('/orders');
        $response->assertSessionHas('success', 'Order deleted successfully.');

        $this->assertDatabaseHas('orders', [
            'id' => $order->id,
            'is_deleted' => true,
        ]);
    }

    /** @test */
    public function user_can_delete_cancelled_order()
    {
        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'status' => 'cancelled',
            'is_deleted' => false,
        ]);

        $response = $this->actingAs($this->user)->delete("/orders/{$order->uuid}");

        $response->assertRedirect('/orders');
        $response->assertSessionHas('success', 'Order deleted successfully.');

        $this->assertDatabaseHas('orders', [
            'id' => $order->id,
            'is_deleted' => true,
        ]);
    }

    /** @test */
    public function user_cannot_delete_confirmed_order()
    {
        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'status' => 'confirmed',
            'is_deleted' => false,
        ]);

        $response = $this->actingAs($this->user)->delete("/orders/{$order->uuid}");

        $response->assertRedirect();
        $response->assertSessionHasErrors(['error']);

        $this->assertDatabaseHas('orders', [
            'id' => $order->id,
            'is_deleted' => false,
        ]);
    }

    /** @test */
    public function user_cannot_delete_shipped_order()
    {
        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'status' => 'shipped',
            'is_deleted' => false,
        ]);

        $response = $this->actingAs($this->user)->delete("/orders/{$order->uuid}");

        $response->assertRedirect();
        $response->assertSessionHasErrors(['error']);

        $this->assertDatabaseHas('orders', [
            'id' => $order->id,
            'is_deleted' => false,
        ]);
    }

    /** @test */
    public function user_cannot_delete_other_users_order()
    {
        $order = Order::factory()->create([
            'user_id' => $this->otherUser->id,
            'currency_id' => $this->currency->id,
            'status' => 'pending',
            'is_deleted' => false,
        ]);

        $response = $this->actingAs($this->user)->delete("/orders/{$order->uuid}");

        $response->assertStatus(403);

        $this->assertDatabaseHas('orders', [
            'id' => $order->id,
            'is_deleted' => false,
        ]);
    }

    /** @test */
    public function deleting_already_deleted_order_returns_404()
    {
        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'status' => 'pending',
            'is_deleted' => true,
        ]);

        $response = $this->actingAs($this->user)->delete("/orders/{$order->uuid}");

        $response->assertStatus(404);
    }

    /** @test */
    public function orders_index_shows_status_counts()
    {
        // Create orders with different statuses
        Order::factory()->create(['user_id' => $this->user->id, 'currency_id' => $this->currency->id, 'status' => 'pending', 'is_deleted' => false]);
        Order::factory()->create(['user_id' => $this->user->id, 'currency_id' => $this->currency->id, 'status' => 'pending', 'is_deleted' => false]);
        Order::factory()->create(['user_id' => $this->user->id, 'currency_id' => $this->currency->id, 'status' => 'shipped', 'is_deleted' => false]);

        $response = $this->actingAs($this->user)->get('/orders');

        $response->assertStatus(200);
        $response->assertViewHas('statusCounts');
        
        $statusCounts = $response->viewData('statusCounts');
        $this->assertEquals(3, $statusCounts['all']);
        $this->assertEquals(2, $statusCounts['pending']);
        $this->assertEquals(1, $statusCounts['shipped']);
    }
}
