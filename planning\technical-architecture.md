# Technical Architecture Document
## ChiSolution Digital Agency Platform

### 🏗️ System Architecture Overview

#### **Architecture Pattern: MVC + Service Layer**
```
┌─────────────────────────────────────────────────────────────┐
│                    PRESENTATION LAYER                       │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Web UI    │  │  Admin UI   │  │   API       │         │
│  │  (Blade)    │  │  (Blade)    │  │ (JSON)      │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   APPLICATION LAYER                         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ Controllers │  │ Middleware  │  │ Form        │         │
│  │             │  │             │  │ Requests    │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    BUSINESS LAYER                           │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │  Services   │  │   Actions   │  │   Events    │         │
│  │             │  │             │  │             │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                     DATA LAYER                              │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Models    │  │ Repositories│  │   Cache     │         │
│  │ (Eloquent)  │  │             │  │   (Redis)   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                 INFRASTRUCTURE LAYER                        │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   MySQL     │  │   Storage   │  │   Queue     │         │
│  │  Database   │  │   (Files)   │  │  (Redis)    │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

### 📁 Folder Structure

```
app/
├── Actions/                    # Single-purpose action classes
│   ├── Auth/
│   ├── Orders/
│   ├── Products/
│   └── Projects/
├── Console/
│   └── Commands/
├── Events/                     # Domain events
├── Exceptions/
├── Http/
│   ├── Controllers/
│   │   ├── Admin/             # Admin dashboard controllers
│   │   ├── Api/               # API controllers
│   │   ├── Auth/              # Authentication controllers
│   │   └── Web/               # Public web controllers
│   ├── Middleware/
│   ├── Requests/              # Form request validation
│   └── Resources/             # API resources
├── Jobs/                      # Queue jobs
├── Listeners/                 # Event listeners
├── Mail/                      # Mail classes
├── Models/
│   ├── Concerns/              # Model traits
│   └── Scopes/                # Query scopes
├── Notifications/
├── Policies/                  # Authorization policies
├── Providers/
├── Repositories/              # Data access layer
│   ├── Contracts/             # Repository interfaces
│   └── Eloquent/              # Eloquent implementations
├── Services/                  # Business logic services
│   ├── Auth/
│   ├── Cart/
│   ├── Order/
│   ├── Payment/
│   ├── Product/
│   ├── Project/
│   └── Seo/
└── Traits/                    # Reusable traits

config/
├── app.php
├── database.php
├── filesystems.php
├── localization.php           # Custom localization config
├── payment.php                # Payment gateway config
├── seo.php                    # SEO configuration
└── services.php

database/
├── factories/
├── migrations/
├── seeders/
└── schema/                    # Database documentation

resources/
├── css/
├── js/
├── lang/                      # Translation files
│   ├── en/
│   ├── fr/
│   └── es/
└── views/
    ├── admin/                 # Admin dashboard views
    ├── auth/                  # Authentication views
    ├── components/            # Blade components
    ├── emails/                # Email templates
    ├── layouts/               # Layout templates
    └── web/                   # Public website views
```

### 🎨 Frontend Architecture

#### **CSS Architecture**
```css
resources/css/app.css Structure:
├── Base Styles
│   ├── CSS Reset & Normalize
│   ├── Typography System
│   └── Color Variables
├── Component Styles
│   ├── Buttons (.btn-primary, .btn-secondary)
│   ├── Cards (.card, .card-hover)
│   ├── Navigation (.nav-link, .nav-link-active)
│   └── Forms (Floating Label System)
├── Layout Styles
│   ├── Grid System
│   ├── Containers
│   └── Spacing Utilities
└── Utility Classes
    ├── Text Utilities
    ├── Spacing Utilities
    └── Display Utilities

Floating Label Form System:
- .floating-input-group: Container wrapper
- .floating-input: Input/textarea/select element
- .floating-label: Animated label element
- States: default, focus, filled, error, success, disabled
```

#### **JavaScript Architecture**
```javascript
resources/js/ Structure:
├── app.js                     # Main application entry
├── components/
│   ├── floating-forms.js      # Floating label functionality
│   ├── navigation.js          # Navigation interactions
│   ├── cart.js               # Shopping cart functionality
│   └── accordion.js          # FAQ accordion component
├── pages/
│   ├── shop.js               # E-commerce specific JS
│   ├── contact.js            # Contact form handling
│   └── checkout.js           # Checkout process
└── utils/
    ├── api.js                # API communication helpers
    ├── validation.js         # Client-side validation
    └── helpers.js            # Utility functions

Form Enhancement Pattern:
1. Progressive Enhancement (works without JS)
2. Event delegation for dynamic content
3. State management for form interactions
4. Validation feedback integration
```

#### **Blade Component System**
```php
resources/views/components/ Structure:
├── forms/
│   ├── floating-input.blade.php    # Floating label input
│   ├── floating-textarea.blade.php # Floating label textarea
│   ├── floating-select.blade.php   # Floating label select
│   └── form-group.blade.php        # Form group wrapper
├── ui/
│   ├── button.blade.php            # Reusable button component
│   ├── card.blade.php              # Card component
│   ├── modal.blade.php             # Modal component
│   └── accordion.blade.php         # Accordion component
└── layout/
    ├── navigation.blade.php        # Main navigation
    ├── footer.blade.php            # Site footer
    └── breadcrumbs.blade.php       # Breadcrumb navigation

Usage Example:
<x-forms.floating-input
    name="email"
    type="email"
    label="Email Address"
    required />
```

storage/
├── app/
│   ├── public/
│   │   ├── products/          # Product images
│   │   ├── projects/          # Project images
│   │   ├── team/              # Team member photos
│   │   └── uploads/           # General uploads
├── framework/
└── logs/

tests/
├── Feature/                   # Feature tests
│   ├── Admin/
│   ├── Api/
│   ├── Auth/
│   └── Web/
├── Unit/                      # Unit tests
│   ├── Actions/
│   ├── Models/
│   ├── Repositories/
│   └── Services/
└── TestCase.php
```

### 🔧 Design Patterns Implementation

#### **Repository Pattern**
```php
// Repository Interface
interface ProductRepositoryInterface
{
    public function findBySlug(string $slug): ?Product;
    public function getActiveProducts(array $filters = []): Collection;
    public function getFeaturedProducts(int $limit = 10): Collection;
}

// Eloquent Implementation
class EloquentProductRepository implements ProductRepositoryInterface
{
    public function findBySlug(string $slug): ?Product
    {
        return Product::where('slug', $slug)
            ->where('is_active', true)
            ->where('is_deleted', false)
            ->first();
    }
}
```

#### **Service Layer Pattern**
```php
class ProductService
{
    public function __construct(
        private ProductRepositoryInterface $productRepository,
        private SeoService $seoService,
        private ImageService $imageService
    ) {}

    public function createProduct(array $data): Product
    {
        DB::beginTransaction();
        try {
            $product = $this->productRepository->create($data);
            $this->seoService->generateSeoMeta($product);
            $this->imageService->processProductImages($product, $data['images']);
            
            DB::commit();
            return $product;
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }
}
```

#### **Action Pattern**
```php
class CreateOrderAction
{
    public function execute(array $cartItems, array $customerData): Order
    {
        return DB::transaction(function () use ($cartItems, $customerData) {
            $order = Order::create($this->prepareOrderData($customerData));
            $this->createOrderItems($order, $cartItems);
            $this->updateInventory($cartItems);
            $this->sendOrderConfirmation($order);
            
            return $order;
        });
    }
}
```

### 🔐 Security Architecture

#### **Authentication Flow**
```
User Request → Middleware → Controller → Service → Repository → Database
     ↓              ↓           ↓          ↓          ↓
  Sanitize → Authenticate → Authorize → Validate → Execute
```

#### **Security Layers**
1. **Input Validation**: Form Requests with custom rules
2. **Authentication**: Laravel Sanctum for API, Session for web
3. **Authorization**: Policies and Gates for fine-grained control
4. **CSRF Protection**: Built-in Laravel CSRF middleware
5. **SQL Injection**: Eloquent ORM with parameter binding
6. **XSS Protection**: Blade template escaping
7. **Rate Limiting**: Custom rate limiters per endpoint

### 📊 Data Flow Architecture

#### **Request Lifecycle**
```
1. Route Resolution
   ↓
2. Middleware Stack
   ├── CORS
   ├── Authentication
   ├── Authorization
   ├── Localization
   └── Rate Limiting
   ↓
3. Controller
   ├── Form Request Validation
   ├── Service Layer Call
   └── Response Formatting
   ↓
4. Service Layer
   ├── Business Logic
   ├── Repository Calls
   ├── Event Dispatching
   └── Cache Management
   ↓
5. Repository Layer
   ├── Database Queries
   ├── Model Relationships
   └── Data Transformation
   ↓
6. Response
   ├── View Rendering (Web)
   ├── JSON Response (API)
   └── Redirect (Forms)
```

### 🚀 Performance Architecture

#### **Caching Strategy**
```php
// Multi-level caching
1. Application Cache (Redis)
   - User sessions
   - Query results
   - Computed data

2. Database Query Cache
   - Eloquent model caching
   - Query result caching

3. View Cache
   - Blade template compilation
   - Partial view caching

4. Asset Cache
   - CSS/JS compilation
   - Image optimization
   - CDN integration
```

#### **Queue Architecture**
```php
// Queue Jobs
├── Email Notifications (high priority)
├── Image Processing (medium priority)
├── SEO Sitemap Generation (low priority)
├── Analytics Data Processing (low priority)
└── Backup Operations (low priority)
```

### 🔄 Event-Driven Architecture

#### **Domain Events**
```php
// Order Events
OrderCreated::class → [
    SendOrderConfirmationEmail::class,
    UpdateInventoryLevels::class,
    CreateInvoice::class,
    NotifyAdmins::class
]

// User Events
UserRegistered::class → [
    SendWelcomeEmail::class,
    CreateUserProfile::class,
    AssignDefaultRole::class
]

// Product Events
ProductCreated::class → [
    GenerateSeoMeta::class,
    ProcessProductImages::class,
    UpdateSitemap::class
]
```

### 📱 API Architecture

#### **RESTful API Design**
```
GET    /api/v1/products              # List products
GET    /api/v1/products/{id}         # Get product
POST   /api/v1/products              # Create product
PUT    /api/v1/products/{id}         # Update product
DELETE /api/v1/products/{id}         # Delete product

GET    /api/v1/orders                # List orders
GET    /api/v1/orders/{id}           # Get order
POST   /api/v1/orders                # Create order
PUT    /api/v1/orders/{id}/status    # Update order status
```

#### **API Response Format**
```json
{
    "success": true,
    "data": {
        "id": 1,
        "name": "Product Name",
        "price": 999.99
    },
    "meta": {
        "pagination": {
            "current_page": 1,
            "total_pages": 10,
            "per_page": 20,
            "total": 200
        }
    },
    "links": {
        "self": "/api/v1/products/1",
        "related": "/api/v1/products/1/variants"
    }
}
```

### 🔧 Development Standards

#### **Code Standards**
- PSR-12 coding standards
- Laravel best practices
- SOLID principles
- DRY (Don't Repeat Yourself)
- KISS (Keep It Simple, Stupid)

#### **Naming Conventions**
```php
// Models: Singular, PascalCase
Product, OrderItem, UserAddress

// Controllers: PascalCase + Controller suffix
ProductController, OrderController

// Services: PascalCase + Service suffix
PaymentService, EmailService

// Actions: PascalCase + Action suffix
CreateOrderAction, ProcessPaymentAction

// Variables: camelCase
$productName, $orderTotal, $userEmail

// Constants: UPPER_SNAKE_CASE
ORDER_STATUS_PENDING, PAYMENT_METHOD_STRIPE
```
