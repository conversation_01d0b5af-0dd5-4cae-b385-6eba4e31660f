@extends('layouts.app')

@section('title', 'Application Submitted Successfully')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Success Header -->
        <div class="text-center mb-12">
            <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6">
                <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
            <h1 class="text-4xl font-bold text-gray-900 mb-4">Application Submitted Successfully!</h1>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                Thank you for your project application. We've received your submission and will review it carefully.
            </p>
        </div>

        <!-- Application Details Card -->
        <div class="bg-white rounded-xl shadow-lg overflow-hidden mb-8">
            <div class="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4">
                <h2 class="text-xl font-semibold text-white">Application Details</h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="text-sm font-medium text-gray-500 uppercase tracking-wide mb-2">Reference Number</h3>
                        <p class="text-2xl font-bold text-blue-600 font-mono">{{ $application->reference_number }}</p>
                        <p class="text-sm text-gray-500 mt-1">Keep this number for your records</p>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-500 uppercase tracking-wide mb-2">Project Title</h3>
                        <p class="text-lg font-semibold text-gray-900">{{ $application->title }}</p>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-500 uppercase tracking-wide mb-2">Status</h3>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                            </svg>
                            {{ ucfirst($application->status) }}
                        </span>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-500 uppercase tracking-wide mb-2">Submitted</h3>
                        <p class="text-lg text-gray-900">{{ $application->submitted_at->format('M d, Y \a\t g:i A') }}</p>
                    </div>
                </div>

                @if($application->attachments && count($application->attachments) > 0)
                <div class="mt-6 pt-6 border-t border-gray-200">
                    <h3 class="text-sm font-medium text-gray-500 uppercase tracking-wide mb-3">Uploaded Files</h3>
                    <div class="space-y-2">
                        @foreach($application->attachments as $attachment)
                        <div class="flex items-center text-sm text-gray-600">
                            <svg class="w-4 h-4 mr-2 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                            {{ $attachment['original_name'] }}
                            <span class="ml-2 text-xs text-gray-400">({{ number_format($attachment['size'] / 1024, 1) }} KB)</span>
                        </div>
                        @endforeach
                    </div>
                </div>
                @endif
            </div>
        </div>

        <!-- Next Steps -->
        <div class="bg-white rounded-xl shadow-lg overflow-hidden mb-8">
            <div class="bg-gradient-to-r from-green-600 to-emerald-600 px-6 py-4">
                <h2 class="text-xl font-semibold text-white">What Happens Next?</h2>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="flex items-center justify-center h-8 w-8 rounded-full bg-blue-100 text-blue-600 font-semibold text-sm">1</div>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-medium text-gray-900">Review Process</h3>
                            <p class="text-gray-600">Our team will carefully review your application and requirements within 1-2 business days.</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="flex items-center justify-center h-8 w-8 rounded-full bg-blue-100 text-blue-600 font-semibold text-sm">2</div>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-medium text-gray-900">Initial Contact</h3>
                            <p class="text-gray-600">We'll reach out to discuss your project in detail and answer any questions you may have.</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="flex items-center justify-center h-8 w-8 rounded-full bg-blue-100 text-blue-600 font-semibold text-sm">3</div>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-medium text-gray-900">Proposal & Timeline</h3>
                            <p class="text-gray-600">We'll provide a detailed proposal with timeline and cost estimates for your project.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status Check -->
        <div class="bg-white rounded-xl shadow-lg overflow-hidden mb-8">
            <div class="p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Check Application Status</h2>
                <p class="text-gray-600 mb-4">You can check the status of your application anytime using your reference number.</p>
                
                <div class="flex flex-col sm:flex-row gap-4">
                    <div class="flex-1">
                        <input type="text" id="statusReferenceNumber" 
                               placeholder="Enter your reference number" 
                               value="{{ $application->reference_number }}"
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <button onclick="checkStatus()" 
                            class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium">
                        Check Status
                    </button>
                </div>
                
                <div id="statusResult" class="mt-4 hidden"></div>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="bg-white rounded-xl shadow-lg overflow-hidden">
            <div class="p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Need Help?</h2>
                <p class="text-gray-600 mb-4">If you have any questions about your application or our services, don't hesitate to contact us.</p>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="font-medium text-gray-900 mb-2">Email Us</h3>
                        <a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-700"><EMAIL></a>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-900 mb-2">Call Us</h3>
                        <a href="tel:+27123456789" class="text-blue-600 hover:text-blue-700">+27 12 345 6789</a>
                    </div>
                </div>
                
                <div class="mt-6 pt-6 border-t border-gray-200">
                    <div class="flex flex-col sm:flex-row gap-4">
                        <a href="{{ route('home') }}" 
                           class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                            </svg>
                            Back to Home
                        </a>
                        <a href="{{ route('contact') }}" 
                           class="inline-flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.456L3 21l2.456-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"></path>
                            </svg>
                            Contact Us
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function checkStatus() {
    const referenceNumber = document.getElementById('statusReferenceNumber').value.trim();
    const resultDiv = document.getElementById('statusResult');
    
    if (!referenceNumber) {
        showStatusResult('Please enter a reference number.', 'error');
        return;
    }
    
    // Show loading state
    showStatusResult('Checking status...', 'loading');
    
    fetch('{{ route("project-applications.status") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: JSON.stringify({ reference_number: referenceNumber })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const app = data.application;
            const statusHtml = `
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h3 class="font-medium text-green-900 mb-2">Application Found</h3>
                    <div class="text-sm text-green-700 space-y-1">
                        <p><strong>Title:</strong> ${app.title}</p>
                        <p><strong>Status:</strong> ${app.status}</p>
                        <p><strong>Submitted:</strong> ${app.submitted_at || 'N/A'}</p>
                        ${app.reviewed_at ? `<p><strong>Reviewed:</strong> ${app.reviewed_at}</p>` : ''}
                    </div>
                </div>
            `;
            showStatusResult(statusHtml, 'success');
        } else {
            showStatusResult(data.message, 'error');
        }
    })
    .catch(error => {
        showStatusResult('An error occurred while checking status. Please try again.', 'error');
    });
}

function showStatusResult(content, type) {
    const resultDiv = document.getElementById('statusResult');
    resultDiv.classList.remove('hidden');
    
    if (type === 'error') {
        resultDiv.innerHTML = `<div class="bg-red-50 border border-red-200 rounded-lg p-4 text-red-700">${content}</div>`;
    } else if (type === 'loading') {
        resultDiv.innerHTML = `<div class="bg-blue-50 border border-blue-200 rounded-lg p-4 text-blue-700">${content}</div>`;
    } else {
        resultDiv.innerHTML = content;
    }
}
</script>
@endsection
