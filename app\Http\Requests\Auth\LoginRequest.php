<?php

namespace App\Http\Requests\Auth;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\RateLimiter;

class LoginRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'email' => [
                'required',
                'string',
                'email:rfc,dns',
                'max:255',
            ],
            'password' => [
                'required',
                'string',
                'min:1', // Allow any password length for login
            ],
            'remember' => [
                'nullable',
                'boolean',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'email.required' => __('validation.required', ['attribute' => __('auth.email')]),
            'email.email' => __('validation.email', ['attribute' => __('auth.email')]),
            'email.max' => __('validation.max.string', ['attribute' => __('auth.email'), 'max' => 255]),
            'password.required' => __('validation.required', ['attribute' => __('auth.password')]),
            'password.string' => __('validation.string', ['attribute' => __('auth.password')]),
            'password.min' => __('validation.min.string', ['attribute' => __('auth.password'), 'min' => 1]),
            'remember.boolean' => __('validation.boolean', ['attribute' => __('auth.remember_me')]),
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'email' => __('auth.email'),
            'password' => __('auth.password'),
            'remember' => __('auth.remember_me'),
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'email' => strtolower(trim($this->email)),
            'remember' => $this->boolean('remember'),
        ]);
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Additional custom validation can be added here
            if ($this->hasInvalidCredentialsFormat()) {
                $validator->errors()->add('email', __('auth.failed'));
            }
        });
    }

    /**
     * Check if the credentials format is invalid.
     */
    protected function hasInvalidCredentialsFormat(): bool
    {
        // Add any additional format checks here
        // For example, checking for suspicious patterns
        return false;
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator): void
    {
        // Log failed validation attempts for security monitoring
        if ($this->filled('email')) {
            \Log::info('Login validation failed', [
                'email' => $this->input('email'),
                'ip' => $this->ip(),
                'user_agent' => $this->userAgent(),
                'errors' => $validator->errors()->toArray(),
            ]);
        }

        // Handle AJAX requests
        if ($this->expectsJson()) {
            $response = response()->json([
                'success' => false,
                'message' => __('validation.failed'),
                'errors' => $validator->errors()
            ], 422);

            throw new \Illuminate\Http\Exceptions\HttpResponseException($response);
        }

        parent::failedValidation($validator);
    }
}
