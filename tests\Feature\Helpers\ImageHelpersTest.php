<?php

namespace Tests\Feature\Helpers;

use App\Services\ImageService;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Config;
use Tests\TestCase;

class ImageHelpersTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        
        // Set up test configuration
        Config::set('image.max_file_size', 10 * 1024 * 1024);
        Config::set('image.allowed_mimes', ['image/jpeg', 'image/png', 'image/gif', 'image/webp']);
        Config::set('image.allowed_extensions', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
        Config::set('image.enable_optimization', true);
        Config::set('image.enable_webp_conversion', true);
        Config::set('image.virus_scan.enabled', false);
        Config::set('image.storage.disk', 'testing');
        Config::set('image.storage.path', 'images');
        Config::set('image.storage.temp_path', 'temp/images');
        Config::set('image.logging.enabled', false);
        
        Config::set('filesystems.disks.testing', [
            'driver' => 'local',
            'root' => storage_path('app/testing'),
        ]);
        
        Storage::fake('testing');
    }
    
    protected function tearDown(): void
    {
        Storage::disk('testing')->deleteDirectory('');
        parent::tearDown();
    }
    
    /** @test */
    public function image_service_helper_returns_service_instance()
    {
        $service = image_service();
        
        $this->assertInstanceOf(ImageService::class, $service);
    }
    
    /** @test */
    public function process_image_helper_works()
    {
        $file = UploadedFile::fake()->image('helper-test.jpg', 400, 300)->size(512);
        
        $result = process_image($file, [
            'subdirectory' => 'helper-test',
            'create_variants' => false,
            'create_webp' => false
        ]);
        
        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('original_path', $result);
    }
    
    /** @test */
    public function quick_image_upload_helper_works()
    {
        $file = UploadedFile::fake()->image('quick-helper.jpg', 400, 300)->size(512);
        
        $result = quick_image_upload($file, 'quick-helper-test');
        
        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('original_path', $result);
        $this->assertNotNull($result['webp_path']);
        $this->assertEmpty($result['variants']);
    }
    
    /** @test */
    public function full_image_upload_helper_works()
    {
        $file = UploadedFile::fake()->image('full-helper.jpg', 800, 600)->size(1024);
        
        $result = full_image_upload($file, 'full-helper-test');
        
        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('original_path', $result);
        $this->assertNotNull($result['webp_path']);
        $this->assertNotEmpty($result['variants']);
    }
    
    /** @test */
    public function validate_image_file_helper_works()
    {
        $file = UploadedFile::fake()->image('validate-helper.jpg', 400, 300)->size(512);
        
        $result = validate_image_file($file);
        
        $this->assertTrue($result['valid']);
        $this->assertEmpty($result['errors']);
        $this->assertArrayHasKey('file_info', $result);
    }
    
    /** @test */
    public function validate_image_file_helper_rejects_invalid_files()
    {
        $file = UploadedFile::fake()->create('document.pdf', 1024, 'application/pdf');
        
        $result = validate_image_file($file);
        
        $this->assertFalse($result['valid']);
        $this->assertNotEmpty($result['errors']);
    }
    
    /** @test */
    public function sanitize_image_filename_helper_works()
    {
        $dangerous = '../../../etc/passwd.jpg';
        $safe = sanitize_image_filename($dangerous);
        
        $this->assertStringNotContainsString('..', $safe);
        $this->assertStringNotContainsString('/', $safe);
        $this->assertStringContainsString('.jpg', $safe);
    }
    
    /** @test */
    public function scan_image_for_viruses_helper_works()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_file');
        file_put_contents($tempPath, 'clean content');
        
        $result = scan_image_for_viruses($tempPath);
        
        $this->assertTrue($result['clean']);
        
        unlink($tempPath);
    }
    
    /** @test */
    public function get_image_url_helper_works()
    {
        Storage::disk('testing')->put('test-url.jpg', 'fake content');
        
        $url = get_image_url('test-url.jpg');
        
        $this->assertIsString($url);
        $this->assertStringContainsString('test-url.jpg', $url);
    }
    
    /** @test */
    public function get_image_info_helper_works()
    {
        // Create a test image
        $tempPath = tempnam(sys_get_temp_dir(), 'test_image');
        $image = imagecreate(200, 150);
        imagejpeg($image, $tempPath);
        imagedestroy($image);
        
        $info = get_image_info($tempPath);
        
        $this->assertTrue($info['exists']);
        $this->assertEquals(200, $info['width']);
        $this->assertEquals(150, $info['height']);
        
        unlink($tempPath);
    }
    
    /** @test */
    public function get_image_info_helper_handles_non_existent_files()
    {
        $info = get_image_info('/non/existent/file.jpg');
        
        $this->assertFalse($info['exists']);
    }
    
    /** @test */
    public function optimize_image_helper_works()
    {
        // Create a test image
        $tempPath = tempnam(sys_get_temp_dir(), 'test_image');
        $image = imagecreate(100, 100);
        imagejpeg($image, $tempPath);
        imagedestroy($image);
        
        $result = optimize_image($tempPath);
        
        $this->assertTrue($result);
        
        unlink($tempPath);
    }
    
    /** @test */
    public function resize_image_helper_works()
    {
        // Create a test image
        $tempPath = tempnam(sys_get_temp_dir(), 'test_image');
        $image = imagecreate(200, 200);
        imagejpeg($image, $tempPath);
        imagedestroy($image);
        
        $result = resize_image($tempPath, 100, 100);
        
        $this->assertTrue($result);
        
        // Verify the image was resized
        $imageInfo = getimagesize($tempPath);
        $this->assertEquals(100, $imageInfo[0]);
        $this->assertEquals(100, $imageInfo[1]);
        
        unlink($tempPath);
    }
    
    /** @test */
    public function convert_to_webp_helper_works()
    {
        // Create a test image
        $tempPath = tempnam(sys_get_temp_dir(), 'test_image');
        $image = imagecreate(100, 100);
        imagejpeg($image, $tempPath);
        imagedestroy($image);
        
        $webpPath = convert_to_webp($tempPath);
        
        $this->assertNotNull($webpPath);
        $this->assertStringContainsString('.webp', $webpPath);
        $this->assertTrue(file_exists($webpPath));
        
        unlink($tempPath);
        if (file_exists($webpPath)) {
            unlink($webpPath);
        }
    }
    
    /** @test */
    public function delete_image_helper_works()
    {
        // Create a test file in storage
        Storage::disk('testing')->put('delete-helper-test.jpg', 'fake content');
        
        $deleted = delete_image('delete-helper-test.jpg');
        
        $this->assertTrue($deleted);
        $this->assertFalse(Storage::disk('testing')->exists('delete-helper-test.jpg'));
    }
    
    /** @test */
    public function all_helpers_are_available()
    {
        // Test that all helper functions exist
        $this->assertTrue(function_exists('image_service'));
        $this->assertTrue(function_exists('process_image'));
        $this->assertTrue(function_exists('quick_image_upload'));
        $this->assertTrue(function_exists('full_image_upload'));
        $this->assertTrue(function_exists('optimize_image'));
        $this->assertTrue(function_exists('resize_image'));
        $this->assertTrue(function_exists('convert_to_webp'));
        $this->assertTrue(function_exists('validate_image_file'));
        $this->assertTrue(function_exists('scan_image_for_viruses'));
        $this->assertTrue(function_exists('sanitize_image_filename'));
        $this->assertTrue(function_exists('get_image_url'));
        $this->assertTrue(function_exists('delete_image'));
        $this->assertTrue(function_exists('get_image_info'));
    }
}
