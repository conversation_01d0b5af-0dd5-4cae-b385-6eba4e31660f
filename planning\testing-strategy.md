# Testing Strategy & Test Plans
## ChiSolution Digital Agency Platform

### 🎯 Testing Framework Overview

#### **Testing Pyramid Strategy**
```
                    ┌─────────────────┐
                    │   E2E Tests     │ ← 10% (Critical User Journeys)
                    │   (Selenium)    │
                ┌───┴─────────────────┴───┐
                │   Integration Tests     │ ← 20% (API & Service Integration)
                │   (Laravel Feature)     │
            ┌───┴─────────────────────────┴───┐
            │        Unit Tests               │ ← 70% (Business Logic & Models)
            │     (PHPUnit)                   │
        ┌───┴─────────────────────────────────┴───┐
        │           Static Analysis               │ ← Code Quality & Security
        │    (PHPStan, Psalm, Security)           │
    └─────────────────────────────────────────────┘
```

#### **Testing Types & Coverage**
```
Unit Tests (70%):
├── Model Tests
├── Service Layer Tests
├── Repository Tests
├── Utility Function Tests
└── Validation Rule Tests

Integration Tests (20%):
├── API Endpoint Tests
├── Database Integration Tests
├── External Service Tests
├── Email/Notification Tests
└── File Upload Tests

End-to-End Tests (10%):
├── User Registration Flow
├── Product Purchase Flow
├── Project Management Flow
├── Admin Dashboard Flow
└── Multi-language Navigation

Performance Tests:
├── Load Testing
├── Stress Testing
├── Database Performance
└── API Response Times

Security Tests:
├── Authentication Tests
├── Authorization Tests
├── Input Validation Tests
└── SQL Injection Tests
```

### 🧪 Unit Testing Strategy

#### **Model Testing**
```php
// tests/Unit/Models/ProductTest.php
class ProductTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_create_a_product_with_valid_data()
    {
        $productData = [
            'name' => 'Test Product',
            'slug' => 'test-product',
            'price' => 999.99,
            'description' => 'Test description',
            'is_active' => true,
            'is_deleted' => false
        ];

        $product = Product::create($productData);

        $this->assertInstanceOf(Product::class, $product);
        $this->assertEquals('Test Product', $product->name);
        $this->assertEquals(999.99, $product->price);
        $this->assertTrue($product->is_active);
    }

    /** @test */
    public function it_generates_slug_automatically_if_not_provided()
    {
        $product = Product::factory()->create(['name' => 'Test Product']);
        
        $this->assertEquals('test-product', $product->slug);
    }

    /** @test */
    public function it_can_have_multiple_categories()
    {
        $product = Product::factory()->create();
        $categories = Category::factory()->count(3)->create();
        
        $product->categories()->attach($categories->pluck('id'));
        
        $this->assertCount(3, $product->categories);
    }

    /** @test */
    public function it_calculates_discounted_price_correctly()
    {
        $product = Product::factory()->create([
            'price' => 1000.00,
            'discount_percentage' => 10
        ]);
        
        $this->assertEquals(900.00, $product->discounted_price);
    }

    /** @test */
    public function it_scopes_active_products_correctly()
    {
        Product::factory()->create(['is_active' => true, 'is_deleted' => false]);
        Product::factory()->create(['is_active' => false, 'is_deleted' => false]);
        Product::factory()->create(['is_active' => true, 'is_deleted' => true]);
        
        $activeProducts = Product::active()->get();
        
        $this->assertCount(1, $activeProducts);
    }
}
```

#### **Service Layer Testing**
```php
// tests/Unit/Services/OrderServiceTest.php
class OrderServiceTest extends TestCase
{
    use RefreshDatabase;

    private OrderService $orderService;
    private User $user;
    private Product $product;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->orderService = app(OrderService::class);
        $this->user = User::factory()->create();
        $this->product = Product::factory()->create(['price' => 100.00]);
    }

    /** @test */
    public function it_creates_order_with_valid_cart_items()
    {
        $cartItems = [
            [
                'product_id' => $this->product->id,
                'quantity' => 2,
                'price' => 100.00
            ]
        ];

        $customerData = [
            'email' => '<EMAIL>',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'phone' => '1234567890'
        ];

        $order = $this->orderService->createOrder($cartItems, $customerData);

        $this->assertInstanceOf(Order::class, $order);
        $this->assertEquals(200.00, $order->total_amount);
        $this->assertCount(1, $order->items);
    }

    /** @test */
    public function it_throws_exception_for_insufficient_inventory()
    {
        $this->product->update(['inventory_quantity' => 1]);
        
        $cartItems = [
            [
                'product_id' => $this->product->id,
                'quantity' => 5,
                'price' => 100.00
            ]
        ];

        $this->expectException(InsufficientInventoryException::class);
        
        $this->orderService->createOrder($cartItems, []);
    }

    /** @test */
    public function it_updates_inventory_after_order_creation()
    {
        $this->product->update(['inventory_quantity' => 10]);
        
        $cartItems = [
            [
                'product_id' => $this->product->id,
                'quantity' => 3,
                'price' => 100.00
            ]
        ];

        $this->orderService->createOrder($cartItems, []);
        
        $this->product->refresh();
        $this->assertEquals(7, $this->product->inventory_quantity);
    }
}
```

### 🔗 Integration Testing

#### **API Integration Tests**
```php
// tests/Feature/Api/ProductApiTest.php
class ProductApiTest extends TestCase
{
    use RefreshDatabase;

    private User $admin;
    private User $customer;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->admin = User::factory()->admin()->create();
        $this->customer = User::factory()->customer()->create();
    }

    /** @test */
    public function admin_can_create_product_via_api()
    {
        $productData = [
            'name' => 'New Product',
            'price' => 299.99,
            'description' => 'Product description',
            'category_id' => Category::factory()->create()->id
        ];

        $response = $this->actingAs($this->admin, 'sanctum')
            ->postJson('/api/v1/products', $productData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'id',
                    'name',
                    'slug',
                    'price',
                    'description'
                ]
            ]);

        $this->assertDatabaseHas('products', [
            'name' => 'New Product',
            'price' => 299.99
        ]);
    }

    /** @test */
    public function customer_cannot_create_product_via_api()
    {
        $productData = [
            'name' => 'New Product',
            'price' => 299.99
        ];

        $response = $this->actingAs($this->customer, 'sanctum')
            ->postJson('/api/v1/products', $productData);

        $response->assertStatus(403);
    }

    /** @test */
    public function it_returns_paginated_products()
    {
        Product::factory()->count(25)->create(['is_active' => true]);

        $response = $this->getJson('/api/v1/products?per_page=10');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    '*' => ['id', 'name', 'price']
                ],
                'meta' => [
                    'pagination' => [
                        'current_page',
                        'total_pages',
                        'per_page',
                        'total'
                    ]
                ]
            ]);

        $this->assertEquals(10, count($response->json('data')));
    }

    /** @test */
    public function it_filters_products_by_category()
    {
        $category = Category::factory()->create();
        Product::factory()->count(5)->create(['category_id' => $category->id]);
        Product::factory()->count(3)->create(); // Different category

        $response = $this->getJson("/api/v1/products?category={$category->id}");

        $response->assertStatus(200);
        $this->assertCount(5, $response->json('data'));
    }
}
```

#### **Database Integration Tests**
```php
// tests/Feature/Database/ProductRepositoryTest.php
class ProductRepositoryTest extends TestCase
{
    use RefreshDatabase;

    private ProductRepository $repository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = app(ProductRepository::class);
    }

    /** @test */
    public function it_finds_products_by_search_query()
    {
        Product::factory()->create(['name' => 'Gaming Laptop']);
        Product::factory()->create(['name' => 'Office Laptop']);
        Product::factory()->create(['name' => 'Gaming Mouse']);

        $results = $this->repository->search('gaming');

        $this->assertCount(2, $results);
        $this->assertTrue($results->contains('name', 'Gaming Laptop'));
        $this->assertTrue($results->contains('name', 'Gaming Mouse'));
    }

    /** @test */
    public function it_applies_price_range_filter()
    {
        Product::factory()->create(['price' => 500]);
        Product::factory()->create(['price' => 1500]);
        Product::factory()->create(['price' => 2500]);

        $results = $this->repository->getByPriceRange(1000, 2000);

        $this->assertCount(1, $results);
        $this->assertEquals(1500, $results->first()->price);
    }

    /** @test */
    public function it_eager_loads_relationships_efficiently()
    {
        $products = Product::factory()->count(10)->create();
        
        DB::enableQueryLog();
        
        $results = $this->repository->getWithRelations();
        
        $queries = DB::getQueryLog();
        
        // Should use eager loading to avoid N+1 queries
        $this->assertLessThanOrEqual(3, count($queries));
    }
}
```

### 🌐 End-to-End Testing

#### **User Journey Tests**
```php
// tests/Browser/PurchaseFlowTest.php
class PurchaseFlowTest extends DuskTestCase
{
    use DatabaseMigrations;

    /** @test */
    public function guest_user_can_complete_purchase_flow()
    {
        $product = Product::factory()->create([
            'name' => 'Test Laptop',
            'price' => 1299.99,
            'inventory_quantity' => 10
        ]);

        $this->browse(function (Browser $browser) use ($product) {
            $browser->visit('/')
                    ->clickLink('Shop')
                    ->assertSee('Test Laptop')
                    ->click('@add-to-cart-' . $product->id)
                    ->assertSee('Added to cart')
                    ->click('@cart-icon')
                    ->assertSee('Test Laptop')
                    ->assertSee('R 1,299.99')
                    ->clickLink('Checkout')
                    ->type('email', '<EMAIL>')
                    ->type('first_name', 'John')
                    ->type('last_name', 'Doe')
                    ->type('phone', '0123456789')
                    ->type('address_line_1', '123 Test Street')
                    ->type('city', 'Cape Town')
                    ->type('postal_code', '8001')
                    ->select('country', 'ZA')
                    ->click('@place-order')
                    ->waitForText('Order Confirmation')
                    ->assertSee('Thank you for your order')
                    ->assertSee('Order #');
        });

        $this->assertDatabaseHas('orders', [
            'email' => '<EMAIL>',
            'status' => 'pending'
        ]);
    }

    /** @test */
    public function user_can_switch_languages()
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/')
                    ->assertSee('Digital Agency')
                    ->click('@language-switcher')
                    ->clickLink('Français')
                    ->waitForLocation('/fr')
                    ->assertSee('Agence Numérique')
                    ->click('@language-switcher')
                    ->clickLink('Español')
                    ->waitForLocation('/es')
                    ->assertSee('Agencia Digital');
        });
    }
}
```

#### **Admin Dashboard Tests**
```php
// tests/Browser/AdminDashboardTest.php
class AdminDashboardTest extends DuskTestCase
{
    use DatabaseMigrations;

    private User $admin;

    protected function setUp(): void
    {
        parent::setUp();
        $this->admin = User::factory()->admin()->create();
    }

    /** @test */
    public function admin_can_manage_products()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->admin)
                    ->visit('/admin/dashboard')
                    ->clickLink('Products')
                    ->assertSee('Product Management')
                    ->click('@add-product')
                    ->type('name', 'New Test Product')
                    ->type('price', '599.99')
                    ->type('description', 'Test product description')
                    ->attach('featured_image', __DIR__.'/fixtures/test-image.jpg')
                    ->click('@save-product')
                    ->waitForText('Product created successfully')
                    ->assertSee('New Test Product');
        });

        $this->assertDatabaseHas('products', [
            'name' => 'New Test Product',
            'price' => 599.99
        ]);
    }

    /** @test */
    public function admin_can_view_analytics_dashboard()
    {
        // Create test data
        Order::factory()->count(10)->create();
        User::factory()->count(50)->create();

        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->admin)
                    ->visit('/admin/dashboard')
                    ->assertSee('Total Sales')
                    ->assertSee('Total Users')
                    ->assertSee('Total Orders')
                    ->assertSee('Revenue Chart')
                    ->assertPresent('@sales-chart')
                    ->assertPresent('@user-growth-chart');
        });
    }
}
```

### 🔒 Security Testing

#### **Authentication & Authorization Tests**
```php
// tests/Feature/Security/AuthenticationTest.php
class AuthenticationTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_prevents_brute_force_attacks()
    {
        $user = User::factory()->create();

        // Attempt 6 failed logins
        for ($i = 0; $i < 6; $i++) {
            $this->postJson('/api/login', [
                'email' => $user->email,
                'password' => 'wrong-password'
            ]);
        }

        // 7th attempt should be rate limited
        $response = $this->postJson('/api/login', [
            'email' => $user->email,
            'password' => 'wrong-password'
        ]);

        $response->assertStatus(429); // Too Many Requests
    }

    /** @test */
    public function it_requires_strong_passwords()
    {
        $response = $this->postJson('/api/register', [
            'email' => '<EMAIL>',
            'password' => 'weak',
            'password_confirmation' => 'weak'
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['password']);
    }

    /** @test */
    public function it_prevents_sql_injection_in_search()
    {
        $maliciousQuery = "'; DROP TABLE products; --";
        
        $response = $this->getJson("/api/v1/products/search?q=" . urlencode($maliciousQuery));
        
        $response->assertStatus(200);
        
        // Verify products table still exists
        $this->assertDatabaseHas('products', []);
    }
}
```

### ⚡ Performance Testing

#### **Load Testing Configuration**
```php
// tests/Performance/LoadTest.php
class LoadTest extends TestCase
{
    /** @test */
    public function api_endpoints_respond_within_acceptable_time()
    {
        $endpoints = [
            '/api/v1/products',
            '/api/v1/categories',
            '/api/v1/orders',
        ];

        foreach ($endpoints as $endpoint) {
            $startTime = microtime(true);
            
            $response = $this->getJson($endpoint);
            
            $responseTime = (microtime(true) - $startTime) * 1000; // Convert to ms
            
            $response->assertStatus(200);
            $this->assertLessThan(500, $responseTime, "Endpoint {$endpoint} took {$responseTime}ms");
        }
    }

    /** @test */
    public function database_queries_are_optimized()
    {
        Product::factory()->count(100)->create();
        
        DB::enableQueryLog();
        
        $this->getJson('/api/v1/products?include=category,images');
        
        $queries = DB::getQueryLog();
        
        // Should not exceed 5 queries due to eager loading
        $this->assertLessThanOrEqual(5, count($queries));
    }
}
```

### 📊 Test Coverage & Quality

#### **Coverage Requirements**
```yaml
# phpunit.xml coverage configuration
coverage:
  minimum_coverage: 80%
  
  by_component:
    models: 90%
    services: 85%
    controllers: 75%
    repositories: 90%
    
  exclude:
    - vendor/
    - tests/
    - bootstrap/
    - config/
    - database/migrations/
```

#### **Quality Gates**
```php
// tests/Quality/CodeQualityTest.php
class CodeQualityTest extends TestCase
{
    /** @test */
    public function code_follows_psr12_standards()
    {
        $output = shell_exec('vendor/bin/phpcs --standard=PSR12 app/');
        
        $this->assertEmpty($output, 'Code style violations found');
    }

    /** @test */
    public function code_passes_static_analysis()
    {
        $output = shell_exec('vendor/bin/phpstan analyse app/ --level=8');
        
        $this->assertStringNotContainsString('ERROR', $output);
    }

    /** @test */
    public function no_security_vulnerabilities_detected()
    {
        $output = shell_exec('vendor/bin/security-checker security:check');
        
        $this->assertStringNotContainsString('vulnerability', strtolower($output));
    }
}
```

### 🚀 Continuous Integration

#### **CI/CD Pipeline Tests**
```yaml
# .github/workflows/tests.yml
name: Tests

on: [push, pull_request]

jobs:
  tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: 8.2
        extensions: mbstring, xml, ctype, iconv, intl, pdo_mysql
        
    - name: Install dependencies
      run: composer install --prefer-dist --no-progress
      
    - name: Run tests
      run: |
        php artisan test --coverage --min=80
        vendor/bin/phpstan analyse
        vendor/bin/phpcs --standard=PSR12 app/
        
    - name: Upload coverage
      uses: codecov/codecov-action@v1
```
