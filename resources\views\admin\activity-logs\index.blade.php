@extends('layouts.dashboard')

@section('title', 'Activity Logs - Admin Dashboard')
@section('page_title', 'Activity Logs')

@push('styles')
<style>
    .risk-minimal { @apply bg-green-100 text-green-800; }
    .risk-low { @apply bg-yellow-100 text-yellow-800; }
    .risk-medium { @apply bg-orange-100 text-orange-800; }
    .risk-high { @apply bg-red-100 text-red-800; }

    .status-success { @apply bg-green-100 text-green-800; }
    .status-failed { @apply bg-red-100 text-red-800; }
    .status-blocked { @apply bg-orange-100 text-orange-800; }
    .status-pending { @apply bg-yellow-100 text-yellow-800; }
</style>
@endpush

@section('content')
<div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Activity Logs</h1>
            <p class="text-gray-600">Monitor password reset requests and security activities</p>
        </div>
        <div class="flex space-x-3">
            <button onclick="refreshLogs()"
                    class="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                </svg>
                Refresh
            </button>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-6">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <span class="text-white text-sm font-bold">📊</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Logs</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $logs->total() }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <span class="text-white text-sm font-bold">✅</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Success</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $logs->where('status', 'success')->count() }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                            <span class="text-white text-sm font-bold">❌</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Failed</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $logs->where('status', 'failed')->count() }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                            <span class="text-white text-sm font-bold">⚠️</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Suspicious</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $logs->where('is_suspicious', true)->count() }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                            <span class="text-white text-sm font-bold">🔍</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Enumeration</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $logs->where('request_data.enumeration_attempt', true)->count() }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Filters</h3>
            <form id="filter-form" method="GET" class="grid grid-cols-1 md:grid-cols-6 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Activity Type</label>
                            <select name="activity_type" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                                <option value="">All Types</option>
                                @foreach($activityTypes as $type)
                                    <option value="{{ $type }}" {{ request('activity_type') == $type ? 'selected' : '' }}>
                                        {{ ucwords(str_replace('_', ' ', $type)) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700">Status</label>
                            <select name="status" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                                <option value="">All Statuses</option>
                                @foreach($statuses as $status)
                                    <option value="{{ $status }}" {{ request('status') == $status ? 'selected' : '' }}>
                                        {{ ucfirst($status) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700">Email</label>
                            <input type="text" name="email" value="{{ request('email') }}" 
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
                                   placeholder="Search email...">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700">IP Address</label>
                            <input type="text" name="ip_address" value="{{ request('ip_address') }}" 
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
                                   placeholder="IP address...">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700">Date From</label>
                            <input type="date" name="date_from" value="{{ request('date_from') }}" 
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                        </div>

                        <div class="flex items-end space-x-2">
                            <div class="flex-1">
                                <label class="block text-sm font-medium text-gray-700">Quick Filters</label>
                                <div class="mt-1 space-x-2">
                                    <label class="inline-flex items-center">
                                        <input type="checkbox" name="enumeration_only" value="1" {{ request('enumeration_only') ? 'checked' : '' }}
                                               class="rounded border-gray-300 text-blue-600 shadow-sm">
                                        <span class="ml-2 text-sm text-gray-700">Email Enumeration Only</span>
                                    </label>
                                </div>
                            </div>
                            <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                                Filter
                            </button>
                        </div>
                    </form>
                </div>
            </div>

    <!-- Activity Logs Table -->
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
        <div class="px-4 py-5 sm:px-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Recent Activity</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">Password reset requests and security events</p>
        </div>
        <ul class="divide-y divide-gray-200">
            @forelse($logs as $log)
                <li class="px-4 py-4 hover:bg-gray-50 {{ $log->is_suspicious ? 'bg-red-50 border-l-4 border-red-400' : '' }}">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 space-y-1">
                                        @if($log->is_suspicious)
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                ⚠️ Suspicious
                                            </span>
                                        @else
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium status-{{ $log->status }}">
                                                {{ ucfirst($log->status) }}
                                            </span>
                                        @endif

                                        @if(isset($log->request_data['enumeration_attempt']) && $log->request_data['enumeration_attempt'])
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                                🔍 Email Enumeration
                                            </span>
                                        @endif
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">
                                            {{ $log->activity_description }}
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            {{ $log->user_email ?? 'Unknown' }} • {{ $log->ip_address }} • {{ $log->location_info }}
                                        </div>
                                        <div class="text-xs text-gray-400">
                                            {{ $log->device_info }}
                                        </div>

                                        @if(isset($log->request_data['admin_notes']))
                                            <div class="text-xs mt-1 {{ $log->request_data['enumeration_attempt'] ? 'text-orange-600 font-medium' : 'text-green-600' }}">
                                                📝 {{ $log->request_data['admin_notes'] }}
                                            </div>
                                        @endif
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium risk-{{ $log->risk_level }}">
                                        Risk: {{ $log->risk_score }}
                                    </span>
                                    <div class="text-sm text-gray-500">
                                        {{ $log->occurred_at->format('M j, Y H:i') }}
                                    </div>
                                    <a href="{{ route('admin.activity-logs.show', $log) }}"
                                       class="text-blue-600 hover:text-blue-900 text-sm font-medium">
                                        View Details
                                    </a>
                                </div>
                            </div>
                            @if($log->failure_reason)
                                <div class="mt-2 text-sm text-red-600">
                                    <strong>Failure Reason:</strong> {{ $log->failure_reason }}
                                </div>
                            @endif
                            @if($log->security_notes)
                                <div class="mt-2 text-sm text-orange-600 bg-orange-50 p-2 rounded">
                                    <strong>🔒 Security Alert:</strong> {{ $log->security_notes }}
                                </div>
                            @endif
                        </li>
            @empty
                <li class="px-4 py-8 text-center text-gray-500">
                    No activity logs found.
                </li>
            @endforelse
        </ul>

        <!-- Pagination -->
        @if($logs->hasPages())
            <div class="px-4 py-3 border-t border-gray-200">
                {{ $logs->links() }}
            </div>
        @endif
    </div>
</div>
@endsection

@push('scripts')
<script>
function refreshLogs() {
    window.location.reload();
}

// Auto-refresh every 30 seconds
let autoRefreshInterval;

function startAutoRefresh() {
    autoRefreshInterval = setInterval(function() {
        // Only refresh if user is still on the page and tab is active
        if (!document.hidden) {
            refreshLogs();
        }
    }, 30000); // 30 seconds
}

function stopAutoRefresh() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
    }
}

// Start auto-refresh when page loads
document.addEventListener('DOMContentLoaded', function() {
    startAutoRefresh();

    // Stop auto-refresh when page is hidden
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            stopAutoRefresh();
        } else {
            startAutoRefresh();
        }
    });

    // Filter form auto-submit on change
    const filterForm = document.getElementById('filter-form');
    if (filterForm) {
        const filterInputs = filterForm.querySelectorAll('select, input[type="date"]');
        filterInputs.forEach(input => {
            input.addEventListener('change', function() {
                filterForm.submit();
            });
        });
    }

    // Add click handlers for expandable log details
    const logItems = document.querySelectorAll('.log-item');
    logItems.forEach(item => {
        item.addEventListener('click', function() {
            const details = this.querySelector('.log-details');
            if (details) {
                details.classList.toggle('hidden');
            }
        });
    });
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    stopAutoRefresh();
});
</script>
@endpush
