@extends('layouts.app')

@section('title', __('common.contact_us') . ' - ' . __('common.company_name'))
@section('meta_description', 'Get in touch with ChiSolution for your digital project needs. Contact us for web development, mobile apps, and digital marketing services.')
@section('meta_keywords', 'contact us, get quote, digital agency contact, web development inquiry, mobile app quote')

@section('content')
<!-- Hero Section -->
<section class="relative bg-gradient-blue text-white overflow-hidden">
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></g></svg>');"></div>
    </div>
    
    <div class="container mx-auto px-4 py-20 lg:py-32 relative z-10">
        <div class="text-center max-w-4xl mx-auto">
            <h1 class="heading-1 text-white mb-6">
                Get In <span class="text-blue-300">Touch</span>
            </h1>
            <p class="text-lead text-blue-100 mb-8">
                Ready to start your digital transformation? We'd love to hear about your project and discuss how we can help bring your vision to life.
            </p>
        </div>
    </div>
    
    <div class="absolute bottom-0 left-0 w-full">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" class="relative block w-full h-16 fill-white">
            <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"></path>
        </svg>
    </div>
</section>

<!-- Contact Section -->
<section class="py-20 bg-white">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <!-- Contact Form -->
            <div class="bg-white rounded-2xl shadow-lg p-8 border border-gray-100">
                <div class="text-center mb-8">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                        </svg>
                    </div>
                    <h2 class="heading-2 mb-4">
                        Send Us a <span class="text-blue-600">Message</span>
                    </h2>
                    <p class="text-gray-600 leading-relaxed">
                        Fill out the form below and we'll get back to you within 24 hours. For urgent inquiries, please call us directly.
                    </p>
                    <div class="mt-6">
                        <a href="{{ route('project-applications.create') }}"
                           class="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            Start Project Application
                        </a>
                    </div>
                </div>
                
                <form class="space-y-8" action="{{ route('contact.submit') }}" method="POST" enctype="multipart/form-data">
                    @csrf

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div class="floating-input-group">
                            <input type="text" id="full_name" name="full_name" required class="floating-input peer" placeholder=" ">
                            <label for="full_name" class="floating-label">Full Name *</label>
                        </div>
                        <div class="floating-input-group">
                            <input type="email" id="email" name="email" required class="floating-input peer" placeholder=" ">
                            <label for="email" class="floating-label">Email Address *</label>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div class="floating-input-group">
                            <input type="tel" id="phone" name="phone" class="floating-input peer" placeholder=" ">
                            <label for="phone" class="floating-label">Phone Number</label>
                        </div>
                        <div class="floating-input-group">
                            <input type="text" id="company" name="company" class="floating-input peer" placeholder=" ">
                            <label for="company" class="floating-label">Company</label>
                        </div>
                    </div>

                    <div class="floating-input-group">
                        <select id="service" name="service" class="floating-input peer">
                            <option value=""></option>
                            <option value="web-development">Web Development</option>
                            <option value="mobile-app-development">Mobile App Development</option>
                            <option value="ecommerce-development">E-commerce Development</option>
                            <option value="digital-marketing">Digital Marketing</option>
                            <option value="seo-services">SEO Services</option>
                            <option value="maintenance-support">Maintenance & Support</option>
                            <option value="other">Other</option>
                        </select>
                        <label for="service" class="floating-label">Service Interested In</label>
                    </div>

                    <div class="floating-input-group">
                        <textarea id="message" name="message" rows="5" required class="floating-input peer resize-vertical" placeholder=" "></textarea>
                        <label for="message" class="floating-label">Project Details *</label>
                    </div>

                    <!-- File Upload Section -->
                    <div class="space-y-4">
                        <div class="flex items-center space-x-2">
                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                            </svg>
                            <h3 class="text-lg font-semibold text-gray-900">Project Attachments</h3>
                            <span class="text-sm text-gray-500">(Optional)</span>
                        </div>

                        <div class="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-6 hover:border-blue-400 transition-colors duration-200">
                            <div class="text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                                <div class="mt-4">
                                    <label for="attachments" class="cursor-pointer">
                                        <span class="mt-2 block text-sm font-medium text-gray-900">
                                            Upload project files, documents, or images
                                        </span>
                                        <span class="mt-1 block text-xs text-gray-500">
                                            PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT, CSV, ZIP, JPG, PNG, WebP up to 25MB each
                                        </span>
                                    </label>
                                    <input id="attachments" name="attachments[]" type="file" class="sr-only" multiple
                                           accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.csv,.zip,.jpg,.jpeg,.png,.webp">
                                </div>
                                <p class="text-xs text-gray-500 mt-2">
                                    All files are scanned for security before processing
                                </p>
                            </div>
                        </div>

                        <!-- File Preview Area -->
                        <div id="file-preview" class="hidden space-y-2">
                            <h4 class="text-sm font-medium text-gray-900">Selected Files:</h4>
                            <div id="file-list" class="space-y-2"></div>
                        </div>
                    </div>

                    <div class="flex items-center">
                        <input type="checkbox" id="newsletter" name="newsletter" class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                        <label for="newsletter" class="ml-2 text-sm text-gray-600">
                            I'd like to receive updates about ChiSolution services and industry insights
                        </label>
                    </div>
                    
                    <button type="submit" class="btn-primary w-full transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl">
                        Send Message
                        <svg class="ml-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                        </svg>
                    </button>
                </form>
            </div>
            
            <!-- Contact Information -->
            <div class="space-y-8">
                <div class="text-center lg:text-left">
                    <h2 class="heading-2 mb-4">
                        Contact <span class="text-blue-600">Information</span>
                    </h2>
                    <p class="text-gray-600 leading-relaxed">
                        We're here to help! Reach out to us through any of the following channels, and we'll respond as quickly as possible.
                    </p>
                </div>
                
                <div class="space-y-6">
                    <!-- Phone -->
                    <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300">
                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-1">Phone</h3>
                                <p class="text-gray-600 font-medium">+27 123 456 789</p>
                                <p class="text-sm text-gray-500">Mon-Fri 8:00 AM - 6:00 PM SAST</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Email -->
                    <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300">
                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-1">Email</h3>
                                <p class="text-gray-600 font-medium"><EMAIL></p>
                                <p class="text-sm text-gray-500">We'll respond within 24 hours</p>
                            </div>
                        </div>
                    </div>

                    <!-- Address -->
                    <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300">
                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-1">Office</h3>
                                <p class="text-gray-600 font-medium">123 Business District<br>Cape Town, South Africa</p>
                                <p class="text-sm text-gray-500">By appointment only</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Social Media -->
                    <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300">
                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-3">Follow Us</h3>
                                <div class="flex space-x-3">
                                    <!-- LinkedIn -->
                                    <a href="#" class="social-icon group" title="LinkedIn">
                                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                        </svg>
                                    </a>

                                    <!-- X (Twitter) -->
                                    <a href="#" class="social-icon group" title="X (Twitter)">
                                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                                        </svg>
                                    </a>

                                    <!-- Facebook -->
                                    <a href="#" class="social-icon group" title="Facebook">
                                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                        </svg>
                                    </a>

                                    <!-- Instagram -->
                                    <a href="#" class="social-icon group" title="Instagram">
                                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                                        </svg>
                                    </a>
                                </div>
                                <p class="text-sm text-gray-500 mt-3">Stay updated with our latest work</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Response Time -->
                <div class="bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl p-6 border border-blue-200">
                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Quick Response Guarantee</h3>
                            <p class="text-gray-700 leading-relaxed">
                                We understand that time is valuable. That's why we guarantee a response to all inquiries within 24 hours during business days. For urgent matters, please call us directly.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="heading-2 mb-4">
                Frequently Asked <span class="text-blue-600">Questions</span>
            </h2>
            <p class="text-lead max-w-3xl mx-auto">
                Here are some common questions we receive. If you don't find your answer here, please don't hesitate to contact us.
            </p>
        </div>
        
        <div class="max-w-4xl mx-auto">
            <div class="faq-accordion space-y-4">
                <!-- FAQ Item 1 -->
                <div class="faq-item bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <button class="faq-trigger w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors focus:outline-none focus:bg-gray-50">
                        <h3 class="text-lg font-semibold text-gray-900">How long does a typical project take?</h3>
                        <svg class="faq-icon w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                    <div class="faq-content max-h-0 overflow-hidden transition-all duration-300 ease-in-out">
                        <div class="px-6 pb-4">
                            <p class="text-gray-600 leading-relaxed">
                                Project timelines vary depending on complexity and scope. A simple website might take 2-4 weeks, while a complex web application or mobile app could take 3-6 months. We'll provide a detailed timeline during our initial consultation.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- FAQ Item 2 -->
                <div class="faq-item bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <button class="faq-trigger w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors focus:outline-none focus:bg-gray-50">
                        <h3 class="text-lg font-semibold text-gray-900">Do you provide ongoing support after project completion?</h3>
                        <svg class="faq-icon w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                    <div class="faq-content max-h-0 overflow-hidden transition-all duration-300 ease-in-out">
                        <div class="px-6 pb-4">
                            <p class="text-gray-600 leading-relaxed">
                                Yes! We offer comprehensive maintenance and support packages to keep your digital assets running smoothly. This includes security updates, performance monitoring, content updates, and technical support.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- FAQ Item 3 -->
                <div class="faq-item bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <button class="faq-trigger w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors focus:outline-none focus:bg-gray-50">
                        <h3 class="text-lg font-semibold text-gray-900">What is your payment structure?</h3>
                        <svg class="faq-icon w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                    <div class="faq-content max-h-0 overflow-hidden transition-all duration-300 ease-in-out">
                        <div class="px-6 pb-4">
                            <p class="text-gray-600 leading-relaxed">
                                We typically work with a 50% upfront payment and 50% upon completion for smaller projects. For larger projects, we can arrange milestone-based payments. We accept bank transfers, credit cards, and other secure payment methods.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- FAQ Item 4 -->
                <div class="faq-item bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <button class="faq-trigger w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors focus:outline-none focus:bg-gray-50">
                        <h3 class="text-lg font-semibold text-gray-900">Can you work with our existing team?</h3>
                        <svg class="faq-icon w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                    <div class="faq-content max-h-0 overflow-hidden transition-all duration-300 ease-in-out">
                        <div class="px-6 pb-4">
                            <p class="text-gray-600 leading-relaxed">
                                Absolutely! We're experienced in collaborating with in-house teams, other agencies, and stakeholders. We can integrate seamlessly into your existing workflow and communication processes.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- FAQ Item 5 -->
                <div class="faq-item bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <button class="faq-trigger w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors focus:outline-none focus:bg-gray-50">
                        <h3 class="text-lg font-semibold text-gray-900">Do you offer free consultations?</h3>
                        <svg class="faq-icon w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                    <div class="faq-content max-h-0 overflow-hidden transition-all duration-300 ease-in-out">
                        <div class="px-6 pb-4">
                            <p class="text-gray-600 leading-relaxed">
                                Yes! We offer free initial consultations to discuss your project requirements, understand your goals, and provide recommendations. This helps us create a tailored proposal that meets your specific needs and budget.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@push('styles')
<style>
/* Social Icons */
.social-icon {
    @apply w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center text-gray-600 hover:bg-blue-600 hover:text-white transition-all duration-300 transform hover:scale-110;
}

/* FAQ Accordion */
.faq-item.active .faq-icon {
    transform: rotate(180deg);
}

.faq-item.active .faq-content {
    max-height: 200px;
}

.faq-item.active .faq-trigger {
    background-color: #f9fafb;
}

/* Floating Label Form Styles */
.floating-input-group {
    position: relative;
    margin-bottom: 2rem;
}

.floating-input {
    width: 100%;
    padding: 1.25rem 0 0.75rem 0;
    border: none;
    border-bottom: 2px solid #e5e7eb;
    background: transparent;
    outline: none;
    font-size: 1rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    color: #374151;
    font-weight: 400;
}

.floating-input:focus {
    border-bottom-color: #3b82f6;
    box-shadow: 0 1px 0 0 #3b82f6;
}

.floating-input:focus ~ .floating-label,
.floating-input:not(:placeholder-shown) ~ .floating-label,
.floating-input.has-value ~ .floating-label {
    transform: translateY(-1.75rem) scale(0.85);
    color: #3b82f6;
    font-weight: 500;
}

.floating-label {
    position: absolute;
    top: 1.25rem;
    left: 0;
    font-size: 1rem;
    color: #9ca3af;
    pointer-events: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: left top;
    background: transparent;
    z-index: 1;
}

/* Select specific styles */
.floating-input-group select.floating-input {
    padding: 1.25rem 2.5rem 0.75rem 0;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.25em 1.25em;
    cursor: pointer;
}

.floating-input-group select.floating-input:focus ~ .floating-label,
.floating-input-group select.floating-input:valid ~ .floating-label,
.floating-input-group select.floating-input.has-value ~ .floating-label {
    transform: translateY(-1.75rem) scale(0.85);
    color: #3b82f6;
    font-weight: 500;
}

/* Textarea specific styles */
.floating-input-group textarea.floating-input {
    min-height: 140px;
    resize: vertical;
    padding-top: 1.75rem;
    line-height: 1.5;
}

/* Hover effects */
.floating-input-group:hover .floating-input:not(:focus) {
    border-bottom-color: #d1d5db;
}

.floating-input-group:hover .floating-label {
    color: #6b7280;
}

/* Error states */
.floating-input.error {
    border-bottom-color: #ef4444;
}

.floating-input.error ~ .floating-label {
    color: #ef4444;
}

/* Success states */
.floating-input.success {
    border-bottom-color: #10b981;
}

.floating-input.success ~ .floating-label {
    color: #10b981;
}

/* Disabled states */
.floating-input:disabled {
    border-bottom-color: #d1d5db;
    color: #9ca3af;
    cursor: not-allowed;
}

.floating-input:disabled ~ .floating-label {
    color: #d1d5db;
}

/* Animation for form load */
.floating-input-group {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.6s ease forwards;
}

.floating-input-group:nth-child(1) { animation-delay: 0.1s; }
.floating-input-group:nth-child(2) { animation-delay: 0.2s; }
.floating-input-group:nth-child(3) { animation-delay: 0.3s; }
.floating-input-group:nth-child(4) { animation-delay: 0.4s; }
.floating-input-group:nth-child(5) { animation-delay: 0.5s; }

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Focus ring for accessibility */
.floating-input:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
}

/* Required field indicator */
.floating-label:after {
    content: '';
}

.floating-label[for="full_name"]:after,
.floating-label[for="email"]:after,
.floating-label[for="message"]:after {
    content: ' *';
    color: #ef4444;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Floating Label Form Enhancement
    const floatingInputs = document.querySelectorAll('.floating-input');

    floatingInputs.forEach(input => {
        // Handle initial state for pre-filled inputs
        if (input.value.trim() !== '') {
            input.classList.add('has-value');
        }

        // Handle input events
        input.addEventListener('input', function() {
            if (this.value.trim() !== '') {
                this.classList.add('has-value');
            } else {
                this.classList.remove('has-value');
            }
        });

        // Handle focus events
        input.addEventListener('focus', function() {
            this.classList.add('focused');
        });

        // Handle blur events
        input.addEventListener('blur', function() {
            this.classList.remove('focused');
            if (this.value.trim() !== '') {
                this.classList.add('has-value');
            } else {
                this.classList.remove('has-value');
            }
        });

        // Special handling for select elements
        if (input.tagName === 'SELECT') {
            input.addEventListener('change', function() {
                if (this.value !== '') {
                    this.classList.add('has-value');
                } else {
                    this.classList.remove('has-value');
                }
            });
        }
    });

    // File Upload Handling
    const fileInput = document.getElementById('attachments');
    const filePreview = document.getElementById('file-preview');
    const fileList = document.getElementById('file-list');
    const maxFileSize = 25 * 1024 * 1024; // 25MB
    const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'text/plain',
        'text/csv',
        'application/zip',
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/webp'
    ];

    if (fileInput) {
        fileInput.addEventListener('change', function(e) {
            const files = Array.from(e.target.files);
            fileList.innerHTML = '';

            if (files.length === 0) {
                filePreview.classList.add('hidden');
                return;
            }

            let validFiles = [];

            files.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'flex items-center justify-between p-3 bg-white border border-gray-200 rounded-lg';

                // Validate file
                let isValid = true;
                let errorMessage = '';

                if (file.size > maxFileSize) {
                    isValid = false;
                    errorMessage = 'File too large (max 25MB)';
                } else if (!allowedTypes.includes(file.type)) {
                    isValid = false;
                    errorMessage = 'File type not allowed';
                }

                if (isValid) {
                    validFiles.push(file);
                }

                const fileInfo = document.createElement('div');
                fileInfo.className = 'flex items-center space-x-3';

                const fileIcon = getFileIcon(file.type);
                const fileSize = formatFileSize(file.size);

                fileInfo.innerHTML = `
                    <div class="w-8 h-8 flex items-center justify-center rounded-lg ${isValid ? 'bg-blue-100' : 'bg-red-100'}">
                        ${fileIcon}
                    </div>
                    <div>
                        <p class="text-sm font-medium ${isValid ? 'text-gray-900' : 'text-red-900'}">${file.name}</p>
                        <p class="text-xs ${isValid ? 'text-gray-500' : 'text-red-500'}">
                            ${isValid ? fileSize : errorMessage}
                        </p>
                    </div>
                `;

                const removeButton = document.createElement('button');
                removeButton.type = 'button';
                removeButton.className = 'text-gray-400 hover:text-red-500 transition-colors duration-200';
                removeButton.innerHTML = `
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                `;

                removeButton.addEventListener('click', function() {
                    fileItem.remove();
                    // Update file input
                    const dt = new DataTransfer();
                    Array.from(fileInput.files).forEach((f, i) => {
                        if (i !== index) dt.items.add(f);
                    });
                    fileInput.files = dt.files;

                    if (fileList.children.length === 0) {
                        filePreview.classList.add('hidden');
                    }
                });

                fileItem.appendChild(fileInfo);
                fileItem.appendChild(removeButton);
                fileList.appendChild(fileItem);
            });

            filePreview.classList.remove('hidden');
        });
    }

    function getFileIcon(mimeType) {
        if (mimeType.startsWith('image/')) {
            return '<svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path></svg>';
        } else if (mimeType === 'application/pdf') {
            return '<svg class="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"></path></svg>';
        } else if (mimeType.includes('spreadsheet') || mimeType.includes('excel')) {
            return '<svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"></path></svg>';
        } else if (mimeType.includes('presentation') || mimeType.includes('powerpoint')) {
            return '<svg class="w-4 h-4 text-orange-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"></path></svg>';
        } else if (mimeType === 'application/zip') {
            return '<svg class="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20"><path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path></svg>';
        } else {
            return '<svg class="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"></path></svg>';
        }
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // FAQ Accordion functionality
    const faqItems = document.querySelectorAll('.faq-item');

    faqItems.forEach(item => {
        const trigger = item.querySelector('.faq-trigger');
        const content = item.querySelector('.faq-content');
        const icon = item.querySelector('.faq-icon');

        trigger.addEventListener('click', function() {
            const isActive = item.classList.contains('active');

            // Close all other FAQ items
            faqItems.forEach(otherItem => {
                if (otherItem !== item) {
                    otherItem.classList.remove('active');
                }
            });

            // Toggle current item
            if (isActive) {
                item.classList.remove('active');
            } else {
                item.classList.add('active');
            }
        });
    });

    // Form submission handling
    const contactForm = document.querySelector('form');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            const submitButton = this.querySelector('button[type="submit"]');
            const originalText = submitButton.innerHTML;

            submitButton.innerHTML = `
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Sending...
            `;
            submitButton.disabled = true;

            // Re-enable button after 3 seconds (in case of error)
            setTimeout(() => {
                submitButton.innerHTML = originalText;
                submitButton.disabled = false;
            }, 3000);
        });
    }
});
</script>
@endpush

@endsection
