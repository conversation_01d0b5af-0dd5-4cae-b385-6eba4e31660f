<?php

namespace App\Http\Middleware;

use Illuminate\Auth\Middleware\Authenticate as Middleware;
use Illuminate\Http\Request;

class Authenticate extends Middleware
{
    /**
     * Get the path the user should be redirected to when they are not authenticated.
     */
    protected function redirectTo(Request $request): ?string
    {
        if ($request->expectsJson()) {
            return null;
        }

        // Store the intended URL in session for later redirect
        if ($request->url() !== route('login') && $request->url() !== route('register')) {
            session(['url.intended' => $request->url()]);
        }

        return route('login');
    }
}
