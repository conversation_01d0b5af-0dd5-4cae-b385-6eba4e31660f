<?php

namespace Tests\Feature\Admin;

use App\Models\Project;
use App\Models\Service;
use App\Models\User;
use App\Models\Role;
use App\Services\ActivityLogger;
use App\Services\ImageService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class ProjectUpdateFixTest extends TestCase
{
    use RefreshDatabase;

    protected User $admin;
    protected User $client;
    protected Project $project;

    protected function setUp(): void
    {
        parent::setUp();

        // Create roles
        $adminRole = Role::factory()->create(['name' => 'admin']);
        $clientRole = Role::factory()->create(['name' => 'client']);

        // Create users
        $this->admin = User::factory()->create(['role_id' => $adminRole->id]);
        $this->client = User::factory()->create(['role_id' => $clientRole->id]);

        // Create project
        $this->project = Project::factory()->create([
            'client_id' => $this->client->id,
        ]);

        // Mock services
        $this->mock(ActivityLogger::class);
    }

    /** @test */
    public function project_update_works_with_correct_image_service_response()
    {
        Storage::fake('public');
        
        // Mock ImageService with correct response structure
        $imageService = $this->mock(ImageService::class);
        $imageService->shouldReceive('deleteImage')
            ->once()
            ->with(\Mockery::type('string'), true)
            ->andReturn(true);
        $imageService->shouldReceive('processUploadedImage')
            ->once()
            ->andReturn([
                'success' => true,
                'original_path' => 'projects/test-image.jpg',
                'webp_path' => 'projects/test-image.webp',
                'variants' => [],
                'file_info' => ['size' => 1024, 'mime' => 'image/jpeg'],
                'processing_time_ms' => 150.5
            ]);

        $file = UploadedFile::fake()->image('project.jpg', 1200, 800);

        $updateData = [
            'title' => 'Updated Project Title',
            'description' => 'Updated project description',
            'client_id' => $this->client->id,
            'client_name' => $this->client->first_name . ' ' . $this->client->last_name,
            'status' => 'completed',
            'featured_image' => $file,
        ];

        $response = $this->actingAs($this->admin)
            ->put(route('admin.projects.update', $this->project), $updateData);

        $response->assertRedirect(route('admin.projects.show', $this->project));
        
        $this->assertDatabaseHas('projects', [
            'id' => $this->project->id,
            'title' => 'Updated Project Title',
            'featured_image' => 'projects/test-image.jpg',
        ]);
    }

    /** @test */
    public function project_update_handles_image_service_failure_gracefully()
    {
        Storage::fake('public');
        
        // Mock ImageService with failure response
        $imageService = $this->mock(ImageService::class);
        $imageService->shouldReceive('deleteImage')
            ->once()
            ->with(\Mockery::type('string'), true)
            ->andReturn(true);
        $imageService->shouldReceive('processUploadedImage')
            ->once()
            ->andReturn([
                'success' => false,
                'errors' => ['File too large', 'Invalid format']
            ]);

        $file = UploadedFile::fake()->image('project.jpg', 1200, 800);

        $updateData = [
            'title' => 'Updated Project Title',
            'description' => 'Updated project description',
            'client_id' => $this->client->id,
            'client_name' => $this->client->first_name . ' ' . $this->client->last_name,
            'status' => 'completed',
            'featured_image' => $file,
        ];

        $response = $this->actingAs($this->admin)
            ->put(route('admin.projects.update', $this->project), $updateData);

        $response->assertRedirect();
        $response->assertSessionHasErrors(['featured_image']);
        
        // Ensure the project wasn't updated due to image failure
        $this->assertDatabaseMissing('projects', [
            'id' => $this->project->id,
            'title' => 'Updated Project Title',
        ]);
    }

    /** @test */
    public function project_update_works_without_image_upload()
    {
        $updateData = [
            'title' => 'Updated Without Image',
            'description' => 'Updated project description',
            'client_id' => $this->client->id,
            'client_name' => $this->client->first_name . ' ' . $this->client->last_name,
            'status' => 'in_progress',
            'priority' => 'high',
            'is_published' => true,
            'is_featured' => false,
        ];

        $response = $this->actingAs($this->admin)
            ->put(route('admin.projects.update', $this->project), $updateData);

        $response->assertRedirect(route('admin.projects.show', $this->project));
        
        $this->assertDatabaseHas('projects', [
            'id' => $this->project->id,
            'title' => 'Updated Without Image',
            'status' => 'in_progress',
            'priority' => 'high',
            'is_published' => true,
            'is_featured' => false,
        ]);
    }

    /** @test */
    public function project_update_validates_client_name_field()
    {
        $updateData = [
            'title' => 'Updated Title',
            'description' => 'Updated description',
            'client_id' => $this->client->id,
            // Missing client_name field
            'status' => 'completed',
        ];

        $response = $this->actingAs($this->admin)
            ->put(route('admin.projects.update', $this->project), $updateData);

        $response->assertSessionHasErrors(['client_name']);
    }

    /** @test */
    public function project_update_preserves_existing_image_when_no_new_upload()
    {
        // Set existing image
        $this->project->update(['featured_image' => 'projects/existing-image.jpg']);

        $updateData = [
            'title' => 'Updated Title Only',
            'description' => $this->project->description,
            'client_id' => $this->client->id,
            'client_name' => $this->client->client_name,
            'status' => 'completed',
        ];

        $response = $this->actingAs($this->admin)
            ->put(route('admin.projects.update', $this->project), $updateData);

        $response->assertRedirect(route('admin.projects.show', $this->project));
        
        $this->assertDatabaseHas('projects', [
            'id' => $this->project->id,
            'title' => 'Updated Title Only',
            'featured_image' => 'projects/existing-image.jpg', // Should be preserved
        ]);
    }
}
