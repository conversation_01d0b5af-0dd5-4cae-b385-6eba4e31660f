<?php

namespace App\Traits;

use App\Models\Translation;
use App\Models\Language;
use Illuminate\Database\Eloquent\Relations\MorphMany;

trait Translatable
{
    /**
     * Get all translations for the model.
     */
    public function translations(): MorphMany
    {
        return $this->morphMany(Translation::class, 'translatable');
    }

    /**
     * Get translation for a specific field and language.
     */
    public function getTranslation(string $field, string $languageCode = null): ?string
    {
        if (!$languageCode) {
            $languageCode = app()->getLocale();
        }

        return Translation::getTranslation($this, $field, $languageCode);
    }

    /**
     * Set translation for a specific field and language.
     */
    public function setTranslation(string $field, string $languageCode, string $value): void
    {
        Translation::setTranslation($this, $field, $languageCode, $value);
    }

    /**
     * Get translated field value or fallback to original.
     */
    public function getTranslatedAttribute(string $field, string $languageCode = null): string
    {
        if (!$languageCode) {
            $languageCode = app()->getLocale();
        }

        $translation = $this->getTranslation($field, $languageCode);
        
        // If no translation found and not default language, try default language
        if (!$translation && $languageCode !== 'en') {
            $translation = $this->getTranslation($field, 'en');
        }

        // If still no translation, return original field value
        return $translation ?: $this->getAttribute($field);
    }

    /**
     * Get all translations for a specific field.
     */
    public function getFieldTranslations(string $field): array
    {
        $translations = [];
        $languages = Language::active()->get();

        foreach ($languages as $language) {
            $translation = $this->getTranslation($field, $language->code);
            if ($translation) {
                $translations[$language->code] = $translation;
            }
        }

        return $translations;
    }

    /**
     * Set multiple translations for a field.
     */
    public function setFieldTranslations(string $field, array $translations): void
    {
        foreach ($translations as $languageCode => $value) {
            if (!empty($value)) {
                $this->setTranslation($field, $languageCode, $value);
            }
        }
    }

    /**
     * Check if model has translation for a specific field and language.
     */
    public function hasTranslation(string $field, string $languageCode): bool
    {
        return !is_null($this->getTranslation($field, $languageCode));
    }

    /**
     * Delete all translations for the model.
     */
    public function deleteTranslations(): void
    {
        $this->translations()->delete();
    }

    /**
     * Delete translations for a specific field.
     */
    public function deleteFieldTranslations(string $field): void
    {
        $this->translations()->where('field_name', $field)->delete();
    }
}
