<?php

namespace App\Services\File\Traits;

use Illuminate\Support\Facades\Config;
use Illuminate\Support\Str;

trait FileSecurity
{
    /**
     * Sanitize filename
     */
    public function sanitizeFilename(string $filename): string
    {
        if (empty($filename)) {
            $filename = 'file_' . time() . '_' . Str::random(8);
        }
        
        // Get file extension
        $extension = pathinfo($filename, PATHINFO_EXTENSION);
        $name = pathinfo($filename, PATHINFO_FILENAME);
        
        // Remove dangerous characters and patterns
        $name = preg_replace('/[^a-zA-Z0-9._-]/', '_', $name);
        $name = preg_replace('/\.+/', '.', $name); // Remove multiple dots
        $name = preg_replace('/_+/', '_', $name); // Remove multiple underscores
        $name = trim($name, '._-');
        
        // Ensure name is not empty
        if (empty($name)) {
            $name = 'file_' . time() . '_' . Str::random(8);
        }
        
        // Limit filename length
        $maxLength = Config::get('file.security.max_filename_length', 255);
        if (strlen($name) > $maxLength - strlen($extension) - 1) {
            $name = substr($name, 0, $maxLength - strlen($extension) - 1);
        }
        
        // Add timestamp and random string to ensure uniqueness
        $uniqueSuffix = '_' . time() . '_' . Str::random(6);
        $name = $name . $uniqueSuffix;
        
        return $extension ? $name . '.' . $extension : $name;
    }
    
    /**
     * Scan file for viruses
     */
    public function scanForViruses(string $filePath): array
    {
        if (!Config::get('file.virus_scan.enabled', false)) {
            return [
                'clean' => true,
                'message' => 'Virus scanning disabled',
                'scanner' => 'disabled',
            ];
        }
        
        $method = Config::get('file.virus_scan.method', 'basic');
        
        switch ($method) {
            case 'clamav':
                return $this->scanWithClamAV($filePath);
            case 'windows_defender':
                return $this->scanWithWindowsDefender($filePath);
            case 'custom':
                return $this->scanWithCustomCommand($filePath);
            case 'basic':
            default:
                return $this->performBasicSecurityCheck($filePath);
        }
    }
    
    /**
     * Scan with ClamAV
     */
    protected function scanWithClamAV(string $filePath): array
    {
        if (!function_exists('exec')) {
            return $this->performBasicSecurityCheck($filePath);
        }
        
        $command = "clamscan --no-summary --infected " . escapeshellarg($filePath) . " 2>&1";
        $output = [];
        $returnCode = 0;
        
        exec($command, $output, $returnCode);
        
        $clean = $returnCode === 0;
        $message = $clean ? 'File is clean' : 'Virus detected: ' . implode(' ', $output);
        
        if (!$clean) {
            $this->handleInfectedFile($filePath);
        }
        
        return [
            'clean' => $clean,
            'message' => $message,
            'scanner' => 'clamav',
            'output' => $output,
        ];
    }
    
    /**
     * Scan with Windows Defender
     */
    protected function scanWithWindowsDefender(string $filePath): array
    {
        if (!function_exists('exec') || !$this->isWindows()) {
            return $this->performBasicSecurityCheck($filePath);
        }
        
        $command = 'powershell.exe -Command "& {Set-MpPreference -DisableRealtimeMonitoring $false; $result = Start-MpScan -ScanType CustomScan -ScanPath ' . escapeshellarg($filePath) . '; if ($result.ScanResults -eq \'NoThreatsFound\') { exit 0 } else { exit 1 }}"';
        $output = [];
        $returnCode = 0;
        
        exec($command, $output, $returnCode);
        
        $clean = $returnCode === 0;
        $message = $clean ? 'File is clean' : 'Threat detected by Windows Defender';
        
        if (!$clean) {
            $this->handleInfectedFile($filePath);
        }
        
        return [
            'clean' => $clean,
            'message' => $message,
            'scanner' => 'windows_defender',
            'output' => $output,
        ];
    }
    
    /**
     * Scan with custom command
     */
    protected function scanWithCustomCommand(string $filePath): array
    {
        $command = Config::get('file.virus_scan.custom_command');
        
        if (empty($command)) {
            return $this->performBasicSecurityCheck($filePath);
        }
        
        $command = str_replace('{file}', escapeshellarg($filePath), $command);
        $output = [];
        $returnCode = 0;
        
        exec($command, $output, $returnCode);
        
        $clean = $returnCode === 0;
        $message = $clean ? 'File is clean' : 'Threat detected by custom scanner';
        
        if (!$clean) {
            $this->handleInfectedFile($filePath);
        }
        
        return [
            'clean' => $clean,
            'message' => $message,
            'scanner' => 'custom',
            'output' => $output,
        ];
    }
    
    /**
     * Perform basic security check
     */
    protected function performBasicSecurityCheck(string $filePath): array
    {
        if (!file_exists($filePath)) {
            return [
                'clean' => false,
                'message' => 'File does not exist',
                'scanner' => 'basic',
            ];
        }
        
        // Check file size (extremely large files might be suspicious)
        $fileSize = filesize($filePath);
        $maxSize = Config::get('file.max_file_size', 50 * 1024 * 1024);
        
        if ($fileSize > $maxSize * 2) { // Allow some flexibility
            return [
                'clean' => false,
                'message' => 'File size is suspiciously large',
                'scanner' => 'basic',
            ];
        }
        
        // Read a portion of the file to check for suspicious content
        $handle = fopen($filePath, 'rb');
        if (!$handle) {
            return [
                'clean' => false,
                'message' => 'Cannot read file',
                'scanner' => 'basic',
            ];
        }
        
        $content = fread($handle, min($fileSize, 1024 * 1024)); // Read up to 1MB
        fclose($handle);
        
        // Check for suspicious patterns
        $suspiciousPatterns = [
            '/eval\s*\(/i',
            '/exec\s*\(/i',
            '/system\s*\(/i',
            '/shell_exec\s*\(/i',
            '/passthru\s*\(/i',
            '/<script/i',
            '/javascript:/i',
            '/vbscript:/i',
            '/powershell/i',
            '/cmd\.exe/i',
            '/CreateObject/i',
            '/WScript\.Shell/i',
        ];
        
        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                return [
                    'clean' => false,
                    'message' => 'Suspicious content detected',
                    'scanner' => 'basic',
                ];
            }
        }
        
        return [
            'clean' => true,
            'message' => 'Basic security check passed',
            'scanner' => 'basic',
        ];
    }
    
    /**
     * Handle infected file
     */
    protected function handleInfectedFile(string $filePath): void
    {
        $deleteInfected = Config::get('file.virus_scan.delete_infected', false);
        
        if ($deleteInfected) {
            if (file_exists($filePath)) {
                unlink($filePath);
            }
            $this->logSecurityEvent('Infected file deleted', ['file_path' => $filePath]);
        } else {
            $quarantinePath = Config::get('file.virus_scan.quarantine_path');
            if ($quarantinePath && is_dir(dirname($quarantinePath))) {
                $quarantineFile = $quarantinePath . '/' . basename($filePath) . '_' . time();
                if (rename($filePath, $quarantineFile)) {
                    $this->logSecurityEvent('Infected file quarantined', [
                        'original_path' => $filePath,
                        'quarantine_path' => $quarantineFile,
                    ]);
                }
            }
        }
    }
    
    /**
     * Remove metadata from file
     */
    public function removeMetadata(string $filePath): bool
    {
        if (!Config::get('file.security.remove_metadata', false)) {
            return true;
        }
        
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        
        switch ($extension) {
            case 'pdf':
                return $this->removePdfMetadata($filePath);
            case 'doc':
            case 'docx':
            case 'xls':
            case 'xlsx':
            case 'ppt':
            case 'pptx':
                return $this->removeOfficeMetadata($filePath);
            default:
                return true; // No metadata removal needed for this file type
        }
    }
    
    /**
     * Remove PDF metadata
     */
    protected function removePdfMetadata(string $filePath): bool
    {
        // This would require a PDF library like TCPDF or similar
        // For now, we'll just return true as a placeholder
        return true;
    }
    
    /**
     * Remove Office document metadata
     */
    protected function removeOfficeMetadata(string $filePath): bool
    {
        // This would require a library like PhpSpreadsheet or similar
        // For now, we'll just return true as a placeholder
        return true;
    }
    
    /**
     * Check if running on Windows
     */
    protected function isWindows(): bool
    {
        return strtoupper(substr(PHP_OS, 0, 3)) === 'WIN';
    }
    

}
