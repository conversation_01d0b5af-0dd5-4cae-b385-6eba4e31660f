<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contact_submissions', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->string('name', 200);
            $table->string('email', 255);
            $table->string('phone', 20)->nullable();
            $table->string('company', 200)->nullable();
            $table->string('service', 100)->nullable();
            $table->string('subject', 300)->nullable();
            $table->longText('message');
            $table->json('attachments')->nullable(); // Store file paths and metadata
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->string('referrer', 1000)->nullable();
            $table->boolean('is_read')->default(false);
            $table->boolean('is_spam')->default(false);
            $table->timestamp('replied_at')->nullable();
            $table->foreignId('replied_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            // Indexes for performance
            $table->index(['email', 'created_at'], 'idx_contact_submissions_email_date');
            $table->index(['is_read', 'is_spam'], 'idx_contact_submissions_read_spam');
            $table->index(['replied_by', 'replied_at'], 'idx_contact_submissions_replied');
            $table->index('created_at', 'idx_contact_submissions_created');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contact_submissions');
    }
};
