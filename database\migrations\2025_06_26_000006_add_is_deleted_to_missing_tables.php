<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add is_deleted to shopping_carts
        Schema::table('shopping_carts', function (Blueprint $table) {
            $table->boolean('is_deleted')->default(false)->after('expires_at');
            $table->index(['user_id', 'is_deleted']);
        });

        // Add is_deleted to cart_items
        Schema::table('cart_items', function (Blueprint $table) {
            $table->boolean('is_deleted')->default(false)->after('price');
            $table->index(['cart_id', 'is_deleted']);
        });

        // Add is_deleted to user_addresses
        Schema::table('user_addresses', function (Blueprint $table) {
            $table->boolean('is_deleted')->default(false)->after('is_default');
            $table->index(['user_id', 'is_deleted']);
        });

        // Add is_deleted to order_items
        Schema::table('order_items', function (Blueprint $table) {
            $table->boolean('is_deleted')->default(false)->after('product_snapshot');
            $table->index(['order_id', 'is_deleted']);
        });

        // Add is_deleted to currencies (for admin management)
        Schema::table('currencies', function (Blueprint $table) {
            $table->boolean('is_deleted')->default(false)->after('is_active');
            $table->index(['code', 'is_active', 'is_deleted']);
        });

        // Add is_deleted to languages (for admin management)
        Schema::table('languages', function (Blueprint $table) {
            $table->boolean('is_deleted')->default(false)->after('is_default');
            $table->index(['code', 'is_active', 'is_deleted']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove from shopping_carts
        Schema::table('shopping_carts', function (Blueprint $table) {
            $table->dropIndex(['user_id', 'is_deleted']);
            $table->dropColumn('is_deleted');
        });

        // Remove from cart_items
        Schema::table('cart_items', function (Blueprint $table) {
            $table->dropIndex(['cart_id', 'is_deleted']);
            $table->dropColumn('is_deleted');
        });

        // Remove from user_addresses
        Schema::table('user_addresses', function (Blueprint $table) {
            $table->dropIndex(['user_id', 'is_deleted']);
            $table->dropColumn('is_deleted');
        });

        // Remove from order_items
        Schema::table('order_items', function (Blueprint $table) {
            $table->dropIndex(['order_id', 'is_deleted']);
            $table->dropColumn('is_deleted');
        });

        // Remove from currencies
        Schema::table('currencies', function (Blueprint $table) {
            $table->dropIndex(['code', 'is_active', 'is_deleted']);
            $table->dropColumn('is_deleted');
        });

        // Remove from languages
        Schema::table('languages', function (Blueprint $table) {
            $table->dropIndex(['code', 'is_active', 'is_deleted']);
            $table->dropColumn('is_deleted');
        });
    }
};
