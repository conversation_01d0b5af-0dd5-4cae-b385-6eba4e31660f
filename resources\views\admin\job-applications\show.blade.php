@extends('layouts.dashboard')

@section('title', 'Application: ' . $jobApplication->full_name . ' - ' . __('common.company_name'))
@section('page_title', 'Job Application Details')

@section('content')
<div class="p-6 space-y-6">
    <!-- Header Section -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <div class="flex items-center space-x-3">
                <h1 class="text-2xl font-bold text-gray-900">{{ $jobApplication->full_name }}</h1>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $jobApplication->status_badge_color }}">
                    {{ \App\Models\JobApplication::getStatusOptions()[$jobApplication->status] ?? $jobApplication->status }}
                </span>
            </div>
            <p class="mt-1 text-sm text-gray-600">
                Applied for {{ $jobApplication->job->title }} • 
                Reference: {{ $jobApplication->reference_number }} • 
                {{ $jobApplication->created_at->format('M j, Y g:i A') }}
            </p>
        </div>
        <div class="mt-4 sm:mt-0 flex space-x-3">
            <a href="{{ route('admin.job-applications.index') }}"
               class="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                Back to Applications
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Personal Information -->
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Personal Information</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <span class="text-sm font-medium text-gray-500">Full Name</span>
                        <p class="text-gray-900">{{ $jobApplication->full_name }}</p>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-500">Email</span>
                        <p class="text-gray-900">
                            <a href="mailto:{{ $jobApplication->email }}" class="text-primary-600 hover:text-primary-800">
                                {{ $jobApplication->email }}
                            </a>
                        </p>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-500">Phone</span>
                        <p class="text-gray-900">
                            <a href="tel:{{ $jobApplication->phone }}" class="text-primary-600 hover:text-primary-800">
                                {{ $jobApplication->phone }}
                            </a>
                        </p>
                    </div>
                    @if($jobApplication->date_of_birth)
                    <div>
                        <span class="text-sm font-medium text-gray-500">Date of Birth</span>
                        <p class="text-gray-900">{{ $jobApplication->date_of_birth->format('M j, Y') }}</p>
                    </div>
                    @endif
                    @if($jobApplication->nationality)
                    <div>
                        <span class="text-sm font-medium text-gray-500">Nationality</span>
                        <p class="text-gray-900">{{ $jobApplication->nationality }}</p>
                    </div>
                    @endif
                    <div>
                        <span class="text-sm font-medium text-gray-500">Country</span>
                        <p class="text-gray-900">{{ $jobApplication->country }}</p>
                    </div>
                </div>

                @if($jobApplication->address)
                <div class="mt-4">
                    <span class="text-sm font-medium text-gray-500">Address</span>
                    <p class="text-gray-900">{{ $jobApplication->address }}</p>
                    @if($jobApplication->city || $jobApplication->province)
                        <p class="text-gray-600 text-sm">
                            {{ $jobApplication->city }}{{ $jobApplication->city && $jobApplication->province ? ', ' : '' }}{{ $jobApplication->province }}
                            {{ $jobApplication->postal_code ? ' ' . $jobApplication->postal_code : '' }}
                        </p>
                    @endif
                </div>
                @endif
            </div>

            <!-- Cover Letter -->
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Cover Letter</h2>
                <div class="prose max-w-none text-gray-700">
                    {!! nl2br(e($jobApplication->cover_letter)) !!}
                </div>
            </div>

            <!-- Experience Summary -->
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Experience Summary</h2>
                <div class="prose max-w-none text-gray-700">
                    {!! nl2br(e($jobApplication->experience_summary)) !!}
                </div>
            </div>

            <!-- Professional Information -->
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Professional Information</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    @if($jobApplication->current_position)
                    <div>
                        <span class="text-sm font-medium text-gray-500">Current Position</span>
                        <p class="text-gray-900">{{ $jobApplication->current_position }}</p>
                    </div>
                    @endif
                    @if($jobApplication->current_company)
                    <div>
                        <span class="text-sm font-medium text-gray-500">Current Company</span>
                        <p class="text-gray-900">{{ $jobApplication->current_company }}</p>
                    </div>
                    @endif
                    @if($jobApplication->current_salary)
                    <div>
                        <span class="text-sm font-medium text-gray-500">Current Salary</span>
                        <p class="text-gray-900">ZAR {{ number_format($jobApplication->current_salary) }}/month</p>
                    </div>
                    @endif
                    @if($jobApplication->expected_salary)
                    <div>
                        <span class="text-sm font-medium text-gray-500">Expected Salary</span>
                        <p class="text-gray-900 font-semibold text-green-600">ZAR {{ number_format($jobApplication->expected_salary) }}/month</p>
                    </div>
                    @endif
                    @if($jobApplication->notice_period)
                    <div>
                        <span class="text-sm font-medium text-gray-500">Notice Period</span>
                        <p class="text-gray-900">{{ $jobApplication->notice_period }}</p>
                    </div>
                    @endif
                    @if($jobApplication->available_start_date)
                    <div>
                        <span class="text-sm font-medium text-gray-500">Available Start Date</span>
                        <p class="text-gray-900">{{ $jobApplication->available_start_date->format('M j, Y') }}</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Education -->
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Education</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <span class="text-sm font-medium text-gray-500">Highest Qualification</span>
                        <p class="text-gray-900">{{ $jobApplication->highest_qualification }}</p>
                    </div>
                    @if($jobApplication->institution)
                    <div>
                        <span class="text-sm font-medium text-gray-500">Institution</span>
                        <p class="text-gray-900">{{ $jobApplication->institution }}</p>
                    </div>
                    @endif
                    @if($jobApplication->field_of_study)
                    <div>
                        <span class="text-sm font-medium text-gray-500">Field of Study</span>
                        <p class="text-gray-900">{{ $jobApplication->field_of_study }}</p>
                    </div>
                    @endif
                    @if($jobApplication->graduation_year)
                    <div>
                        <span class="text-sm font-medium text-gray-500">Graduation Year</span>
                        <p class="text-gray-900">{{ $jobApplication->graduation_year }}</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Skills & Languages -->
            @if($jobApplication->skills || $jobApplication->languages)
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Skills & Languages</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    @if($jobApplication->skills)
                    <div>
                        <span class="text-sm font-medium text-gray-500 block mb-2">Skills</span>
                        <div class="flex flex-wrap gap-2">
                            @foreach($jobApplication->skills as $skill)
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    {{ $skill }}
                                </span>
                            @endforeach
                        </div>
                    </div>
                    @endif
                    @if($jobApplication->languages)
                    <div>
                        <span class="text-sm font-medium text-gray-500 block mb-2">Languages</span>
                        <div class="flex flex-wrap gap-2">
                            @foreach($jobApplication->languages as $language)
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    {{ $language }}
                                </span>
                            @endforeach
                        </div>
                    </div>
                    @endif
                </div>
            </div>
            @endif

            <!-- Preferences -->
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Preferences & Additional Info</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="flex items-center">
                        <span class="text-sm font-medium text-gray-500 mr-3">Willing to relocate:</span>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $jobApplication->willing_to_relocate ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                            {{ $jobApplication->willing_to_relocate ? 'Yes' : 'No' }}
                        </span>
                    </div>
                    <div class="flex items-center">
                        <span class="text-sm font-medium text-gray-500 mr-3">Willing to travel:</span>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $jobApplication->willing_to_travel ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                            {{ $jobApplication->willing_to_travel ? 'Yes' : 'No' }}
                        </span>
                    </div>
                    <div class="flex items-center">
                        <span class="text-sm font-medium text-gray-500 mr-3">Has driver's license:</span>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $jobApplication->has_drivers_license ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                            {{ $jobApplication->has_drivers_license ? 'Yes' : 'No' }}
                        </span>
                    </div>
                    <div class="flex items-center">
                        <span class="text-sm font-medium text-gray-500 mr-3">Has own transport:</span>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $jobApplication->has_own_transport ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                            {{ $jobApplication->has_own_transport ? 'Yes' : 'No' }}
                        </span>
                    </div>
                </div>

                @if($jobApplication->additional_notes)
                <div class="mt-4">
                    <span class="text-sm font-medium text-gray-500 block mb-2">Additional Notes</span>
                    <div class="prose max-w-none text-gray-700">
                        {!! nl2br(e($jobApplication->additional_notes)) !!}
                    </div>
                </div>
                @endif
            </div>

            <!-- Attachments -->
            @if($jobApplication->attachments && count($jobApplication->attachments) > 0)
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Attachments</h2>
                <div class="space-y-3">
                    @foreach($jobApplication->attachments as $index => $attachment)
                        <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                            <div class="flex items-center space-x-3">
                                <div class="p-2 bg-blue-100 rounded-lg">
                                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">{{ $attachment['original_name'] ?? 'Document ' . ($index + 1) }}</p>
                                    <p class="text-xs text-gray-500">{{ $attachment['size'] ?? 'Unknown size' }}</p>
                                </div>
                            </div>
                            <a href="{{ route('admin.job-applications.download', [$jobApplication, $index]) }}" 
                               class="inline-flex items-center px-3 py-1 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                Download
                            </a>
                        </div>
                    @endforeach
                </div>
            </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Status Management -->
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Application Status</h3>
                
                <form method="POST" action="{{ route('admin.job-applications.update', $jobApplication) }}">
                    @csrf
                    @method('PUT')
                    
                    <div class="space-y-4">
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                            <select name="status" id="status" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                                @foreach(\App\Models\JobApplication::getStatusOptions() as $key => $label)
                                    <option value="{{ $key }}" {{ $jobApplication->status === $key ? 'selected' : '' }}>{{ $label }}</option>
                                @endforeach
                            </select>
                        </div>

                        <div>
                            <label for="admin_notes" class="block text-sm font-medium text-gray-700 mb-1">Admin Notes</label>
                            <textarea name="admin_notes" id="admin_notes" rows="4"
                                      placeholder="Add notes about this application..."
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">{{ old('admin_notes', $jobApplication->admin_notes) }}</textarea>
                        </div>

                        <button type="submit" 
                                class="w-full bg-primary-600 text-white py-2 rounded-md hover:bg-primary-700 transition-colors">
                            Update Application
                        </button>
                    </div>
                </form>
            </div>

            <!-- Job Information -->
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Job Information</h3>
                <div class="space-y-3">
                    <div>
                        <span class="text-sm font-medium text-gray-500">Position</span>
                        <p class="text-gray-900">{{ $jobApplication->job->title }}</p>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-500">Department</span>
                        <p class="text-gray-900">{{ $jobApplication->job->department }}</p>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-500">Location</span>
                        <p class="text-gray-900">{{ $jobApplication->job->location }}</p>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-500">Employment Type</span>
                        <p class="text-gray-900">{{ ucfirst(str_replace('-', ' ', $jobApplication->job->employment_type)) }}</p>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-500">Salary Range</span>
                        <p class="text-gray-900">{{ $jobApplication->job->formatted_salary }}</p>
                    </div>
                </div>
                
                <div class="mt-4">
                    <a href="{{ route('admin.jobs.show', $jobApplication->job) }}" 
                       class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                        </svg>
                        View Job Details
                    </a>
                </div>
            </div>

            <!-- Application Metadata -->
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Application Details</h3>
                <div class="space-y-3">
                    <div>
                        <span class="text-sm font-medium text-gray-500">Reference Number</span>
                        <p class="text-gray-900 font-mono">{{ $jobApplication->reference_number }}</p>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-500">Submitted</span>
                        <p class="text-gray-900">{{ $jobApplication->created_at->format('M j, Y g:i A') }}</p>
                    </div>
                    @if($jobApplication->reviewed_at)
                    <div>
                        <span class="text-sm font-medium text-gray-500">Last Reviewed</span>
                        <p class="text-gray-900">{{ $jobApplication->reviewed_at->format('M j, Y g:i A') }}</p>
                        @if($jobApplication->reviewedBy)
                            <p class="text-xs text-gray-500">by {{ $jobApplication->reviewedBy->name }}</p>
                        @endif
                    </div>
                    @endif
                    @if($jobApplication->user)
                    <div>
                        <span class="text-sm font-medium text-gray-500">User Account</span>
                        <p class="text-gray-900">Registered User</p>
                    </div>
                    @else
                    <div>
                        <span class="text-sm font-medium text-gray-500">User Account</span>
                        <p class="text-gray-900">Guest Application</p>
                    </div>
                    @endif
                    @if($jobApplication->ip_address)
                    <div>
                        <span class="text-sm font-medium text-gray-500">IP Address</span>
                        <p class="text-gray-900 font-mono text-xs">{{ $jobApplication->ip_address }}</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                <div class="space-y-3">
                    <a href="mailto:{{ $jobApplication->email }}?subject=Re: Your Application for {{ $jobApplication->job->title }}" 
                       class="w-full inline-flex items-center justify-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        Send Email
                    </a>
                    
                    <a href="tel:{{ $jobApplication->phone }}" 
                       class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                        </svg>
                        Call Applicant
                    </a>

                    <form method="POST" action="{{ route('admin.job-applications.destroy', $jobApplication) }}" 
                          onsubmit="return confirm('Are you sure you want to delete this application? This action cannot be undone.')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" 
                                class="w-full inline-flex items-center justify-center px-4 py-2 border border-red-300 text-red-700 rounded-lg hover:bg-red-50 transition-colors">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            Delete Application
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
