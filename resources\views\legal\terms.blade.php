@extends('layouts.app')

@section('title', __('Terms of Service') . ' - ' . __('common.company_name'))

@section('content')
<div class="container mx-auto px-4 py-16">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">Terms of Service</h1>
        
        <div class="prose prose-lg max-w-none">
            <p class="text-gray-600 mb-6">Last updated: {{ date('F j, Y') }}</p>
            
            <h2>1. Acceptance of Terms</h2>
            <p>By accessing and using this website, you accept and agree to be bound by the terms and provision of this agreement.</p>
            
            <h2>2. Use License</h2>
            <p>Permission is granted to temporarily download one copy of the materials on {{ __('common.company_name') }}'s website for personal, non-commercial transitory viewing only.</p>
            
            <h2>3. Disclaimer</h2>
            <p>The materials on {{ __('common.company_name') }}'s website are provided on an 'as is' basis. {{ __('common.company_name') }} makes no warranties, expressed or implied, and hereby disclaims and negates all other warranties including without limitation, implied warranties or conditions of merchantability, fitness for a particular purpose, or non-infringement of intellectual property or other violation of rights.</p>
            
            <h2>4. Limitations</h2>
            <p>In no event shall {{ __('common.company_name') }} or its suppliers be liable for any damages (including, without limitation, damages for loss of data or profit, or due to business interruption) arising out of the use or inability to use the materials on {{ __('common.company_name') }}'s website, even if {{ __('common.company_name') }} or a {{ __('common.company_name') }} authorized representative has been notified orally or in writing of the possibility of such damage.</p>
            
            <h2>5. Contact Information</h2>
            <p>If you have any questions about these Terms of Service, please contact us.</p>
        </div>
        
        <div class="mt-12 pt-8 border-t border-gray-200">
            <a href="{{ route('register') }}" class="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Registration
            </a>
        </div>
    </div>
</div>
@endsection
