<?php

namespace App\Http\Controllers\Examples;

use App\Http\Controllers\Controller;
use App\Services\ImageService;
use App\Facades\ImageService as ImageServiceFacade;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

/**
 * Example controller demonstrating ImageService usage
 * This controller shows different ways to use the ImageService
 */
class ImageUploadExampleController extends Controller
{
    public function __construct(
        private ImageService $imageService
    ) {}
    
    /**
     * Example 1: Basic image upload using dependency injection
     */
    public function basicUpload(Request $request): JsonResponse
    {
        $request->validate([
            'image' => 'required|image|max:10240', // 10MB max
        ]);
        
        $result = $this->imageService->processUploadedImage(
            $request->file('image'),
            [
                'subdirectory' => 'examples/basic',
                'create_variants' => true,
                'create_webp' => true
            ]
        );
        
        if ($result['success']) {
            return response()->json([
                'success' => true,
                'message' => 'Image uploaded and processed successfully',
                'data' => [
                    'original_path' => $result['original_path'],
                    'webp_path' => $result['webp_path'],
                    'variants' => $result['variants'],
                    'public_url' => $this->imageService->getImageUrl($result['original_path']),
                    'processing_time' => $result['processing_time_ms'] . 'ms'
                ]
            ]);
        }
        
        return response()->json([
            'success' => false,
            'message' => 'Image upload failed',
            'errors' => $result['errors']
        ], 422);
    }
    
    /**
     * Example 2: Quick upload using facade
     */
    public function quickUpload(Request $request): JsonResponse
    {
        $request->validate([
            'image' => 'required|image|max:5120', // 5MB max
        ]);
        
        $result = ImageServiceFacade::quickUpload(
            $request->file('image'),
            'examples/quick'
        );
        
        if ($result['success']) {
            return response()->json([
                'success' => true,
                'message' => 'Quick upload completed',
                'data' => [
                    'path' => $result['original_path'],
                    'url' => ImageServiceFacade::getImageUrl($result['original_path'])
                ]
            ]);
        }
        
        return response()->json([
            'success' => false,
            'errors' => $result['errors']
        ], 422);
    }
    
    /**
     * Example 3: Full upload with all variants using helper function
     */
    public function fullUpload(Request $request): JsonResponse
    {
        $request->validate([
            'image' => 'required|image|max:10240',
        ]);
        
        // Using helper function
        $result = full_image_upload($request->file('image'), 'examples/full');
        
        if ($result['success']) {
            return response()->json([
                'success' => true,
                'message' => 'Full upload with variants completed',
                'data' => [
                    'original' => [
                        'path' => $result['original_path'],
                        'url' => get_image_url($result['original_path'])
                    ],
                    'webp' => [
                        'path' => $result['webp_path'],
                        'url' => $result['webp_path'] ? get_image_url($result['webp_path']) : null
                    ],
                    'variants' => array_map(function($path) {
                        return [
                            'path' => $path,
                            'url' => get_image_url($path)
                        ];
                    }, $result['variants']),
                    'file_info' => $result['file_info']
                ]
            ]);
        }
        
        return response()->json([
            'success' => false,
            'errors' => $result['errors']
        ], 422);
    }
    
    /**
     * Example 4: Multiple image upload
     */
    public function multipleUpload(Request $request): JsonResponse
    {
        $request->validate([
            'images' => 'required|array|max:5',
            'images.*' => 'required|image|max:10240',
        ]);
        
        $uploadedImages = [];
        $errors = [];
        
        foreach ($request->file('images') as $index => $image) {
            $result = quick_image_upload($image, 'examples/multiple');
            
            if ($result['success']) {
                $uploadedImages[] = [
                    'index' => $index,
                    'original_name' => $image->getClientOriginalName(),
                    'path' => $result['original_path'],
                    'url' => get_image_url($result['original_path']),
                    'webp_path' => $result['webp_path'],
                    'webp_url' => $result['webp_path'] ? get_image_url($result['webp_path']) : null
                ];
            } else {
                $errors[] = [
                    'index' => $index,
                    'original_name' => $image->getClientOriginalName(),
                    'errors' => $result['errors']
                ];
            }
        }
        
        return response()->json([
            'success' => empty($errors),
            'message' => count($uploadedImages) . ' images uploaded successfully',
            'uploaded_images' => $uploadedImages,
            'failed_uploads' => $errors
        ]);
    }
    
    /**
     * Example 5: Custom processing with specific options
     */
    public function customUpload(Request $request): JsonResponse
    {
        $request->validate([
            'image' => 'required|image|max:10240',
            'quality' => 'sometimes|integer|min:1|max:100',
            'create_webp' => 'sometimes|boolean',
            'create_variants' => 'sometimes|boolean',
        ]);
        
        $options = [
            'subdirectory' => 'examples/custom',
            'quality' => $request->input('quality', 85),
            'create_webp' => $request->boolean('create_webp', true),
            'create_variants' => $request->boolean('create_variants', true),
        ];
        
        // Add custom size variants if requested
        if ($options['create_variants']) {
            $options['sizes'] = [
                'small' => ['width' => 200, 'height' => 200, 'crop' => true],
                'medium' => ['width' => 500, 'height' => 500, 'crop' => false],
                'large' => ['width' => 1000, 'height' => 1000, 'crop' => false],
            ];
        }
        
        $result = $this->imageService->processUploadedImage(
            $request->file('image'),
            $options
        );
        
        if ($result['success']) {
            return response()->json([
                'success' => true,
                'message' => 'Custom image processing completed',
                'data' => $result,
                'options_used' => $options
            ]);
        }
        
        return response()->json([
            'success' => false,
            'errors' => $result['errors']
        ], 422);
    }
    
    /**
     * Example 6: Image validation only
     */
    public function validateOnly(Request $request): JsonResponse
    {
        $request->validate([
            'image' => 'required|file',
        ]);
        
        $validation = validate_image_file($request->file('image'));
        
        return response()->json([
            'valid' => $validation['valid'],
            'errors' => $validation['errors'],
            'file_info' => $validation['file_info']
        ]);
    }
    
    /**
     * Example 7: Get image information
     */
    public function getImageInfo(Request $request): JsonResponse
    {
        $request->validate([
            'path' => 'required|string',
        ]);
        
        $info = get_image_info($request->input('path'));
        
        return response()->json($info);
    }
    
    /**
     * Example 8: Delete image
     */
    public function deleteImage(Request $request): JsonResponse
    {
        $request->validate([
            'path' => 'required|string',
            'delete_variants' => 'sometimes|boolean',
        ]);
        
        $deleted = delete_image(
            $request->input('path'),
            $request->boolean('delete_variants', true)
        );
        
        return response()->json([
            'success' => $deleted,
            'message' => $deleted ? 'Image deleted successfully' : 'Failed to delete image'
        ]);
    }
}
