@props([
    'headers' => [],
    'rows' => [],
    'actions' => null,
    'searchable' => false,
    'sortable' => false,
    'pagination' => null
])

<div {{ $attributes->merge(['class' => 'bg-white rounded-xl shadow-soft border border-neutral-100 overflow-hidden']) }}>
    @if($searchable || isset($header))
        <div class="px-6 py-4 border-b border-neutral-200">
            @isset($header)
                {{ $header }}
            @else
                @if($searchable)
                    <div class="flex items-center justify-between">
                        <div class="relative">
                            <input type="text" 
                                   placeholder="Search..." 
                                   class="pl-10 pr-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                </svg>
                            </div>
                        </div>
                        @if($actions)
                            <div class="flex items-center space-x-2">
                                {{ $actions }}
                            </div>
                        @endif
                    </div>
                @endif
            @endisset
        </div>
    @endif

    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-neutral-200">
            @if(count($headers) > 0 || isset($thead))
                <thead class="bg-neutral-50">
                    <tr>
                        @isset($thead)
                            {{ $thead }}
                        @else
                            @foreach($headers as $header)
                                <th class="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                                    @if($sortable && is_array($header))
                                        <button type="button" class="group inline-flex items-center space-x-1 hover:text-neutral-700">
                                            <span>{{ $header['label'] }}</span>
                                            <svg class="h-4 w-4 text-neutral-400 group-hover:text-neutral-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
                                            </svg>
                                        </button>
                                    @else
                                        {{ is_array($header) ? $header['label'] : $header }}
                                    @endif
                                </th>
                            @endforeach
                        @endisset
                    </tr>
                </thead>
            @endif

            <tbody class="bg-white divide-y divide-neutral-200">
                @isset($tbody)
                    {{ $tbody }}
                @else
                    @forelse($rows as $row)
                        <tr class="hover:bg-neutral-50 transition-colors duration-200">
                            @foreach($row as $cell)
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $cell }}
                                </td>
                            @endforeach
                        </tr>
                    @empty
                        <tr>
                            <td colspan="{{ count($headers) }}" class="px-6 py-12 text-center">
                                <div class="flex flex-col items-center">
                                    <svg class="h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    <h3 class="text-lg font-medium text-gray-900 mb-2">No data found</h3>
                                    <p class="text-gray-500">There are no records to display.</p>
                                </div>
                            </td>
                        </tr>
                    @endforelse
                @endisset
            </tbody>
        </table>
    </div>

    @if($pagination || isset($footer))
        <div class="px-6 py-4 border-t border-neutral-200">
            @isset($footer)
                {{ $footer }}
            @else
                {{ $pagination }}
            @endisset
        </div>
    @endif
</div>
