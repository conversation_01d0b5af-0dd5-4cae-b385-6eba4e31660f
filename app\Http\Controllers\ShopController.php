<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\ProductCategory;
use Illuminate\Http\Request;
use Illuminate\View\View;

class ShopController extends Controller
{
    /**
     * Display the shop index page.
     */
    public function index(Request $request): View
    {
        $query = Product::active()->with(['categories']);
        
        // Search functionality
        if ($request->filled('search')) {
            $query->search($request->search);
        }
        
        // Category filter
        if ($request->filled('category')) {
            $category = ProductCategory::active()->where('slug', $request->category)->first();
            if ($category) {
                $categoryIds = $category->getAllCategoryIds();
                $query->whereHas('categories', function ($q) use ($categoryIds) {
                    $q->whereIn('product_categories.id', $categoryIds);
                });
            }
        }
        
        // Price filter
        if ($request->filled('min_price')) {
            $query->where('price', '>=', $request->min_price);
        }
        
        if ($request->filled('max_price')) {
            $query->where('price', '<=', $request->max_price);
        }
        
        // Sorting
        $sortBy = $request->get('sort', 'name');
        $sortOrder = $request->get('order', 'asc');
        
        switch ($sortBy) {
            case 'price':
                $query->orderBy('price', $sortOrder);
                break;
            case 'created_at':
                $query->orderBy('created_at', $sortOrder);
                break;
            case 'featured':
                $query->orderBy('is_featured', 'desc')->orderBy('name', 'asc');
                break;
            default:
                $query->orderBy('name', $sortOrder);
                break;
        }
        
        $products = $query->paginate(12)->withQueryString();
        
        // Get categories for filter sidebar
        $categories = ProductCategory::active()
            ->root()
            ->with(['children' => function ($query) {
                $query->active()->ordered();
            }])
            ->ordered()
            ->get();
        
        // Get price range for filter
        $priceRange = Product::active()->selectRaw('MIN(price) as min_price, MAX(price) as max_price')->first();
        
        return view('pages.shop.index', compact('products', 'categories', 'priceRange'));
    }
    
    /**
     * Display products in a specific category.
     */
    public function category(Request $request, ProductCategory $category): View
    {
        if (!$category->is_active) {
            abort(404);
        }
        
        $categoryIds = $category->getAllCategoryIds();
        
        $query = Product::active()
            ->with(['categories'])
            ->whereHas('categories', function ($q) use ($categoryIds) {
                $q->whereIn('product_categories.id', $categoryIds);
            });
        
        // Search functionality
        if ($request->filled('search')) {
            $query->search($request->search);
        }
        
        // Price filter
        if ($request->filled('min_price')) {
            $query->where('price', '>=', $request->min_price);
        }
        
        if ($request->filled('max_price')) {
            $query->where('price', '<=', $request->max_price);
        }
        
        // Sorting
        $sortBy = $request->get('sort', 'name');
        $sortOrder = $request->get('order', 'asc');
        
        switch ($sortBy) {
            case 'price':
                $query->orderBy('price', $sortOrder);
                break;
            case 'created_at':
                $query->orderBy('created_at', $sortOrder);
                break;
            case 'featured':
                $query->orderBy('is_featured', 'desc')->orderBy('name', 'asc');
                break;
            default:
                $query->orderBy('name', $sortOrder);
                break;
        }
        
        $products = $query->paginate(12)->withQueryString();
        
        // Get subcategories
        $subcategories = $category->children()->active()->ordered()->get();
        
        // Get price range for filter
        $priceRange = Product::active()
            ->whereHas('categories', function ($q) use ($categoryIds) {
                $q->whereIn('product_categories.id', $categoryIds);
            })
            ->selectRaw('MIN(price) as min_price, MAX(price) as max_price')
            ->first();
        
        return view('pages.shop.category', compact('category', 'products', 'subcategories', 'priceRange'));
    }
    
    /**
     * Display a specific product.
     */
    public function product(Product $product): View
    {
        if (!$product->is_active) {
            abort(404);
        }
        
        $product->load(['categories', 'variants' => function ($query) {
            $query->active();
        }]);
        
        // Get related products from same categories
        $relatedProducts = Product::active()
            ->where('id', '!=', $product->id)
            ->whereHas('categories', function ($query) use ($product) {
                $query->whereIn('product_categories.id', $product->categories->pluck('id'));
            })
            ->inStock()
            ->limit(4)
            ->get();
        
        return view('pages.shop.product', compact('product', 'relatedProducts'));
    }
    
    /**
     * Search products via AJAX.
     */
    public function search(Request $request)
    {
        $request->validate([
            'q' => 'required|string|min:2|max:100',
        ]);
        
        $products = Product::active()
            ->search($request->q)
            ->inStock()
            ->limit(10)
            ->get(['id', 'name', 'slug', 'price', 'featured_image']);
        
        return response()->json([
            'products' => $products->map(function ($product) {
                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'slug' => $product->slug,
                    'price' => $product->formatted_price,
                    'image' => $product->primary_image,
                    'url' => route('shop.product', $product->slug),
                ];
            }),
        ]);
    }
    
    /**
     * Get product quick view data.
     */
    public function quickView(Product $product)
    {
        if (!$product->is_active) {
            abort(404);
        }
        
        $product->load(['variants' => function ($query) {
            $query->active();
        }]);
        
        return response()->json([
            'product' => [
                'id' => $product->id,
                'name' => $product->name,
                'slug' => $product->slug,
                'short_description' => $product->short_description,
                'price' => $product->formatted_price,
                'compare_price' => $product->formatted_compare_price,
                'discount_percentage' => $product->discount_percentage,
                'images' => $product->all_images,
                'stock_status' => $product->stock_status,
                'is_in_stock' => $product->isInStock(),
                'variants' => $product->variants->map(function ($variant) {
                    return [
                        'id' => $variant->id,
                        'name' => $variant->name,
                        'price' => $variant->formatted_price,
                        'stock_status' => $variant->stock_status,
                        'is_in_stock' => $variant->isInStock(),
                        'attributes' => $variant->formatted_attributes,
                    ];
                }),
                'url' => route('shop.product', $product->slug),
            ],
        ]);
    }
}
