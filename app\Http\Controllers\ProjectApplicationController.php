<?php

namespace App\Http\Controllers;

use App\Models\ProjectApplication;
use App\Models\Service;
use App\Services\ActivityLogger;
use App\Services\EmailNotificationService;
use App\Services\FileService;
use App\Services\ImageService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;

class ProjectApplicationController extends Controller
{
    protected ActivityLogger $activityLogger;

    /**
     * Create a new controller instance.
     */
    public function __construct(ActivityLogger $activityLogger)
    {
        $this->activityLogger = $activityLogger;
        // Only require auth for index, show, edit, update, destroy
        // create and store should be accessible to public
        $this->middleware('auth')->except(['create', 'store', 'success', 'status']);
    }

    /**
     * Display a listing of the user's project applications.
     */
    public function index(): View
    {
        $applications = Auth::user()->projectApplications()
            ->with(['service', 'reviewedBy', 'project'])
            ->latest()
            ->paginate(10);

        return view('project-applications.index', compact('applications'));
    }

    /**
     * Show the form for creating a new project application.
     */
    public function create(): View
    {
        $services = Service::active()->ordered()->get();
        $user = Auth::user();

        // Pre-fill contact information for authenticated users
        $contactInfo = [];
        if ($user) {
            $contactInfo = [
                'full_name' => $user->name,
                'email' => $user->email,
                'phone' => $user->phone ?? '',
                'company' => $user->company ?? '',
                'position' => $user->position ?? '',
                'country' => $user->country ?? 'South Africa',
            ];
        }

        return view('project-applications.create', compact('services', 'user', 'contactInfo'));
    }

    /**
     * Store a newly created project application.
     */
    public function store(Request $request)
    {
        $isAuthenticated = Auth::check();

        // Define validation rules based on authentication status
        $rules = [
            // Project Information
            'service_id' => 'nullable|exists:services,id',
            'project_type' => 'nullable|string|max:255',
            'project_category' => 'nullable|string|max:255',
            'target_audience' => 'nullable|string|max:1000',
            'project_timeline' => 'nullable|string|max:255',
            'estimated_budget' => 'nullable|string|max:255',
            'title' => 'required|string|max:255',
            'description' => 'required|string|max:10000',
            'requirements' => 'nullable|string|max:10000',
            'budget_range' => 'nullable|string|max:100',
            'timeline' => 'nullable|string|max:100',
            'priority' => 'required|in:low,medium,high,urgent',

            // Feature Requirements (checkboxes)
            'user_authentication' => 'nullable|in:0,1',
            'admin_panel' => 'nullable|in:0,1',
            'payment_processing' => 'nullable|in:0,1',
            'multi_language' => 'nullable|in:0,1',
            'mobile_responsive' => 'nullable|in:0,1',
            'api_development' => 'nullable|in:0,1',
            'third_party_integrations' => 'nullable|in:0,1',
            'data_analytics' => 'nullable|in:0,1',

            // Technical Preferences
            'preferred_technologies' => 'nullable|string|max:1000',
            'hosting_preference' => 'nullable|string|max:255',
            'existing_system_integration' => 'nullable|in:0,1',
            'existing_systems_details' => 'nullable|string|max:1000',
            'performance_requirements' => 'nullable|string|max:1000',
            'security_level' => 'nullable|in:basic,standard,high,enterprise',
            'maintenance_support' => 'nullable|in:0,1',
            'training_required' => 'nullable|in:0,1',

            // Additional Information
            'additional_notes' => 'nullable|string|max:5000',
            'newsletter_signup' => 'nullable|in:0,1',
            'terms_accepted' => 'required|in:1',

            // File attachments - Enhanced security validation
            'attachments' => 'nullable|array|max:15',
            'attachments.*' => 'file|max:25600|mimes:pdf,doc,docx,xls,xlsx,ppt,pptx,jpg,jpeg,png,txt,zip', // 25MB max per file, specific types only
        ];

        // Add contact information validation for public applications
        if (!$isAuthenticated) {
            $rules = array_merge($rules, [
                'full_name' => 'required|string|max:255',
                'email' => 'required|email|max:255',
                'phone' => 'nullable|string|max:20',
                'company' => 'nullable|string|max:255',
                'position' => 'nullable|string|max:255',
                'country' => 'nullable|string|max:255',
                'preferred_contact_method' => 'required|in:email,phone,whatsapp',
            ]);
        }

        $validated = $request->validate($rules);

        // Convert checkbox values to boolean
        $validated = $this->convertCheckboxValues($validated);

        // Process file attachments if any
        $attachmentData = [];
        $fileUploadResults = [];
        if ($request->hasFile('attachments')) {
            $userId = $isAuthenticated ? Auth::id() : 'public';

            foreach ($request->file('attachments') as $file) {
                try {
                    // Use FileService for documents and ImageService for images
                    $mimeType = $file->getMimeType();

                    if (str_starts_with($mimeType, 'image/')) {
                        // Process image files with ImageService
                        $result = app(ImageService::class)->processUploadedFile($file, [
                            'folder' => 'project-applications/' . $userId . '/' . date('Y/m'),
                            'optimize' => true,
                            'convert_to_webp' => true,
                            'scan_for_viruses' => true,
                        ]);
                    } else {
                        // Process document files with FileService
                        $result = app(FileService::class)->processUploadedFile($file, [
                            'folder' => 'project-applications/' . $userId . '/' . date('Y/m'),
                            'scan_for_viruses' => true,
                            'scan_content' => true,
                        ]);
                    }

                    if ($result['success']) {
                        $attachmentData[] = [
                            'original_name' => $file->getClientOriginalName(),
                            'stored_name' => $result['filename'],
                            'path' => $result['path'],
                            'size' => $file->getSize(),
                            'mime_type' => $mimeType,
                            'scan_results' => $result['scan_results'] ?? null,
                        ];

                        $fileUploadResults[] = [
                            'original_name' => $file->getClientOriginalName(),
                            'size' => $file->getSize(),
                            'mime_type' => $mimeType,
                            'virus_scan_passed' => $result['virus_scan_passed'] ?? true,
                            'scan_engine' => $result['scan_engine'] ?? 'unknown',
                            'success' => true,
                        ];
                    } else {
                        $fileUploadResults[] = [
                            'original_name' => $file->getClientOriginalName(),
                            'size' => $file->getSize(),
                            'mime_type' => $mimeType,
                            'virus_scan_passed' => false,
                            'scan_engine' => 'unknown',
                            'success' => false,
                            'error' => $result['message'] ?? 'Unknown error',
                        ];

                        Log::warning('File upload failed for project application', [
                            'file' => $file->getClientOriginalName(),
                            'error' => $result['message'],
                            'user_id' => $userId
                        ]);
                    }
                } catch (\Exception $e) {
                    $fileUploadResults[] = [
                        'original_name' => $file->getClientOriginalName(),
                        'size' => $file->getSize(),
                        'mime_type' => $file->getMimeType(),
                        'virus_scan_passed' => false,
                        'scan_engine' => 'unknown',
                        'success' => false,
                        'error' => $e->getMessage(),
                    ];

                    Log::error('Error processing project application attachment', [
                        'file' => $file->getClientOriginalName(),
                        'error' => $e->getMessage(),
                        'user_id' => $userId
                    ]);
                }
            }
        }

        // Add user data and tracking information
        if ($isAuthenticated) {
            $validated['user_id'] = Auth::id();
        } else {
            $validated['user_id'] = null;
        }

        $validated['attachments'] = $attachmentData;
        $validated['is_completed'] = true;
        $validated['submitted_at'] = now();
        $validated['ip_address'] = $request->ip();
        $validated['user_agent'] = $request->userAgent();
        $validated['referrer'] = $request->header('referer');

        // UTM tracking
        $validated['utm_source'] = $request->get('utm_source');
        $validated['utm_medium'] = $request->get('utm_medium');
        $validated['utm_campaign'] = $request->get('utm_campaign');

        // Create project application
        $application = ProjectApplication::create($validated);

        // Send email notifications
        $emailService = new EmailNotificationService();
        $emailService->sendProjectApplicationReceived($application);

        // Log successful project application
        $this->activityLogger->logProjectApplicationActivity(
            'create',
            $application->id,
            $validated,
            true,
            null
        );

        $message = 'Your project application has been submitted successfully! We will review it and get back to you soon.';
        if (!empty($attachmentData)) {
            $fileCount = count($attachmentData);
            $message .= " Your {$fileCount} file(s) have been securely uploaded and scanned.";
        }

        // Return appropriate response based on authentication status
        if ($isAuthenticated) {
            return redirect()->route('project-applications.index')->with('success', $message);
        } else {
            // For public applications, redirect to success page with reference number
            return redirect()->route('project-applications.success', $application->reference_number)
                ->with('success', $message);
        }
    }

    /**
     * Display the specified project application.
     */
    public function show(ProjectApplication $projectApplication): View
    {
        // Ensure user can only view their own applications
        if ($projectApplication->user_id !== Auth::id()) {
            abort(403);
        }

        $projectApplication->load(['service', 'reviewedBy', 'project']);

        return view('project-applications.show', compact('projectApplication'));
    }

    /**
     * Show the form for editing the specified project application.
     */
    public function edit(ProjectApplication $projectApplication): View
    {
        // Ensure user can only edit their own applications and only if pending
        if ($projectApplication->user_id !== Auth::id() || $projectApplication->status !== 'pending') {
            abort(403);
        }

        $services = Service::active()->ordered()->get();

        return view('project-applications.edit', compact('projectApplication', 'services'));
    }

    /**
     * Update the specified project application.
     */
    public function update(Request $request, ProjectApplication $projectApplication)
    {
        // Ensure user can only update their own applications and only if pending
        if ($projectApplication->user_id !== Auth::id() || $projectApplication->status !== 'pending') {
            abort(403);
        }

        $validated = $request->validate([
            'service_id' => 'nullable|exists:services,id',
            'title' => 'required|string|max:255',
            'description' => 'required|string|max:10000',
            'requirements' => 'nullable|string|max:10000',
            'budget_range' => 'nullable|string|max:100',
            'timeline' => 'nullable|string|max:100',
            'priority' => 'required|in:low,medium,high,urgent',
            'attachments' => 'nullable|array|max:15',
            'attachments.*' => 'file|max:25600|mimes:pdf,doc,docx,xls,xlsx,ppt,pptx,jpg,jpeg,png,txt,zip', // 25MB max per file, specific types only
        ]);

        // Process new file attachments if any
        $existingAttachments = $projectApplication->attachments ?? [];
        $newAttachmentData = [];
        $fileUploadResults = [];
        
        if ($request->hasFile('attachments')) {
            foreach ($request->file('attachments') as $file) {
                try {
                    // Use FileService for documents and ImageService for images
                    $mimeType = $file->getMimeType();
                    
                    if (str_starts_with($mimeType, 'image/')) {
                        // Process image files with ImageService
                        $result = app(ImageService::class)->processUploadedFile($file, [
                            'folder' => 'project-applications/' . Auth::id() . '/' . date('Y/m'),
                            'optimize' => true,
                            'convert_to_webp' => true,
                            'scan_for_viruses' => true,
                        ]);
                    } else {
                        // Process document files with FileService
                        $result = app(FileService::class)->processUploadedFile($file, [
                            'folder' => 'project-applications/' . Auth::id() . '/' . date('Y/m'),
                            'scan_for_viruses' => true,
                            'scan_content' => true,
                        ]);
                    }

                    if ($result['success']) {
                        $newAttachmentData[] = [
                            'original_name' => $file->getClientOriginalName(),
                            'stored_name' => $result['filename'],
                            'path' => $result['path'],
                            'size' => $file->getSize(),
                            'mime_type' => $mimeType,
                            'scan_results' => $result['scan_results'] ?? null,
                        ];

                        $fileUploadResults[] = [
                            'original_name' => $file->getClientOriginalName(),
                            'size' => $file->getSize(),
                            'mime_type' => $mimeType,
                            'virus_scan_passed' => $result['virus_scan_passed'] ?? true,
                            'scan_engine' => $result['scan_engine'] ?? 'unknown',
                            'success' => true,
                        ];
                    } else {
                        $fileUploadResults[] = [
                            'original_name' => $file->getClientOriginalName(),
                            'size' => $file->getSize(),
                            'mime_type' => $mimeType,
                            'virus_scan_passed' => false,
                            'scan_engine' => 'unknown',
                            'success' => false,
                            'error' => $result['message'] ?? 'Unknown error',
                        ];
                    }
                } catch (\Exception $e) {
                    $fileUploadResults[] = [
                        'original_name' => $file->getClientOriginalName(),
                        'size' => $file->getSize(),
                        'mime_type' => $file->getMimeType(),
                        'virus_scan_passed' => false,
                        'scan_engine' => 'unknown',
                        'success' => false,
                        'error' => $e->getMessage(),
                    ];

                    Log::error('Error processing project application attachment update', [
                        'file' => $file->getClientOriginalName(),
                        'error' => $e->getMessage(),
                        'user_id' => Auth::id()
                    ]);
                }
            }
        }

        // Merge existing and new attachments
        $validated['attachments'] = array_merge($existingAttachments, $newAttachmentData);

        // Update project application
        $projectApplication->update($validated);

        // Log project application update
        $this->activityLogger->logProjectApplicationActivity(
            'update',
            $projectApplication->id,
            $validated,
            true,
            null
        );

        $message = 'Your project application has been updated successfully!';
        if (!empty($newAttachmentData)) {
            $fileCount = count($newAttachmentData);
            $message .= " {$fileCount} new file(s) have been securely uploaded and scanned.";
        }

        return redirect()->route('project-applications.show', $projectApplication)->with('success', $message);
    }

    /**
     * Remove the specified project application.
     */
    public function destroy(ProjectApplication $projectApplication)
    {
        // Ensure user can only delete their own applications and only if pending
        if ($projectApplication->user_id !== Auth::id() || $projectApplication->status !== 'pending') {
            abort(403);
        }

        $projectApplication->delete();

        return redirect()->route('project-applications.index')->with('success', 'Project application deleted successfully.');
    }

    /**
     * Show success page for public applications.
     */
    public function success(string $referenceNumber): View
    {
        $application = ProjectApplication::where('reference_number', $referenceNumber)->firstOrFail();

        return view('project-applications.success', compact('application'));
    }

    /**
     * Check status of public application.
     */
    public function status(Request $request)
    {
        $request->validate([
            'reference_number' => 'required|string',
        ]);

        $application = ProjectApplication::where('reference_number', $request->reference_number)->first();

        // Log application status check
        $this->activityLogger->logApplicationStatusCheck(
            $request->reference_number,
            $application !== null,
            'project'
        );

        if (!$application) {
            return response()->json([
                'success' => false,
                'message' => 'Application not found with the provided reference number.',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'application' => [
                'reference_number' => $application->reference_number,
                'title' => $application->title,
                'status' => $application->status,
                'submitted_at' => $application->submitted_at?->format('M d, Y'),
                'reviewed_at' => $application->reviewed_at?->format('M d, Y'),
            ],
        ]);
    }

    /**
     * Convert checkbox values from strings to booleans.
     */
    private function convertCheckboxValues(array $data): array
    {
        $checkboxFields = [
            'user_authentication', 'admin_panel', 'payment_processing', 'multi_language',
            'mobile_responsive', 'api_development', 'third_party_integrations', 'data_analytics',
            'existing_system_integration', 'maintenance_support', 'training_required', 'newsletter_signup'
        ];

        foreach ($checkboxFields as $field) {
            if (isset($data[$field])) {
                $data[$field] = $data[$field] === '1' || $data[$field] === 1;
            } else {
                $data[$field] = false;
            }
        }

        return $data;
    }
}
