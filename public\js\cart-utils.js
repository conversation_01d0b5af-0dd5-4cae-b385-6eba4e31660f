/**
 * Global Cart Utilities
 * Handles cart count updates and state management across all pages
 */

// Global cart count update function
function updateCartCount(count) {
    console.log('🔄 Updating global cart count to:', count);
    
    // Update all cart count elements
    const cartCountElements = document.querySelectorAll('.cart-count');
    cartCountElements.forEach(element => {
        element.textContent = count;
        
        // Show/hide badge based on count
        if (count > 0) {
            element.style.display = '';
            element.classList.remove('hidden');
            
            // Add animation for visual feedback
            element.classList.add('animate-pulse');
            setTimeout(() => {
                element.classList.remove('animate-pulse');
            }, 1000);
        } else {
            element.style.display = 'none';
            element.classList.add('hidden');
        }
    });
    
    // Update global cart count variable
    window.globalCartCount = count;
    
    console.log('✅ Global cart count updated successfully');
}

// Fetch current cart count from server
async function fetchCartCount() {
    try {
        const response = await fetch('/cart/count', {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            updateCartCount(data.count);
            return data.count;
        }
    } catch (error) {
        console.error('❌ Error fetching cart count:', error);
    }
    return 0;
}

// Initialize cart count on page load
document.addEventListener('DOMContentLoaded', function() {
    // Fetch and update cart count when page loads
    fetchCartCount();
});

// Auto-refresh cart count every 30 seconds (optional)
setInterval(fetchCartCount, 30000);

// Global notification function
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg text-white z-50 transition-all duration-300 transform translate-x-full`;
    
    // Set background color based on type
    switch(type) {
        case 'success':
            notification.classList.add('bg-green-500');
            break;
        case 'error':
            notification.classList.add('bg-red-500');
            break;
        case 'warning':
            notification.classList.add('bg-yellow-500');
            break;
        default:
            notification.classList.add('bg-blue-500');
    }
    
    notification.innerHTML = `
        <div class="flex items-center">
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-3 text-white hover:text-gray-200">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 300);
    }, 5000);
}

// Export functions for use in other scripts
window.updateCartCount = updateCartCount;
window.fetchCartCount = fetchCartCount;
window.showNotification = showNotification;
