<?php $__env->startSection('title', 'Project Application Details'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Project Application Details</h4>
                    <div class="d-flex gap-2">
                        <?php if($projectApplication->status === 'pending'): ?>
                            <a href="<?php echo e(route('project-applications.edit', $projectApplication)); ?>" class="btn btn-primary btn-sm">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                        <?php endif; ?>
                        <a href="<?php echo e(route('project-applications.index')); ?>" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h5><?php echo e($projectApplication->title); ?></h5>
                            <p class="text-muted mb-3">
                                <small>
                                    <i class="fas fa-calendar"></i> Submitted: <?php echo e($projectApplication->created_at->format('M d, Y \a\t g:i A')); ?>

                                    <?php if($projectApplication->reviewed_at): ?>
                                        | <i class="fas fa-check-circle"></i> Reviewed: <?php echo e($projectApplication->reviewed_at->format('M d, Y \a\t g:i A')); ?>

                                    <?php endif; ?>
                                </small>
                            </p>

                            <div class="mb-4">
                                <h6>Description</h6>
                                <p><?php echo e($projectApplication->description); ?></p>
                            </div>

                            <?php if($projectApplication->requirements): ?>
                                <div class="mb-4">
                                    <h6>Requirements</h6>
                                    <p><?php echo e($projectApplication->requirements); ?></p>
                                </div>
                            <?php endif; ?>

                            <?php if($projectApplication->attachments && count($projectApplication->attachments) > 0): ?>
                                <div class="mb-4">
                                    <h6>Attachments</h6>
                                    <div class="list-group">
                                        <?php $__currentLoopData = $projectApplication->attachments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attachment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                                <div>
                                                    <i class="fas fa-file"></i>
                                                    <strong><?php echo e($attachment['original_name']); ?></strong>
                                                    <small class="text-muted">(<?php echo e(number_format($attachment['size'] / 1024, 1)); ?> KB)</small>
                                                </div>
                                                <a href="<?php echo e(Storage::url($attachment['stored_name'])); ?>" 
                                                   class="btn btn-sm btn-outline-primary" 
                                                   target="_blank">
                                                    <i class="fas fa-download"></i> Download
                                                </a>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if($projectApplication->admin_notes): ?>
                                <div class="mb-4">
                                    <h6>Admin Notes</h6>
                                    <div class="alert alert-info">
                                        <?php echo e($projectApplication->admin_notes); ?>

                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Application Details</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <strong>Status:</strong>
                                        <span class="badge badge-<?php echo e($projectApplication->status_color); ?>">
                                            <?php echo e(ucfirst($projectApplication->status)); ?>

                                        </span>
                                    </div>

                                    <?php if($projectApplication->budget_range): ?>
                                        <div class="mb-3">
                                            <strong>Budget Range:</strong><br>
                                            $<?php echo e($projectApplication->budget_range); ?>

                                        </div>
                                    <?php endif; ?>

                                    <?php if($projectApplication->timeline): ?>
                                        <div class="mb-3">
                                            <strong>Timeline:</strong><br>
                                            <?php echo e($projectApplication->timeline); ?>

                                        </div>
                                    <?php endif; ?>

                                    <div class="mb-3">
                                        <strong>Priority:</strong><br>
                                        <span class="badge badge-<?php echo e($projectApplication->priority === 'urgent' ? 'danger' : ($projectApplication->priority === 'high' ? 'warning' : ($projectApplication->priority === 'medium' ? 'info' : 'secondary'))); ?>">
                                            <?php echo e(ucfirst($projectApplication->priority)); ?>

                                        </span>
                                    </div>

                                    <?php if($projectApplication->service): ?>
                                        <div class="mb-3">
                                            <strong>Service:</strong><br>
                                            <?php echo e($projectApplication->service->name); ?>

                                        </div>
                                    <?php endif; ?>

                                    <?php if($projectApplication->project): ?>
                                        <div class="mb-3">
                                            <strong>Related Project:</strong><br>
                                            <?php echo e($projectApplication->project->name); ?>

                                        </div>
                                    <?php endif; ?>

                                    <?php if($projectApplication->reviewedBy): ?>
                                        <div class="mb-3">
                                            <strong>Reviewed By:</strong><br>
                                            <?php echo e($projectApplication->reviewedBy->name); ?>

                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <?php if($projectApplication->status === 'pending'): ?>
                                <div class="mt-3">
                                    <form action="<?php echo e(route('project-applications.destroy', $projectApplication)); ?>" 
                                          method="POST" 
                                          onsubmit="return confirm('Are you sure you want to delete this application?')">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit" class="btn btn-danger btn-sm w-100">
                                            <i class="fas fa-trash"></i> Delete Application
                                        </button>
                                    </form>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\files\chisolution\resources\views/project-applications/show.blade.php ENDPATH**/ ?>