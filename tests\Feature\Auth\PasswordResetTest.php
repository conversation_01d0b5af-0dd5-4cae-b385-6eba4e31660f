<?php

namespace Tests\Feature\Auth;

use App\Models\User;
use App\Notifications\ResetPasswordNotification;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Password;
use Tests\TestCase;

class PasswordResetTest extends TestCase
{
    use RefreshDatabase;

    protected $seed = true;

    /** @test */
    public function user_can_request_password_reset_link()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        $response = $this->postJson('/forgot-password', [
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => __('passwords.sent_secure'),
                ]);

        $this->assertDatabaseHas('password_reset_tokens', [
            'email' => '<EMAIL>',
        ]);
    }

    /** @test */
    public function password_reset_link_is_sent_via_notification()
    {
        Notification::fake();

        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        $this->postJson('/forgot-password', [
            'email' => '<EMAIL>',
        ]);

        Notification::assertSentTo($user, ResetPasswordNotification::class);
    }

    /** @test */
    public function password_reset_always_returns_success_for_security()
    {
        // Test with non-existent email - should still return success to prevent email enumeration
        $response = $this->postJson('/forgot-password', [
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => __('passwords.sent_secure'),
                ]);
    }

    /** @test */
    public function password_reset_is_rate_limited()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        // Make 6 requests (limit is 5)
        for ($i = 0; $i < 6; $i++) {
            $response = $this->postJson('/forgot-password', [
                'email' => '<EMAIL>',
            ]);
        }

        // With our secure implementation, rate limiting still works internally
        // but we always return success to prevent email enumeration
        // The rate limiting prevents actual emails from being sent
        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => __('passwords.sent_secure'),
                ]);
    }

    /** @test */
    public function user_can_reset_password_with_valid_token()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('old-password'),
        ]);

        $token = Password::createToken($user);

        $response = $this->postJson('/reset-password', [
            'token' => $token,
            'email' => '<EMAIL>',
            'password' => 'SecureP@ssw0rd2024!',
            'password_confirmation' => 'SecureP@ssw0rd2024!',
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => __('passwords.reset'),
                ]);

        $user->refresh();
        $this->assertTrue(Hash::check('SecureP@ssw0rd2024!', $user->password));
    }

    /** @test */
    public function password_reset_fails_with_invalid_token()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        $response = $this->postJson('/reset-password', [
            'token' => 'invalid-token',
            'email' => '<EMAIL>',
            'password' => 'SecureP@ssw0rd2024!',
            'password_confirmation' => 'SecureP@ssw0rd2024!',
        ]);

        $response->assertStatus(422)
                ->assertJson([
                    'success' => false,
                    'message' => __('passwords.token'),
                ]);
    }

    /** @test */
    public function password_reset_requires_password_confirmation()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        $token = Password::createToken($user);

        $response = $this->postJson('/reset-password', [
            'token' => $token,
            'email' => '<EMAIL>',
            'password' => 'SecureP@ssw0rd2024!',
            'password_confirmation' => 'DifferentP@ssw0rd2024!',
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['password']);
    }

    /** @test */
    public function password_reset_validates_password_strength()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        $token = Password::createToken($user);

        $response = $this->postJson('/reset-password', [
            'token' => $token,
            'email' => '<EMAIL>',
            'password' => 'weak',
            'password_confirmation' => 'weak',
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['password']);
    }

    /** @test */
    public function password_reset_clears_remember_tokens()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'remember_token' => 'old-remember-token',
        ]);

        $token = Password::createToken($user);

        $this->postJson('/reset-password', [
            'token' => $token,
            'email' => '<EMAIL>',
            'password' => 'SecureP@ssw0rd2024!',
            'password_confirmation' => 'SecureP@ssw0rd2024!',
        ]);

        $user->refresh();
        $this->assertNotEquals('old-remember-token', $user->remember_token);
    }

    /** @test */
    public function password_reset_logs_out_all_sessions()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        // Create a session for the user
        DB::table('sessions')->insert([
            'id' => 'test-session-id',
            'user_id' => $user->id,
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Test Agent',
            'payload' => 'test-payload',
            'last_activity' => time(),
        ]);

        $token = Password::createToken($user);

        $this->postJson('/reset-password', [
            'token' => $token,
            'email' => '<EMAIL>',
            'password' => 'SecureP@ssw0rd2024!',
            'password_confirmation' => 'SecureP@ssw0rd2024!',
        ]);

        $this->assertDatabaseMissing('sessions', [
            'user_id' => $user->id,
        ]);
    }

    /** @test */
    public function password_reset_form_displays_correctly()
    {
        $response = $this->get('/reset-password/test-token?email=<EMAIL>');

        $response->assertStatus(200)
                ->assertViewIs('auth.reset-password')
                ->assertViewHas('token', 'test-token')
                ->assertViewHas('email', '<EMAIL>');
    }

    /** @test */
    public function forgot_password_form_displays_correctly()
    {
        $response = $this->get('/forgot-password');

        $response->assertStatus(200)
                ->assertViewIs('auth.forgot-password');
    }

    /** @test */
    public function disposable_email_addresses_still_return_success_for_security()
    {
        // Even disposable emails should return success to prevent enumeration
        $response = $this->postJson('/forgot-password', [
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => __('passwords.sent_secure'),
                ]);
    }
}
