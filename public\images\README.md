# Images Directory

This directory contains static images used throughout the application.

## Structure

- `projects/` - Project-related images
  - `placeholder.jpg` - Default placeholder image for projects without featured images
- `services/` - Service-related images
- `users/` - User avatar placeholders
- `general/` - General application images

## Placeholder Images

Make sure to add appropriate placeholder images:

1. **Project Placeholder** (`projects/placeholder.jpg`)
   - Recommended size: 1200x800px
   - Format: JPG or PNG
   - Should be a neutral, professional image

2. **Service Placeholder** (`services/placeholder.jpg`)
   - Recommended size: 600x400px
   - Format: JPG or PNG

3. **User Avatar Placeholder** (`users/avatar-placeholder.jpg`)
   - Recommended size: 200x200px
   - Format: JPG or PNG
   - Should be a generic avatar icon

## Usage

These images are served directly by the web server and should be optimized for web delivery.
