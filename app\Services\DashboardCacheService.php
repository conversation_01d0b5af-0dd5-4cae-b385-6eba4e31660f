<?php

namespace App\Services;

use App\Models\User;
use App\Models\Order;
use App\Models\Project;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\ActivityLog;
use Illuminate\Support\Facades\Cache;

class DashboardCacheService
{
    // Cache TTL constants (in seconds)
    private const ADMIN_STATS_TTL = 300;      // 5 minutes
    private const RECENT_ORDERS_TTL = 120;    // 2 minutes
    private const LOW_STOCK_TTL = 600;        // 10 minutes
    private const ACTIVITIES_TTL = 60;        // 1 minute
    private const USER_DASHBOARD_TTL = 300;   // 5 minutes

    /**
     * Get cached admin dashboard stats.
     */
    public function getAdminStats(): array
    {
        return Cache::remember('admin.dashboard.stats', self::ADMIN_STATS_TTL, function () {
            return [
                'total_users' => User::where('is_deleted', false)->count(),
                'total_orders' => Order::count(),
                'total_products' => Product::where('is_deleted', false)->count(),
                'total_categories' => ProductCategory::where('is_deleted', false)->count(),
                'pending_orders' => Order::where('status', 'pending')->count(),
                'revenue_today' => Order::whereDate('created_at', today())
                                       ->where('payment_status', 'completed')
                                       ->sum('total_amount'),
                'revenue_month' => Order::whereMonth('created_at', now()->month)
                                       ->whereYear('created_at', now()->year)
                                       ->where('payment_status', 'completed')
                                       ->sum('total_amount'),
                'total_activity_logs' => ActivityLog::count(),
                'suspicious_activities' => ActivityLog::where('is_suspicious', true)->count(),
                'today_activities' => ActivityLog::whereDate('occurred_at', today())->count(),
                'failed_logins_today' => ActivityLog::where('activity_type', 'password_reset_request')
                                                   ->whereDate('occurred_at', today())
                                                   ->where('status', 'failed')
                                                   ->count(),
            ];
        });
    }

    /**
     * Get cached recent orders for admin dashboard.
     */
    public function getRecentOrders()
    {
        return Cache::remember('admin.dashboard.recent_orders', self::RECENT_ORDERS_TTL, function () {
            return Order::with(['user:id,first_name,last_name,email', 'items.product:id,name,price'])
                       ->select(['id', 'user_id', 'order_number', 'status', 'total_amount', 'created_at'])
                       ->latest()
                       ->limit(5)
                       ->get();
        });
    }

    /**
     * Get cached low stock products for admin dashboard.
     */
    public function getLowStockProducts()
    {
        return Cache::remember('admin.dashboard.low_stock', self::LOW_STOCK_TTL, function () {
            return Product::where('is_deleted', false)
                         ->where('track_inventory', true)
                         ->whereRaw('inventory_quantity <= low_stock_threshold')
                         ->select(['id', 'name', 'inventory_quantity', 'low_stock_threshold'])
                         ->limit(10)
                         ->get();
        });
    }

    /**
     * Get cached recent activities for admin dashboard.
     */
    public function getRecentActivities()
    {
        return Cache::remember('admin.dashboard.recent_activities', self::ACTIVITIES_TTL, function () {
            return ActivityLog::with('user:id,first_name,last_name,email')
                             ->select(['id', 'user_id', 'activity_type', 'activity_description', 'occurred_at'])
                             ->orderBy('occurred_at', 'desc')
                             ->limit(10)
                             ->get();
        });
    }

    /**
     * Get cached customer dashboard data.
     */
    public function getCustomerDashboardData(User $user): array
    {
        $cacheKey = "customer.dashboard.{$user->id}";
        
        return Cache::remember($cacheKey, self::USER_DASHBOARD_TTL, function () use ($user) {
            // Get customer orders with optimized eager loading
            $orders = Order::where('user_id', $user->id)
                          ->with(['items.product:id,name,price'])
                          ->select(['id', 'order_number', 'status', 'total_amount', 'created_at'])
                          ->orderBy('created_at', 'desc')
                          ->limit(5)
                          ->get();

            // Single query for all order statistics
            $orderStats = Order::where('user_id', $user->id)
                              ->selectRaw('
                                  COUNT(*) as total_orders,
                                  SUM(CASE WHEN status = "delivered" THEN total_amount ELSE 0 END) as total_spent,
                                  SUM(CASE WHEN status IN ("pending", "processing") THEN 1 ELSE 0 END) as pending_orders
                              ')
                              ->first();

            return [
                'recent_orders' => $orders,
                'quick_stats' => [
                    'total_orders' => $orderStats->total_orders ?? 0,
                    'total_spent' => $orderStats->total_spent ?? 0,
                    'pending_orders' => $orderStats->pending_orders ?? 0,
                    'account_status' => $user->is_active ? 'active' : 'inactive',
                ],
            ];
        });
    }

    /**
     * Get cached client dashboard data.
     */
    public function getClientDashboardData(User $user): array
    {
        $cacheKey = "client.dashboard.{$user->id}";
        
        return Cache::remember($cacheKey, self::USER_DASHBOARD_TTL, function () use ($user) {
            // Get client projects with minimal data
            $projects = Project::where('client_id', $user->id)
                              ->where('is_deleted', false)
                              ->select(['id', 'title', 'status', 'priority', 'total_amount', 'created_at', 'estimated_hours', 'actual_hours'])
                              ->orderBy('created_at', 'desc')
                              ->limit(5)
                              ->get();

            // Get client orders with optimized eager loading
            $orders = Order::where('user_id', $user->id)
                          ->where('is_deleted', false)
                          ->with(['items.product:id,name,price'])
                          ->select(['id', 'order_number', 'status', 'total_amount', 'created_at'])
                          ->orderBy('created_at', 'desc')
                          ->limit(3)
                          ->get();

            // Single query for project statistics
            $projectStats = Project::where('client_id', $user->id)
                                  ->where('is_deleted', false)
                                  ->selectRaw('
                                      COUNT(*) as total_projects,
                                      SUM(CASE WHEN status IN ("planning", "in_progress", "review") THEN 1 ELSE 0 END) as active_projects,
                                      SUM(total_amount) as total_project_value
                                  ')
                                  ->first();

            return [
                'recent_projects' => $projects,
                'recent_orders' => $orders,
                'quick_stats' => [
                    'total_projects' => $projectStats->total_projects ?? 0,
                    'active_projects' => $projectStats->active_projects ?? 0,
                    'total_project_value' => $projectStats->total_project_value ?? 0,
                    'account_status' => $user->is_active ? 'active' : 'inactive',
                ],
            ];
        });
    }

    /**
     * Invalidate user-specific dashboard cache.
     */
    public function invalidateUserDashboard(User $user): void
    {
        if ($user->isCustomer()) {
            Cache::forget("customer.dashboard.{$user->id}");
        } elseif ($user->isClient()) {
            Cache::forget("client.dashboard.{$user->id}");
        }
    }

    /**
     * Invalidate admin dashboard cache.
     */
    public function invalidateAdminDashboard(): void
    {
        Cache::forget('admin.dashboard.stats');
        Cache::forget('admin.dashboard.recent_orders');
        Cache::forget('admin.dashboard.low_stock');
        Cache::forget('admin.dashboard.recent_activities');
    }

    /**
     * Invalidate all dashboard caches.
     */
    public function invalidateAllDashboards(): void
    {
        // Use cache tags if available (Redis/Memcached)
        if (Cache::getStore() instanceof \Illuminate\Cache\RedisStore) {
            Cache::tags(['dashboard'])->flush();
        } else {
            // Fallback for file/database cache
            $this->invalidateAdminDashboard();
            
            // Clear user dashboard caches (this is expensive, use sparingly)
            $userIds = User::pluck('id');
            foreach ($userIds as $userId) {
                Cache::forget("customer.dashboard.{$userId}");
                Cache::forget("client.dashboard.{$userId}");
            }
        }
    }

    /**
     * Warm up dashboard caches.
     */
    public function warmUpCaches(): void
    {
        // Warm up admin dashboard
        $this->getAdminStats();
        $this->getRecentOrders();
        $this->getLowStockProducts();
        $this->getRecentActivities();
    }
}
