<?php $__env->startSection('title', 'Project Application Details'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
            <nav class="flex mb-4" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="<?php echo e(route('admin.project-applications.index')); ?>" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-primary-600">
                            Project Applications
                        </a>
                    </li>
                    <li aria-current="page">
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                            </svg>
                            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">Application #<?php echo e($projectApplication->id); ?></span>
                        </div>
                    </li>
                </ol>
            </nav>
            <h1 class="text-2xl font-bold text-gray-900"><?php echo e($projectApplication->title); ?></h1>
        </div>
        <div class="flex flex-wrap gap-2">
            <a href="<?php echo e(route('admin.project-applications.edit', $projectApplication)); ?>" class="inline-flex items-center px-4 py-2 bg-primary-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-primary-700 focus:bg-primary-700 active:bg-primary-900 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                </svg>
                Edit Application
            </a>
            <button type="button" onclick="showStatusModal(<?php echo e($projectApplication->id); ?>, '<?php echo e($projectApplication->status); ?>')" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"/>
                </svg>
                Change Status
            </button>
            <div class="relative inline-block text-left" x-data="{ open: false }">
                <button @click="open = !open" type="button" class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                    </svg>
                </button>
                <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                    <div class="py-1">
                        <a href="#" onclick="printApplication()" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"/>
                            </svg>
                            Print
                        </a>
                        <a href="#" onclick="exportApplication('pdf')" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                            </svg>
                            Export as PDF
                        </a>
                        <hr class="my-1 border-gray-200">
                        <a href="#" onclick="confirmDelete(<?php echo e($projectApplication->id); ?>, '<?php echo e(addslashes($projectApplication->title)); ?>')" class="flex items-center px-4 py-2 text-sm text-red-700 hover:bg-red-50">
                            <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                            </svg>
                            Delete Application
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Application Details Card -->
            <div class="bg-white shadow-soft rounded-xl border border-neutral-100">
                <div class="px-6 py-4 border-b border-neutral-100">
                    <h3 class="text-lg font-semibold text-primary-900">Application Details</h3>
                </div>
                <div class="p-6 space-y-6">
                    <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                        <div class="font-semibold text-gray-900">Title:</div>
                        <div class="sm:col-span-2 text-gray-700"><?php echo e($projectApplication->title); ?></div>
                    </div>

                    <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                        <div class="font-semibold text-gray-900">Description:</div>
                        <div class="sm:col-span-2">
                            <div class="border border-gray-200 rounded-lg p-4 bg-gray-50">
                                <div class="text-gray-700 whitespace-pre-line"><?php echo e($projectApplication->description); ?></div>
                            </div>
                        </div>
                    </div>

                    <?php if($projectApplication->requirements): ?>
                    <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                        <div class="font-semibold text-gray-900">Requirements:</div>
                        <div class="sm:col-span-2">
                            <div class="border border-gray-200 rounded-lg p-4 bg-gray-50">
                                <div class="text-gray-700 whitespace-pre-line"><?php echo e($projectApplication->requirements); ?></div>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                        <div class="font-semibold text-gray-900">Service:</div>
                        <div class="sm:col-span-2">
                            <?php if($projectApplication->service): ?>
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                    <?php echo e($projectApplication->service->name); ?>

                                </span>
                            <?php else: ?>
                                <span class="text-gray-500">No service specified</span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                        <div class="font-semibold text-gray-900">Budget Range:</div>
                        <div class="sm:col-span-2">
                            <?php if($projectApplication->budget_range): ?>
                                <span class="text-green-600 font-semibold">$<?php echo e($projectApplication->budget_range); ?></span>
                            <?php else: ?>
                                <span class="text-gray-500">Not specified</span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                        <div class="font-semibold text-gray-900">Timeline:</div>
                        <div class="sm:col-span-2">
                            <?php if($projectApplication->timeline): ?>
                                <span class="text-gray-700"><?php echo e($projectApplication->timeline); ?></span>
                            <?php else: ?>
                                <span class="text-gray-500">Not specified</span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                        <div class="font-semibold text-gray-900">Priority:</div>
                        <div class="sm:col-span-2">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                <?php if($projectApplication->priority === 'high'): ?> bg-red-100 text-red-800
                                <?php elseif($projectApplication->priority === 'medium'): ?> bg-yellow-100 text-yellow-800
                                <?php else: ?> bg-green-100 text-green-800 <?php endif; ?>">
                                <?php echo e($projectApplication->priority_label); ?>

                            </span>
                        </div>
                    </div>

                    <?php if($projectApplication->estimated_cost): ?>
                    <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                        <div class="font-semibold text-gray-900">Estimated Cost:</div>
                        <div class="sm:col-span-2">
                            <span class="text-green-600 font-semibold">$<?php echo e(number_format($projectApplication->estimated_cost, 2)); ?></span>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if($projectApplication->estimated_hours): ?>
                    <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                        <div class="font-semibold text-gray-900">Estimated Hours:</div>
                        <div class="sm:col-span-2">
                            <span class="font-semibold text-gray-700"><?php echo e($projectApplication->estimated_hours); ?> hours</span>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Admin Notes Card -->
            <?php if($projectApplication->admin_notes): ?>
            <div class="bg-white shadow-soft rounded-xl border border-neutral-100">
                <div class="px-6 py-4 border-b border-neutral-100">
                    <h3 class="text-lg font-semibold text-yellow-700">Admin Notes</h3>
                </div>
                <div class="p-6">
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <div class="text-yellow-800 whitespace-pre-line"><?php echo e($projectApplication->admin_notes); ?></div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Attachments Card -->
            <?php if($projectApplication->attachments && count($projectApplication->attachments) > 0): ?>
            <div class="bg-white shadow-soft rounded-xl border border-neutral-100">
                <div class="px-6 py-4 border-b border-neutral-100">
                    <h3 class="text-lg font-semibold text-green-700">Attachments (<?php echo e(count($projectApplication->attachments)); ?>)</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <?php $__currentLoopData = $projectApplication->attachments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $attachment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                            <div class="flex items-center space-x-3">
                                <div class="flex-shrink-0">
                                    <?php
                                        $extension = pathinfo($attachment['original_name'], PATHINFO_EXTENSION);
                                        $iconColor = match(strtolower($extension)) {
                                            'pdf' => 'text-red-500',
                                            'doc', 'docx' => 'text-blue-500',
                                            'xls', 'xlsx' => 'text-green-500',
                                            'jpg', 'jpeg', 'png', 'gif' => 'text-purple-500',
                                            'zip', 'rar' => 'text-yellow-500',
                                            default => 'text-gray-500'
                                        };
                                        $iconType = match(strtolower($extension)) {
                                            'pdf' => 'M12 14l9-5-9-5-9 5 9 5z M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z',
                                            'doc', 'docx' => 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z',
                                            'xls', 'xlsx' => 'M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z',
                                            'jpg', 'jpeg', 'png', 'gif' => 'M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z',
                                            default => 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
                                        };
                                    ?>
                                    <svg class="w-8 h-8 <?php echo e($iconColor); ?>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<?php echo e($iconType); ?>"/>
                                    </svg>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="font-semibold text-gray-900 truncate"><?php echo e($attachment['original_name']); ?></div>
                                    <div class="text-sm text-gray-500">
                                        <?php echo e(isset($attachment['file_size']) ? number_format($attachment['file_size'] / 1024, 1) . ' KB' : 'Unknown size'); ?>

                                    </div>
                                </div>
                                <div class="flex-shrink-0">
                                    <a href="<?php echo e(route('admin.project-applications.download', [$projectApplication, $index])); ?>"
                                       class="inline-flex items-center p-2 text-sm font-medium text-primary-600 bg-primary-50 rounded-lg hover:bg-primary-100 focus:ring-4 focus:ring-primary-300 focus:outline-none transition-colors"
                                       title="Download">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Status Card -->
            <div class="bg-white shadow-soft rounded-xl border border-neutral-100">
                <div class="px-6 py-4 border-b border-neutral-100">
                    <h3 class="text-lg font-semibold text-primary-900">Application Status</h3>
                </div>
                <div class="p-6 text-center">
                    <span class="inline-flex items-center px-4 py-2 rounded-full text-lg font-medium mb-4
                        <?php if($projectApplication->status === 'pending'): ?> bg-yellow-100 text-yellow-800
                        <?php elseif($projectApplication->status === 'under_review'): ?> bg-blue-100 text-blue-800
                        <?php elseif($projectApplication->status === 'approved'): ?> bg-green-100 text-green-800
                        <?php elseif($projectApplication->status === 'rejected'): ?> bg-red-100 text-red-800
                        <?php elseif($projectApplication->status === 'in_progress'): ?> bg-purple-100 text-purple-800
                        <?php elseif($projectApplication->status === 'completed'): ?> bg-green-100 text-green-800
                        <?php else: ?> bg-gray-100 text-gray-800 <?php endif; ?>"
                          id="current-status">
                        <?php echo e($projectApplication->status_label); ?>

                    </span>
                    <div class="mt-4">
                        <button type="button" onclick="showStatusModal(<?php echo e($projectApplication->id); ?>, '<?php echo e($projectApplication->status); ?>')"
                                class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"/>
                            </svg>
                            Change Status
                        </button>
                    </div>
                </div>
            </div>

            <!-- Customer Information Card -->
            <div class="bg-white shadow-soft rounded-xl border border-neutral-100">
                <div class="px-6 py-4 border-b border-neutral-100">
                    <h3 class="text-lg font-semibold text-primary-900">Customer Information</h3>
                </div>
                <div class="p-6">
                    <div class="text-center mb-6">
                        <?php if($projectApplication->user->avatar): ?>
                            <img src="<?php echo e(asset('storage/' . $projectApplication->user->avatar)); ?>"
                                 class="w-20 h-20 rounded-full mx-auto" alt="Avatar">
                        <?php else: ?>
                            <div class="w-20 h-20 rounded-full bg-primary-600 flex items-center justify-center mx-auto">
                                <span class="text-white font-bold text-xl">
                                    <?php echo e(strtoupper(substr($projectApplication->user->first_name, 0, 1))); ?><?php echo e(strtoupper(substr($projectApplication->user->last_name, 0, 1))); ?>

                                </span>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="text-center">
                        <h4 class="text-lg font-semibold text-gray-900 mb-2"><?php echo e($projectApplication->user->first_name); ?> <?php echo e($projectApplication->user->last_name); ?></h4>
                        <p class="text-gray-600 mb-2"><?php echo e($projectApplication->user->email); ?></p>
                        <?php if($projectApplication->user->phone): ?>
                            <p class="text-gray-600 mb-4"><?php echo e($projectApplication->user->phone); ?></p>
                        <?php endif; ?>
                        <div class="flex flex-wrap justify-center gap-2">
                            <a href="mailto:<?php echo e($projectApplication->user->email); ?>" class="inline-flex items-center px-3 py-2 border border-primary-300 rounded-md text-sm font-medium text-primary-700 bg-primary-50 hover:bg-primary-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                                </svg>
                                Email
                            </a>
                            <?php if($projectApplication->user->phone): ?>
                                <a href="tel:<?php echo e($projectApplication->user->phone); ?>" class="inline-flex items-center px-3 py-2 border border-green-300 rounded-md text-sm font-medium text-green-700 bg-green-50 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                                    </svg>
                                    Call
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Timeline Card -->
            <div class="bg-white shadow-soft rounded-xl border border-neutral-100">
                <div class="px-6 py-4 border-b border-neutral-100">
                    <h3 class="text-lg font-semibold text-primary-900">Timeline</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0">
                                <div class="w-3 h-3 bg-green-500 rounded-full mt-2"></div>
                            </div>
                            <div class="flex-1">
                                <h6 class="font-semibold text-gray-900">Application Submitted</h6>
                                <p class="text-sm text-gray-500"><?php echo e($projectApplication->created_at->format('M d, Y h:i A')); ?></p>
                            </div>
                        </div>

                        <?php if($projectApplication->reviewed_at): ?>
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0">
                                <div class="w-3 h-3 bg-blue-500 rounded-full mt-2"></div>
                            </div>
                            <div class="flex-1">
                                <h6 class="font-semibold text-gray-900">Last Reviewed</h6>
                                <p class="text-sm text-gray-500">
                                    <?php echo e($projectApplication->reviewed_at->format('M d, Y h:i A')); ?>

                                    <?php if($projectApplication->reviewedBy): ?>
                                        <br>by <?php echo e($projectApplication->reviewedBy->first_name); ?> <?php echo e($projectApplication->reviewedBy->last_name); ?>

                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                        <?php endif; ?>

                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0">
                                <div class="w-3 h-3 bg-gray-400 rounded-full mt-2"></div>
                            </div>
                            <div class="flex-1">
                                <h6 class="font-semibold text-gray-900">Last Updated</h6>
                                <p class="text-sm text-gray-500"><?php echo e($projectApplication->updated_at->format('M d, Y h:i A')); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions Card -->
            <div class="bg-white shadow-soft rounded-xl border border-neutral-100">
                <div class="px-6 py-4 border-b border-neutral-100">
                    <h3 class="text-lg font-semibold text-primary-900">Quick Actions</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-3">
                        <?php if($projectApplication->status === 'pending'): ?>
                            <button type="button" onclick="quickStatusChange('under_review')"
                                    class="w-full inline-flex items-center justify-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                </svg>
                                Start Review
                            </button>
                        <?php endif; ?>

                        <?php if($projectApplication->status === 'under_review'): ?>
                            <button type="button" onclick="quickStatusChange('approved')"
                                    class="w-full inline-flex items-center justify-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                </svg>
                                Approve
                            </button>
                            <button type="button" onclick="quickStatusChange('rejected')"
                                    class="w-full inline-flex items-center justify-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700 focus:bg-red-700 active:bg-red-900 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                </svg>
                                Reject
                            </button>
                        <?php endif; ?>

                        <?php if($projectApplication->status === 'approved'): ?>
                            <button type="button" onclick="quickStatusChange('in_progress')"
                                    class="w-full inline-flex items-center justify-center px-4 py-2 bg-primary-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-primary-700 focus:bg-primary-700 active:bg-primary-900 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H15M9 10v4a2 2 0 002 2h2a2 2 0 002-2v-4M9 10V9a2 2 0 012-2h2a2 2 0 012 2v1"/>
                                </svg>
                                Start Project
                            </button>
                        <?php endif; ?>

                        <?php if($projectApplication->status === 'in_progress'): ?>
                            <button type="button" onclick="quickStatusChange('completed')"
                                    class="w-full inline-flex items-center justify-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                Mark Complete
                            </button>
                        <?php endif; ?>

                        <a href="<?php echo e(route('admin.project-applications.edit', $projectApplication)); ?>"
                           class="w-full inline-flex items-center justify-center px-4 py-2 bg-white border border-primary-300 rounded-md font-semibold text-xs text-primary-700 uppercase tracking-widest hover:bg-primary-50 focus:bg-primary-50 active:bg-primary-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition ease-in-out duration-150">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                            </svg>
                            Edit Details
                        </a>

                        <button type="button" onclick="printApplication()"
                                class="w-full inline-flex items-center justify-center px-4 py-2 bg-white border border-blue-300 rounded-md font-semibold text-xs text-blue-700 uppercase tracking-widest hover:bg-blue-50 focus:bg-blue-50 active:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"/>
                            </svg>
                            Print Application
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Status Change Modal -->
<div id="statusModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50 flex items-center justify-center">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">Change Application Status</h3>
                <button type="button" onclick="closeStatusModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
        </div>
        <form id="statusForm">
            <?php echo csrf_field(); ?>
            <div class="px-6 py-4 space-y-4">
                <div>
                    <label for="new_status" class="block text-sm font-medium text-gray-700 mb-2">New Status</label>
                    <select name="status" id="new_status" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500" required>
                        <option value="pending">Pending Review</option>
                        <option value="under_review">Under Review</option>
                        <option value="approved">Approved</option>
                        <option value="rejected">Rejected</option>
                        <option value="in_progress">In Progress</option>
                        <option value="completed">Completed</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                </div>
                <div>
                    <label for="admin_notes" class="block text-sm font-medium text-gray-700 mb-2">Admin Notes (Optional)</label>
                    <textarea name="admin_notes" id="admin_notes" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                              placeholder="Add notes about this status change..."></textarea>
                </div>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                <button type="button" onclick="closeStatusModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                    Cancel
                </button>
                <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                    Update Status
                </button>
            </div>
        </form>
    </div>
</div>

<?php $__env->startPush('styles'); ?>
<style>
@media print {
    .print-hidden {
        display: none !important;
    }

    .bg-white {
        border: none !important;
        box-shadow: none !important;
    }

    .p-6 {
        padding: 0 !important;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
let currentApplicationId = <?php echo e($projectApplication->id); ?>;

function showStatusModal(applicationId, currentStatus) {
    document.getElementById('new_status').value = currentStatus;
    document.getElementById('admin_notes').value = '';
    document.getElementById('statusModal').classList.remove('hidden');
}

function closeStatusModal() {
    document.getElementById('statusModal').classList.add('hidden');
}

function quickStatusChange(newStatus) {
    if (confirm(`Are you sure you want to change the status to "${getStatusLabel(newStatus)}"?`)) {
        updateStatus(newStatus, '');
    }
}

function getStatusLabel(status) {
    const labels = {
        'pending': 'Pending Review',
        'under_review': 'Under Review',
        'approved': 'Approved',
        'rejected': 'Rejected',
        'in_progress': 'In Progress',
        'completed': 'Completed',
        'cancelled': 'Cancelled'
    };
    return labels[status] || status;
}

function getStatusClasses(status) {
    const classes = {
        'pending': 'bg-yellow-100 text-yellow-800',
        'under_review': 'bg-blue-100 text-blue-800',
        'approved': 'bg-green-100 text-green-800',
        'rejected': 'bg-red-100 text-red-800',
        'in_progress': 'bg-purple-100 text-purple-800',
        'completed': 'bg-green-100 text-green-800',
        'cancelled': 'bg-gray-100 text-gray-800'
    };
    return classes[status] || 'bg-gray-100 text-gray-800';
}

function updateStatus(status, notes) {
    const statusData = {
        status: status,
        admin_notes: notes,
        _token: document.querySelector('meta[name="csrf-token"]').getAttribute('content')
    };

    fetch(`/admin/project-applications/${currentApplicationId}/status`, {
        method: 'PATCH',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': statusData._token
        },
        body: JSON.stringify(statusData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update the status badge
            const statusBadge = document.getElementById('current-status');
            statusBadge.textContent = data.status_label;
            statusBadge.className = `inline-flex items-center px-4 py-2 rounded-full text-lg font-medium mb-4 ${getStatusClasses(data.new_status)}`;

            // Close modal if open
            closeStatusModal();

            showAlert('success', data.message);

            // Reload page to update timeline and quick actions
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showAlert('error', data.message || 'Failed to update status');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', 'An error occurred while updating the status');
    });
}

document.getElementById('statusForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    updateStatus(formData.get('status'), formData.get('admin_notes'));
});

function confirmDelete(applicationId, title) {
    if (confirm(`Are you sure you want to delete the application "${title}"? This action cannot be undone.`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/project-applications/${applicationId}`;
        form.innerHTML = `
            <?php echo csrf_field(); ?>
            <?php echo method_field('DELETE'); ?>
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function printApplication() {
    window.print();
}

function exportApplication(format) {
    window.location.href = `/admin/project-applications/${currentApplicationId}/export/${format}`;
}

function showAlert(type, message) {
    const alertClass = type === 'success' ? 'bg-green-50 border-green-200 text-green-800' : 'bg-red-50 border-red-200 text-red-800';
    const iconClass = type === 'success' ? 'text-green-400' : 'text-red-400';
    const iconPath = type === 'success'
        ? 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z'
        : 'M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z';

    const alertHtml = `
        <div class="rounded-md border p-4 mb-4 ${alertClass}" role="alert">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 ${iconClass}" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="${iconPath}" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium">${message}</p>
                </div>
                <div class="ml-auto pl-3">
                    <div class="-mx-1.5 -my-1.5">
                        <button type="button" class="inline-flex rounded-md p-1.5 ${iconClass} hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500" onclick="this.parentElement.parentElement.parentElement.parentElement.remove()">
                            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    const container = document.querySelector('.p-6.space-y-6');
    container.insertAdjacentHTML('afterbegin', alertHtml);

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        const alert = container.querySelector('[role="alert"]');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}

// Close modal when clicking outside
document.getElementById('statusModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeStatusModal();
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\files\chisolution\resources\views/admin/project-applications/show.blade.php ENDPATH**/ ?>