<?php

namespace Tests\Feature\Facades;

use App\Facades\ImageService;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Config;
use Tests\TestCase;

class ImageServiceFacadeTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        
        // Set up test configuration
        Config::set('image.max_file_size', 10 * 1024 * 1024);
        Config::set('image.allowed_mimes', ['image/jpeg', 'image/png', 'image/gif', 'image/webp']);
        Config::set('image.allowed_extensions', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
        Config::set('image.enable_optimization', true);
        Config::set('image.enable_webp_conversion', true);
        Config::set('image.virus_scan.enabled', false);
        Config::set('image.storage.disk', 'testing');
        Config::set('image.storage.path', 'images');
        Config::set('image.storage.temp_path', 'temp/images');
        Config::set('image.logging.enabled', false);
        
        Config::set('filesystems.disks.testing', [
            'driver' => 'local',
            'root' => storage_path('app/testing'),
        ]);
        
        Storage::fake('testing');
    }
    
    protected function tearDown(): void
    {
        Storage::disk('testing')->deleteDirectory('');
        parent::tearDown();
    }
    
    /** @test */
    public function facade_can_process_uploaded_image()
    {
        $file = UploadedFile::fake()->image('facade-test.jpg', 400, 300)->size(512);
        
        $result = ImageService::processUploadedImage($file, [
            'subdirectory' => 'facade-test',
            'create_variants' => false,
            'create_webp' => false
        ]);
        
        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('original_path', $result);
    }
    
    /** @test */
    public function facade_can_quick_upload()
    {
        $file = UploadedFile::fake()->image('facade-quick.jpg', 400, 300)->size(512);
        
        $result = ImageService::quickUpload($file, 'facade-quick-test');
        
        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('original_path', $result);
        $this->assertNotNull($result['webp_path']);
        $this->assertEmpty($result['variants']);
    }
    
    /** @test */
    public function facade_can_full_upload()
    {
        $file = UploadedFile::fake()->image('facade-full.jpg', 800, 600)->size(1024);
        
        $result = ImageService::fullUpload($file, 'facade-full-test');
        
        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('original_path', $result);
        $this->assertNotNull($result['webp_path']);
        $this->assertNotEmpty($result['variants']);
    }
    
    /** @test */
    public function facade_can_validate_image_file()
    {
        $file = UploadedFile::fake()->image('facade-validate.jpg', 400, 300)->size(512);
        
        $result = ImageService::validateImageFile($file);
        
        $this->assertTrue($result['valid']);
        $this->assertEmpty($result['errors']);
        $this->assertArrayHasKey('file_info', $result);
    }
    
    /** @test */
    public function facade_can_sanitize_filename()
    {
        $dangerous = '../../../etc/passwd.jpg';
        $safe = ImageService::sanitizeFilename($dangerous);
        
        $this->assertStringNotContainsString('..', $safe);
        $this->assertStringNotContainsString('/', $safe);
        $this->assertStringContainsString('.jpg', $safe);
    }
    
    /** @test */
    public function facade_can_scan_for_viruses()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_file');
        file_put_contents($tempPath, 'clean content');
        
        $result = ImageService::scanForViruses($tempPath);
        
        $this->assertTrue($result['clean']);
        
        unlink($tempPath);
    }
    
    /** @test */
    public function facade_can_get_image_url()
    {
        Storage::disk('testing')->put('facade-url-test.jpg', 'fake content');
        
        $url = ImageService::getImageUrl('facade-url-test.jpg');
        
        $this->assertIsString($url);
        $this->assertStringContainsString('facade-url-test.jpg', $url);
    }
    
    /** @test */
    public function facade_can_get_image_info()
    {
        // Create a test image
        $tempPath = tempnam(sys_get_temp_dir(), 'test_image');
        $image = imagecreate(200, 150);
        imagejpeg($image, $tempPath);
        imagedestroy($image);
        
        $info = ImageService::getImageInfo($tempPath);
        
        $this->assertTrue($info['exists']);
        $this->assertEquals(200, $info['width']);
        $this->assertEquals(150, $info['height']);
        
        unlink($tempPath);
    }
    
    /** @test */
    public function facade_can_optimize_image()
    {
        // Create a test image
        $tempPath = tempnam(sys_get_temp_dir(), 'test_image');
        $image = imagecreate(100, 100);
        imagejpeg($image, $tempPath);
        imagedestroy($image);
        
        $result = ImageService::optimizeImage($tempPath);
        
        $this->assertTrue($result);
        
        unlink($tempPath);
    }
    
    /** @test */
    public function facade_can_resize_image()
    {
        // Create a test image
        $tempPath = tempnam(sys_get_temp_dir(), 'test_image');
        $image = imagecreate(200, 200);
        imagejpeg($image, $tempPath);
        imagedestroy($image);
        
        $result = ImageService::resizeImage($tempPath, 100, 100);
        
        $this->assertTrue($result);
        
        unlink($tempPath);
    }
    
    /** @test */
    public function facade_can_convert_to_webp()
    {
        // Create a test image
        $tempPath = tempnam(sys_get_temp_dir(), 'test_image');
        $image = imagecreate(100, 100);
        imagejpeg($image, $tempPath);
        imagedestroy($image);
        
        $webpPath = ImageService::convertToWebP($tempPath);
        
        $this->assertNotNull($webpPath);
        $this->assertStringContainsString('.webp', $webpPath);
        
        unlink($tempPath);
        if (file_exists($webpPath)) {
            unlink($webpPath);
        }
    }
    
    /** @test */
    public function facade_can_delete_image()
    {
        // Create a test file in storage
        Storage::disk('testing')->put('facade-delete-test.jpg', 'fake content');
        
        $deleted = ImageService::deleteImage('facade-delete-test.jpg');
        
        $this->assertTrue($deleted);
        $this->assertFalse(Storage::disk('testing')->exists('facade-delete-test.jpg'));
    }
    
    /** @test */
    public function facade_handles_errors_gracefully()
    {
        $file = UploadedFile::fake()->create('document.pdf', 1024, 'application/pdf');
        
        $result = ImageService::processUploadedImage($file);
        
        $this->assertFalse($result['success']);
        $this->assertArrayHasKey('errors', $result);
        $this->assertNotEmpty($result['errors']);
    }
    
    /** @test */
    public function facade_returns_consistent_response_structure()
    {
        $file = UploadedFile::fake()->image('structure-test.jpg', 400, 300)->size(512);
        
        $result = ImageService::processUploadedImage($file);
        
        // Check required keys exist
        $this->assertArrayHasKey('success', $result);
        $this->assertIsBool($result['success']);
        
        if ($result['success']) {
            $this->assertArrayHasKey('original_path', $result);
            $this->assertArrayHasKey('webp_path', $result);
            $this->assertArrayHasKey('variants', $result);
            $this->assertArrayHasKey('file_info', $result);
            $this->assertArrayHasKey('processing_time_ms', $result);
        } else {
            $this->assertArrayHasKey('errors', $result);
            $this->assertIsArray($result['errors']);
        }
    }
}
