@props([
    'label' => null,
    'name' => null,
    'required' => false,
    'placeholder' => 'Select an option',
    'help' => null,
    'error' => null,
    'value' => null,
    'options' => [],
    'multiple' => false
])

@php
$selectClasses = 'w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200';

if ($error) {
    $selectClasses .= ' border-red-500 focus:ring-red-500';
}
@endphp

<div {{ $attributes->only('class') }}>
    @if($label)
        <label for="{{ $name }}" class="block text-sm font-medium text-gray-700 mb-2">
            {{ $label }}
            @if($required)
                <span class="text-red-500">*</span>
            @endif
        </label>
    @endif

    <select @if($name) name="{{ $name }}" id="{{ $name }}" @endif
            @if($required) required @endif
            @if($multiple) multiple @endif
            {{ $attributes->except(['class', 'label', 'name', 'required', 'placeholder', 'help', 'error', 'value', 'options', 'multiple'])->merge(['class' => $selectClasses]) }}>
        
        @if(!$multiple && $placeholder)
            <option value="">{{ $placeholder }}</option>
        @endif

        @if(is_array($options) && count($options) > 0)
            @foreach($options as $optionValue => $optionLabel)
                <option value="{{ $optionValue }}" 
                        @if($multiple && is_array($value) && in_array($optionValue, $value)) selected 
                        @elseif(!$multiple && $value == $optionValue) selected 
                        @endif>
                    {{ $optionLabel }}
                </option>
            @endforeach
        @else
            {{ $slot }}
        @endif
    </select>

    @if($help && !$error)
        <p class="mt-1 text-xs text-gray-500">{{ $help }}</p>
    @endif

    @if($error)
        <p class="mt-1 text-sm text-red-600">{{ $error }}</p>
    @endif
</div>
