<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class PermissionMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @param  string  $resource
     * @param  string  $action
     */
    public function handle(Request $request, Closure $next, string $resource, string $action): Response
    {
        if (!$request->user()) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'UNAUTHENTICATED',
                    'message' => 'Authentication required.',
                    'timestamp' => now()->toISOString()
                ]
            ], 401);
        }

        $user = $request->user();

        // Check if user is active and not deleted
        if (!$user->is_active || $user->is_deleted) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'ACCOUNT_INACTIVE',
                    'message' => 'Your account is inactive.',
                    'timestamp' => now()->toISOString()
                ]
            ], 403);
        }

        // Check if user has required permission
        if (!$user->hasPermission($resource, $action)) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'INSUFFICIENT_PERMISSIONS',
                    'message' => "You do not have permission to {$action} {$resource}.",
                    'timestamp' => now()->toISOString()
                ]
            ], 403);
        }

        return $next($request);
    }
}
