<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Seed core system data
        $this->call([
            RoleSeeder::class,
            LanguageSeeder::class,
            CurrencySeeder::class,
            ProjectSeeder::class,
        ]);

        // Create default admin user
        $adminRole = \DB::table('roles')->where('name', 'admin')->first();

        if ($adminRole) {
            \DB::table('users')->insert([
                'uuid' => \Str::uuid(),
                'first_name' => 'Admin',
                'last_name' => 'User',
                'email' => '<EMAIL>',
                'email_verified_at' => now(),
                'password' => \Hash::make('password'),
                'phone' => '+27123456789',
                'role_id' => $adminRole->id,
                'is_active' => true,
                'is_deleted' => false,
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }
    }
}
