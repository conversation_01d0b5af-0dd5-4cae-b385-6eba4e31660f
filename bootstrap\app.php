<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withProviders([
        App\Providers\ImageServiceProvider::class,
        App\Providers\FileServiceProvider::class,
    ])
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        $middleware->web(append: [
            \App\Http\Middleware\LocalizationMiddleware::class,
        ]);

        $middleware->alias([
            'auth' => \App\Http\Middleware\Authenticate::class,
            'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
            'role' => \App\Http\Middleware\RoleMiddleware::class,
            'permission' => \App\Http\Middleware\PermissionMiddleware::class,
            'localization' => \App\Http\Middleware\LocalizationMiddleware::class,
            'activity.log' => \App\Http\Middleware\ActivityLoggingMiddleware::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        // Handle CSRF token mismatch with user-friendly message
        $exceptions->render(function (\Symfony\Component\HttpKernel\Exception\HttpException $e, $request) {
            if ($e->getStatusCode() === 419) {
                $message = 'CSRF token mismatch. Please refresh the page and try again.';

                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => $message,
                        'error' => [
                            'code' => 'CSRF_TOKEN_MISMATCH',
                            'message' => $message,
                            'action' => 'refresh_page',
                            'timestamp' => now()->toISOString()
                        ]
                    ], 419);
                }

                // For non-AJAX requests, redirect back with error
                return redirect()->back()
                    ->withInput()
                    ->withErrors(['csrf' => $message]);
            }
        });
    })->create();
