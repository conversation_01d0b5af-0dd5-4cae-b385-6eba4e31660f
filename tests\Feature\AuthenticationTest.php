<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Role;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class AuthenticationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Only seed roles if they don't exist
        if (!\App\Models\Role::exists()) {
            $this->artisan('db:seed', ['--class' => 'RoleSeeder']);
        }
    }

    /** @test */
    public function user_can_register_successfully()
    {
        $userData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'Password123!',
            'password_confirmation' => 'Password123!',
            'phone' => '+1234567890',
            'terms' => true,
        ];

        $response = $this->post(route('register'), $userData);

        // Check if user was created
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'first_name' => '<PERSON>',
            'last_name' => 'Doe',
        ]);

        // Check if user is authenticated
        $this->assertAuthenticated();

        // Check if user has customer role by default
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertEquals('customer', $user->role->name);

        // Check redirect to dashboard
        $response->assertRedirect(route('dashboard'));
    }

    /** @test */
    public function user_can_login_successfully()
    {
        // Create a test user with a real-looking email
        $customerRole = Role::where('name', 'customer')->first();
        $user = User::factory()->create([
            'email' => '<EMAIL>', // Use a real domain for DNS validation
            'role_id' => $customerRole->id,
            'is_active' => true,
            'is_deleted' => false,
        ]);

        // Check user was created correctly
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'is_active' => true,
            'is_deleted' => false,
        ]);

        $loginData = [
            'email' => '<EMAIL>',
            'password' => 'password', // Default factory password
        ];

        $response = $this->post(route('login'), $loginData);

        // Check if user is authenticated
        $this->assertAuthenticated();
        $this->assertEquals($user->id, auth()->id());

        // Check redirect to dashboard
        $response->assertRedirect(route('dashboard'));
    }

    /** @test */
    public function admin_user_redirects_to_admin_dashboard()
    {
        // Create an admin user with unique email
        $adminRole = Role::where('name', 'admin')->first();
        $admin = User::factory()->create([
            'email' => 'admin-test-' . time() . '@gmail.com',
            'role_id' => $adminRole->id,
        ]);

        $loginData = [
            'email' => $admin->email,
            'password' => 'password', // Default factory password
        ];

        $response = $this->post(route('login'), $loginData);

        // Check if user is authenticated
        $this->assertAuthenticated();

        // Check redirect to admin dashboard
        $response->assertRedirect(route('admin.dashboard'));
    }

    /** @test */
    public function staff_user_redirects_to_admin_dashboard()
    {
        // Create a staff user
        $staffRole = Role::where('name', 'staff')->first();
        $staff = User::factory()->create([
            'email' => '<EMAIL>',
            'role_id' => $staffRole->id,
        ]);

        $loginData = [
            'email' => '<EMAIL>',
            'password' => 'password', // Default factory password
        ];

        $response = $this->post(route('login'), $loginData);

        // Check if user is authenticated
        $this->assertAuthenticated();

        // Check redirect to admin dashboard
        $response->assertRedirect(route('admin.dashboard'));
    }

    /** @test */
    public function client_user_redirects_to_customer_dashboard()
    {
        // Create a client user
        $clientRole = Role::where('name', 'client')->first();
        $client = User::factory()->create([
            'email' => '<EMAIL>',
            'role_id' => $clientRole->id,
        ]);

        $loginData = [
            'email' => '<EMAIL>',
            'password' => 'password', // Default factory password
        ];

        $response = $this->post(route('login'), $loginData);

        // Check if user is authenticated
        $this->assertAuthenticated();

        // Check redirect to customer/client dashboard
        $response->assertRedirect(route('dashboard'));
    }

    /** @test */
    public function registration_fails_with_invalid_data()
    {
        $invalidData = [
            'first_name' => '',
            'last_name' => '',
            'email' => 'invalid-email',
            'password' => '123',
            'password_confirmation' => '456',
            'terms' => false,
        ];

        $response = $this->post(route('register'), $invalidData);

        // Check validation errors
        $response->assertSessionHasErrors([
            'first_name',
            'last_name', 
            'email',
            'password',
            'terms'
        ]);

        // Check user was not created
        $this->assertDatabaseMissing('users', [
            'email' => 'invalid-email',
        ]);

        // Check user is not authenticated
        $this->assertGuest();
    }

    /** @test */
    public function login_fails_with_invalid_credentials()
    {
        $invalidData = [
            'email' => '<EMAIL>',
            'password' => 'wrongpassword',
        ];

        $response = $this->post(route('login'), $invalidData);

        // Check validation errors
        $response->assertSessionHasErrors(['email']);

        // Check user is not authenticated
        $this->assertGuest();
    }

    /** @test */
    public function user_can_logout_successfully()
    {
        // Create and authenticate a user
        $user = User::factory()->create();
        $this->actingAs($user);

        $response = $this->post(route('logout'));

        // Check user is logged out
        $this->assertGuest();

        // Check redirect to home
        $response->assertRedirect(route('home'));
    }

    /** @test */
    public function database_connection_is_working()
    {
        // Test basic database operations
        $this->assertDatabaseCount('roles', 4); // admin, staff, client, customer
        
        $customerRole = Role::where('name', 'customer')->first();
        $this->assertNotNull($customerRole);
        $this->assertEquals('customer', $customerRole->name);
    }
}
