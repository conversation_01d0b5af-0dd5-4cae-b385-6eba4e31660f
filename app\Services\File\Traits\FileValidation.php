<?php

namespace App\Services\File\Traits;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Config;

trait FileValidation
{
    /**
     * Validate uploaded file
     */
    public function validateFile(UploadedFile $file): array
    {
        $errors = [];
        $fileInfo = $this->getUploadedFileInfo($file);
        
        // Check file size
        if (!$this->validateFileSize($file)) {
            $maxSize = Config::get('file.max_file_size');
            $errors[] = "File size exceeds maximum allowed size of " . $this->formatBytes($maxSize);
        }
        
        // Check MIME type
        if (!$this->validateMimeType($file)) {
            $errors[] = "Invalid file type. Allowed types: " . implode(', ', Config::get('file.allowed_mimes'));
        }
        
        // Check file extension
        if (!$this->validateExtension($file)) {
            $errors[] = "Invalid file extension. Allowed extensions: " . implode(', ', Config::get('file.allowed_extensions'));
        }
        
        // Check forbidden extensions
        if ($this->isForbiddenExtension($file)) {
            $errors[] = "File type is not allowed for security reasons";
        }
        
        // Validate file signature if enabled
        if (Config::get('file.security.check_file_signature') && !$this->validateFileSignature($file)) {
            $errors[] = "File signature validation failed - file may be corrupted or malicious";
        }
        
        // Validate file content if enabled
        if (Config::get('file.security.validate_file_content') && !$this->validateFileContent($file)) {
            $errors[] = "File content validation failed";
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'file_info' => $fileInfo,
        ];
    }
    
    /**
     * Validate file size
     */
    protected function validateFileSize(UploadedFile $file): bool
    {
        $maxSize = Config::get('file.max_file_size');
        $fileType = $this->getFileType($file);
        
        // Check type-specific size limits
        if ($fileType && isset(Config::get('file.file_types')[$fileType]['max_size'])) {
            $maxSize = Config::get('file.file_types')[$fileType]['max_size'];
        }
        
        return $file->getSize() <= $maxSize;
    }
    
    /**
     * Validate MIME type
     */
    protected function validateMimeType(UploadedFile $file): bool
    {
        $allowedMimes = Config::get('file.allowed_mimes', []);
        $fileMime = $file->getMimeType();
        
        return in_array($fileMime, $allowedMimes);
    }
    
    /**
     * Validate file extension
     */
    protected function validateExtension(UploadedFile $file): bool
    {
        $allowedExtensions = Config::get('file.allowed_extensions', []);
        $fileExtension = strtolower($file->getClientOriginalExtension());
        
        return in_array($fileExtension, $allowedExtensions);
    }
    
    /**
     * Check if extension is forbidden
     */
    protected function isForbiddenExtension(UploadedFile $file): bool
    {
        $forbiddenExtensions = Config::get('file.forbidden_extensions', []);
        $fileExtension = strtolower($file->getClientOriginalExtension());
        
        return in_array($fileExtension, $forbiddenExtensions);
    }
    
    /**
     * Validate file signature (magic bytes)
     */
    protected function validateFileSignature(UploadedFile $file): bool
    {
        $filePath = $file->getRealPath();
        $extension = strtolower($file->getClientOriginalExtension());
        
        if (!file_exists($filePath)) {
            return false;
        }
        
        $fileHandle = fopen($filePath, 'rb');
        if (!$fileHandle) {
            return false;
        }
        
        $header = fread($fileHandle, 20);
        fclose($fileHandle);
        
        return $this->checkFileSignature($header, $extension);
    }
    
    /**
     * Check file signature against known patterns
     */
    protected function checkFileSignature(string $header, string $extension): bool
    {
        $signatures = [
            'pdf' => ['%PDF'],
            'doc' => ["\xD0\xCF\x11\xE0\xA1\xB1\x1A\xE1"],
            'docx' => ['PK'],
            'xls' => ["\xD0\xCF\x11\xE0\xA1\xB1\x1A\xE1"],
            'xlsx' => ['PK'],
            'ppt' => ["\xD0\xCF\x11\xE0\xA1\xB1\x1A\xE1"],
            'pptx' => ['PK'],
            'zip' => ['PK'],
            'rar' => ['Rar!'],
            '7z' => ["7z\xBC\xAF\x27\x1C"],
            'txt' => [], // Text files don't have specific signatures
            'csv' => [], // CSV files don't have specific signatures
            'json' => [], // JSON files don't have specific signatures
            'xml' => ['<?xml', "\xEF\xBB\xBF<?xml"], // With or without BOM
        ];
        
        if (!isset($signatures[$extension])) {
            return false;
        }
        
        // Text-based files without specific signatures are allowed
        if (empty($signatures[$extension])) {
            return true;
        }
        
        foreach ($signatures[$extension] as $signature) {
            if (strpos($header, $signature) === 0) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Validate file content
     */
    protected function validateFileContent(UploadedFile $file): bool
    {
        $filePath = $file->getRealPath();
        
        if (!file_exists($filePath)) {
            return false;
        }
        
        // Check for suspicious patterns in file content
        if (Config::get('file.security.scan_content_patterns')) {
            return $this->scanForSuspiciousPatterns($filePath);
        }
        
        return true;
    }
    
    /**
     * Scan file for suspicious patterns
     */
    protected function scanForSuspiciousPatterns(string $filePath): bool
    {
        $content = file_get_contents($filePath, false, null, 0, 1024 * 1024); // Read first 1MB
        
        if ($content === false) {
            return false;
        }
        
        $suspiciousPatterns = Config::get('file.suspicious_patterns', []);
        
        foreach ($suspiciousPatterns as $category => $patterns) {
            foreach ($patterns as $pattern) {
                if (preg_match($pattern, $content)) {
                    $this->logSecurityEvent("Suspicious {$category} pattern detected in file", [
                        'file_path' => $filePath,
                        'pattern' => $pattern,
                        'category' => $category,
                    ]);
                    return false;
                }
            }
        }
        
        return true;
    }
    
    /**
     * Get file type category
     */
    protected function getFileType(UploadedFile $file): ?string
    {
        $extension = strtolower($file->getClientOriginalExtension());
        $fileTypes = Config::get('file.file_types', []);
        
        foreach ($fileTypes as $type => $config) {
            if (in_array($extension, $config['extensions'])) {
                return $type;
            }
        }
        
        return null;
    }
    
    /**
     * Get uploaded file information
     */
    protected function getUploadedFileInfo(UploadedFile $file): array
    {
        return [
            'original_name' => $file->getClientOriginalName(),
            'size' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
            'extension' => $file->getClientOriginalExtension(),
            'type_category' => $this->getFileType($file),
            'hash' => hash_file('sha256', $file->getRealPath()),
        ];
    }
    
    /**
     * Format bytes to human readable format
     */
    protected function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= (1 << (10 * $pow));

        return round($bytes, 2) . ' ' . $units[$pow];
    }


}
