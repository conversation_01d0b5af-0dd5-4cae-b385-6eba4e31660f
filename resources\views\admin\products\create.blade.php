@extends('layouts.dashboard')

@section('title', 'Create Product - Admin Dashboard')
@section('page_title', 'Create Product')

@section('content')
<div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Create Product</h1>
            <p class="text-gray-600">Add a new product to your catalog</p>
        </div>
        <a href="{{ route('admin.products.index') }}" 
           class="inline-flex items-center px-4 py-2 bg-neutral-600 text-white rounded-lg hover:bg-neutral-700 transition-colors duration-200">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
            </svg>
            Back to Products
        </a>
    </div>

    <!-- Multi-Step Form -->
    <div class="bg-white rounded-xl shadow-soft border border-neutral-100">
        <!-- Tab Navigation -->
        <div class="border-b border-neutral-200">
            <nav class="flex space-x-8 px-6" aria-label="Tabs">
                <button type="button" 
                        class="tab-button active py-4 px-1 border-b-2 border-primary-500 font-medium text-sm text-primary-600"
                        data-tab="basic">
                    <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center">
                            <span class="text-xs font-semibold text-primary-600">1</span>
                        </div>
                        <span>Basic Info</span>
                    </div>
                </button>
                
                <button type="button" 
                        class="tab-button py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700"
                        data-tab="pricing">
                    <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center">
                            <span class="text-xs font-semibold text-gray-500">2</span>
                        </div>
                        <span>Pricing & Inventory</span>
                    </div>
                </button>
                
                <button type="button" 
                        class="tab-button py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700"
                        data-tab="images">
                    <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center">
                            <span class="text-xs font-semibold text-gray-500">3</span>
                        </div>
                        <span>Images</span>
                    </div>
                </button>
                
                <button type="button" 
                        class="tab-button py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700"
                        data-tab="specifications">
                    <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center">
                            <span class="text-xs font-semibold text-gray-500">4</span>
                        </div>
                        <span>Specifications</span>
                    </div>
                </button>
                
                <button type="button" 
                        class="tab-button py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700"
                        data-tab="seo">
                    <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center">
                            <span class="text-xs font-semibold text-gray-500">5</span>
                        </div>
                        <span>SEO & Settings</span>
                    </div>
                </button>
            </nav>
        </div>

        <!-- Form Content -->
        <form method="POST" action="{{ route('admin.products.store') }}" enctype="multipart/form-data" id="product-form">
            @csrf
            
            <!-- Step 1: Basic Information -->
            <div class="tab-content active" id="basic-tab">
                <div class="p-6 space-y-6">
                    <h3 class="text-lg font-semibold text-gray-900">Basic Information</h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Product Name -->
                        <div class="md:col-span-2">
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                                Product Name <span class="text-red-500">*</span>
                            </label>
                            <input type="text"
                                   id="name"
                                   name="name"
                                   value="{{ old('name') }}"
                                   class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('name') border-red-500 @enderror"
                                   placeholder="Enter product name"
                                   required>
                            @error('name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- SKU -->
                        <div>
                            <label for="sku" class="block text-sm font-medium text-gray-700 mb-2">
                                SKU <span class="text-red-500">*</span>
                            </label>
                            <input type="text"
                                   id="sku"
                                   name="sku"
                                   value="{{ old('sku') }}"
                                   class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('sku') border-red-500 @enderror"
                                   placeholder="Enter SKU"
                                   required>
                            @error('sku')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Slug -->
                        <div>
                            <label for="slug" class="block text-sm font-medium text-gray-700 mb-2">
                                URL Slug
                            </label>
                            <input type="text"
                                   id="slug"
                                   name="slug"
                                   value="{{ old('slug') }}"
                                   class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('slug') border-red-500 @enderror"
                                   placeholder="auto-generated-from-name">
                            <p class="mt-1 text-xs text-gray-500">Leave empty to auto-generate from name</p>
                            @error('slug')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Brand -->
                        <div>
                            <label for="brand" class="block text-sm font-medium text-gray-700 mb-2">
                                Brand
                            </label>
                            <input type="text"
                                   id="brand"
                                   name="brand"
                                   value="{{ old('brand') }}"
                                   class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('brand') border-red-500 @enderror"
                                   placeholder="Enter brand name">
                            @error('brand')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Model Number -->
                        <div>
                            <label for="model_number" class="block text-sm font-medium text-gray-700 mb-2">
                                Model Number
                            </label>
                            <input type="text"
                                   id="model_number"
                                   name="model_number"
                                   value="{{ old('model_number') }}"
                                   class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('model_number') border-red-500 @enderror"
                                   placeholder="Enter model number">
                            @error('model_number')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Short Description -->
                    <div>
                        <label for="short_description" class="block text-sm font-medium text-gray-700 mb-2">
                            Short Description
                        </label>
                        <textarea id="short_description"
                                  name="short_description"
                                  class="tinymce-editor @error('short_description') border-red-500 @enderror"
                                  placeholder="Brief product description for listings">{{ old('short_description') }}</textarea>
                        @error('short_description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Full Description -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                            Full Description
                        </label>
                        <textarea id="description"
                                  name="description"
                                  class="tinymce-editor @error('description') border-red-500 @enderror"
                                  placeholder="Detailed product description">{{ old('description') }}</textarea>
                        @error('description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Categories -->
                    <div>
                        <label for="categories" class="block text-sm font-medium text-gray-700 mb-2">
                            Categories
                        </label>
                        <select id="categories"
                                name="categories[]"
                                multiple
                                class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('categories') border-red-500 @enderror">
                            @foreach($categories as $category)
                                <option value="{{ $category->id }}" {{ in_array($category->id, old('categories', [])) ? 'selected' : '' }}>
                                    {{ $category->name }}
                                </option>
                            @endforeach
                        </select>
                        <p class="mt-1 text-xs text-gray-500">Hold Ctrl/Cmd to select multiple categories</p>
                        @error('categories')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Step 2: Pricing & Inventory -->
            <div class="tab-content" id="pricing-tab">
                <div class="p-6 space-y-6">
                    <h3 class="text-lg font-semibold text-gray-900">Pricing & Inventory</h3>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Price -->
                        <div>
                            <label for="price" class="block text-sm font-medium text-gray-700 mb-2">
                                Price <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <span class="absolute left-3 top-2 text-gray-500">R</span>
                                <input type="number"
                                       id="price"
                                       name="price"
                                       value="{{ old('price') }}"
                                       step="0.01"
                                       min="0"
                                       class="w-full pl-8 pr-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('price') border-red-500 @enderror"
                                       placeholder="0.00"
                                       required>
                            </div>
                            @error('price')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Compare Price -->
                        <div>
                            <label for="compare_price" class="block text-sm font-medium text-gray-700 mb-2">
                                Compare Price
                            </label>
                            <div class="relative">
                                <span class="absolute left-3 top-2 text-gray-500">R</span>
                                <input type="number"
                                       id="compare_price"
                                       name="compare_price"
                                       value="{{ old('compare_price') }}"
                                       step="0.01"
                                       min="0"
                                       class="w-full pl-8 pr-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('compare_price') border-red-500 @enderror"
                                       placeholder="0.00">
                            </div>
                            <p class="mt-1 text-xs text-gray-500">Original price for showing discounts</p>
                            @error('compare_price')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Cost Price -->
                        <div>
                            <label for="cost_price" class="block text-sm font-medium text-gray-700 mb-2">
                                Cost Price
                            </label>
                            <div class="relative">
                                <span class="absolute left-3 top-2 text-gray-500">R</span>
                                <input type="number"
                                       id="cost_price"
                                       name="cost_price"
                                       value="{{ old('cost_price') }}"
                                       step="0.01"
                                       min="0"
                                       class="w-full pl-8 pr-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('cost_price') border-red-500 @enderror"
                                       placeholder="0.00">
                            </div>
                            <p class="mt-1 text-xs text-gray-500">Your cost for this product</p>
                            @error('cost_price')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Inventory Tracking -->
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <input type="checkbox"
                                   id="track_inventory"
                                   name="track_inventory"
                                   value="1"
                                   {{ old('track_inventory', true) ? 'checked' : '' }}
                                   class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-neutral-300 rounded">
                            <label for="track_inventory" class="ml-2 block text-sm text-gray-900">
                                Track inventory for this product
                            </label>
                        </div>

                        <div id="inventory-fields" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Inventory Quantity -->
                            <div>
                                <label for="inventory_quantity" class="block text-sm font-medium text-gray-700 mb-2">
                                    Inventory Quantity
                                </label>
                                <input type="number"
                                       id="inventory_quantity"
                                       name="inventory_quantity"
                                       value="{{ old('inventory_quantity', 0) }}"
                                       min="0"
                                       class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('inventory_quantity') border-red-500 @enderror"
                                       placeholder="0">
                                @error('inventory_quantity')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Low Stock Threshold -->
                            <div>
                                <label for="low_stock_threshold" class="block text-sm font-medium text-gray-700 mb-2">
                                    Low Stock Threshold
                                </label>
                                <input type="number"
                                       id="low_stock_threshold"
                                       name="low_stock_threshold"
                                       value="{{ old('low_stock_threshold', 5) }}"
                                       min="0"
                                       class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('low_stock_threshold') border-red-500 @enderror"
                                       placeholder="5">
                                <p class="mt-1 text-xs text-gray-500">Alert when stock falls below this number</p>
                                @error('low_stock_threshold')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Physical Properties -->
                    <div class="space-y-4">
                        <h4 class="text-md font-medium text-gray-900">Physical Properties</h4>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Weight -->
                            <div>
                                <label for="weight" class="block text-sm font-medium text-gray-700 mb-2">
                                    Weight (grams)
                                </label>
                                <input type="number"
                                       id="weight"
                                       name="weight"
                                       value="{{ old('weight') }}"
                                       step="0.01"
                                       min="0"
                                       class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('weight') border-red-500 @enderror"
                                       placeholder="0.00">
                                @error('weight')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Barcode -->
                            <div>
                                <label for="barcode" class="block text-sm font-medium text-gray-700 mb-2">
                                    Barcode
                                </label>
                                <input type="text"
                                       id="barcode"
                                       name="barcode"
                                       value="{{ old('barcode') }}"
                                       class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('barcode') border-red-500 @enderror"
                                       placeholder="Enter barcode">
                                @error('barcode')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 3: Images -->
            <div class="tab-content" id="images-tab">
                <div class="p-6 space-y-6">
                    <h3 class="text-lg font-semibold text-gray-900">Product Images</h3>

                    <!-- Featured Image -->
                    <div>
                        <label for="featured_image" class="block text-sm font-medium text-gray-700 mb-2">
                            Featured Image <span class="text-red-500">*</span>
                        </label>
                        <div class="border-2 border-dashed border-neutral-300 rounded-lg p-6 text-center hover:border-primary-400 transition-colors duration-200">
                            <div id="featured-image-preview" class="hidden mb-4">
                                <img id="featured-preview-img" src="" alt="Featured image preview" class="mx-auto max-h-48 rounded-lg">
                                <button type="button" id="remove-featured" class="mt-2 text-sm text-red-600 hover:text-red-800">Remove</button>
                            </div>
                            <div id="featured-upload-area">
                                <svg class="mx-auto h-12 w-12 text-neutral-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                <div class="mt-4">
                                    <label for="featured_image" class="cursor-pointer">
                                        <span class="mt-2 block text-sm font-medium text-gray-900">
                                            Upload featured image
                                        </span>
                                        <span class="mt-1 block text-sm text-gray-500">
                                            PNG, JPG, GIF up to 2MB
                                        </span>
                                    </label>
                                    <input id="featured_image" name="featured_image" type="file" accept="image/*" class="sr-only">
                                </div>
                            </div>
                        </div>
                        @error('featured_image')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Gallery Images -->
                    <div>
                        <label for="gallery" class="block text-sm font-medium text-gray-700 mb-2">
                            Gallery Images
                        </label>
                        <div class="border-2 border-dashed border-neutral-300 rounded-lg p-6 text-center hover:border-primary-400 transition-colors duration-200">
                            <div id="gallery-preview" class="hidden mb-4">
                                <div id="gallery-images" class="grid grid-cols-2 md:grid-cols-4 gap-4"></div>
                                <button type="button" id="clear-gallery" class="mt-4 text-sm text-red-600 hover:text-red-800">Clear All</button>
                            </div>
                            <div id="gallery-upload-area">
                                <svg class="mx-auto h-12 w-12 text-neutral-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                <div class="mt-4">
                                    <label for="gallery" class="cursor-pointer">
                                        <span class="mt-2 block text-sm font-medium text-gray-900">
                                            Upload gallery images
                                        </span>
                                        <span class="mt-1 block text-sm text-gray-500">
                                            Select multiple images (PNG, JPG, GIF up to 2MB each)
                                        </span>
                                    </label>
                                    <input id="gallery" name="gallery[]" type="file" accept="image/*" multiple class="sr-only">
                                </div>
                            </div>
                        </div>
                        @error('gallery')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Step 4: Specifications -->
            <div class="tab-content" id="specifications-tab">
                <div class="p-6 space-y-6">
                    <h3 class="text-lg font-semibold text-gray-900">Product Specifications</h3>

                    <!-- Dimensions -->
                    <div>
                        <h4 class="text-md font-medium text-gray-900 mb-4">Dimensions</h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label for="length" class="block text-sm font-medium text-gray-700 mb-2">Length (cm)</label>
                                <input type="number"
                                       id="length"
                                       name="dimensions[length]"
                                       value="{{ old('dimensions.length') }}"
                                       step="0.01"
                                       min="0"
                                       class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                       placeholder="0.00">
                            </div>
                            <div>
                                <label for="width" class="block text-sm font-medium text-gray-700 mb-2">Width (cm)</label>
                                <input type="number"
                                       id="width"
                                       name="dimensions[width]"
                                       value="{{ old('dimensions.width') }}"
                                       step="0.01"
                                       min="0"
                                       class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                       placeholder="0.00">
                            </div>
                            <div>
                                <label for="height" class="block text-sm font-medium text-gray-700 mb-2">Height (cm)</label>
                                <input type="number"
                                       id="height"
                                       name="dimensions[height]"
                                       value="{{ old('dimensions.height') }}"
                                       step="0.01"
                                       min="0"
                                       class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                       placeholder="0.00">
                            </div>
                        </div>
                    </div>

                    <!-- Product Variants -->
                    <div>
                        <div class="flex items-center justify-between mb-4">
                            <h4 class="text-md font-medium text-gray-900">Product Variants</h4>
                            <button type="button" id="add-variant" class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200">
                                Add Variant
                            </button>
                        </div>
                        <div id="variants-container" class="space-y-4">
                            <!-- Variants will be added here dynamically -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 5: SEO & Settings -->
            <div class="tab-content" id="seo-tab">
                <div class="p-6 space-y-6">
                    <h3 class="text-lg font-semibold text-gray-900">SEO & Settings</h3>

                    <!-- SEO Settings -->
                    <div class="space-y-6">
                        <h4 class="text-md font-medium text-gray-900">Search Engine Optimization</h4>

                        <!-- Meta Title -->
                        <div>
                            <label for="meta_title" class="block text-sm font-medium text-gray-700 mb-2">
                                Meta Title
                            </label>
                            <input type="text"
                                   id="meta_title"
                                   name="meta_title"
                                   value="{{ old('meta_title') }}"
                                   maxlength="255"
                                   class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                   placeholder="SEO title for search engines">
                            <p class="mt-1 text-xs text-gray-500">Recommended: 50-60 characters</p>
                        </div>

                        <!-- Meta Description -->
                        <div>
                            <label for="meta_description" class="block text-sm font-medium text-gray-700 mb-2">
                                Meta Description
                            </label>
                            <textarea id="meta_description"
                                      name="meta_description"
                                      rows="3"
                                      maxlength="160"
                                      class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                      placeholder="Brief description for search engines">{{ old('meta_description') }}</textarea>
                            <p class="mt-1 text-xs text-gray-500">Recommended: 150-160 characters</p>
                        </div>

                        <!-- Meta Keywords -->
                        <div>
                            <label for="meta_keywords" class="block text-sm font-medium text-gray-700 mb-2">
                                Meta Keywords
                            </label>
                            <input type="text"
                                   id="meta_keywords"
                                   name="meta_keywords"
                                   value="{{ old('meta_keywords') }}"
                                   class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                   placeholder="keyword1, keyword2, keyword3">
                            <p class="mt-1 text-xs text-gray-500">Separate keywords with commas</p>
                        </div>
                    </div>

                    <!-- Product Settings -->
                    <div class="space-y-4">
                        <h4 class="text-md font-medium text-gray-900">Product Settings</h4>

                        <div class="space-y-4">
                            <!-- Active Status -->
                            <div class="flex items-center">
                                <input type="checkbox"
                                       id="is_active"
                                       name="is_active"
                                       value="1"
                                       {{ old('is_active', true) ? 'checked' : '' }}
                                       class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-neutral-300 rounded">
                                <label for="is_active" class="ml-2 block text-sm text-gray-900">
                                    Active (visible on website)
                                </label>
                            </div>

                            <!-- Featured Product -->
                            <div class="flex items-center">
                                <input type="checkbox"
                                       id="is_featured"
                                       name="is_featured"
                                       value="1"
                                       {{ old('is_featured') ? 'checked' : '' }}
                                       class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-neutral-300 rounded">
                                <label for="is_featured" class="ml-2 block text-sm text-gray-900">
                                    Featured product (show in featured sections)
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation Buttons -->
            <div class="flex items-center justify-between px-6 py-4 bg-gray-50 border-t border-neutral-200">
                <button type="button" 
                        id="prev-btn" 
                        class="px-6 py-2 border border-neutral-300 text-neutral-700 rounded-lg hover:bg-neutral-50 transition-colors duration-200 hidden">
                    Previous
                </button>
                
                <div class="flex space-x-3">
                    <a href="{{ route('admin.products.index') }}" 
                       class="px-6 py-2 border border-neutral-300 text-neutral-700 rounded-lg hover:bg-neutral-50 transition-colors duration-200">
                        Cancel
                    </a>
                    
                    <button type="button" 
                            id="next-btn" 
                            class="px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200">
                        Next Step
                    </button>
                    
                    <button type="submit" 
                            id="submit-btn" 
                            class="px-6 py-2 bg-success-600 text-white rounded-lg hover:bg-success-700 transition-colors duration-200 hidden">
                        Create Product
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const tabs = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');
    const prevBtn = document.getElementById('prev-btn');
    const nextBtn = document.getElementById('next-btn');
    const submitBtn = document.getElementById('submit-btn');
    
    let currentStep = 0;
    const totalSteps = tabs.length;

    // Tab switching
    tabs.forEach((tab, index) => {
        tab.addEventListener('click', () => {
            if (index <= currentStep || validateCurrentStep()) {
                switchToStep(index);
            }
        });
    });

    // Navigation buttons
    nextBtn.addEventListener('click', () => {
        if (validateCurrentStep() && currentStep < totalSteps - 1) {
            switchToStep(currentStep + 1);
        }
    });

    prevBtn.addEventListener('click', () => {
        if (currentStep > 0) {
            switchToStep(currentStep - 1);
        }
    });

    function switchToStep(step) {
        // Update current step
        currentStep = step;

        // Update tabs
        tabs.forEach((tab, index) => {
            const circle = tab.querySelector('.w-6');
            const span = circle.querySelector('span');
            
            if (index < currentStep) {
                // Completed step
                tab.classList.remove('border-transparent', 'text-gray-500');
                tab.classList.add('border-green-500', 'text-green-600');
                circle.classList.remove('bg-gray-100', 'bg-primary-100');
                circle.classList.add('bg-green-100');
                span.classList.remove('text-gray-500', 'text-primary-600');
                span.classList.add('text-green-600');
            } else if (index === currentStep) {
                // Active step
                tab.classList.remove('border-transparent', 'text-gray-500', 'border-green-500', 'text-green-600');
                tab.classList.add('border-primary-500', 'text-primary-600');
                circle.classList.remove('bg-gray-100', 'bg-green-100');
                circle.classList.add('bg-primary-100');
                span.classList.remove('text-gray-500', 'text-green-600');
                span.classList.add('text-primary-600');
            } else {
                // Future step
                tab.classList.remove('border-primary-500', 'text-primary-600', 'border-green-500', 'text-green-600');
                tab.classList.add('border-transparent', 'text-gray-500');
                circle.classList.remove('bg-primary-100', 'bg-green-100');
                circle.classList.add('bg-gray-100');
                span.classList.remove('text-primary-600', 'text-green-600');
                span.classList.add('text-gray-500');
            }
        });

        // Update tab contents
        tabContents.forEach((content, index) => {
            content.classList.toggle('active', index === currentStep);
        });

        // Update navigation buttons
        prevBtn.classList.toggle('hidden', currentStep === 0);
        nextBtn.classList.toggle('hidden', currentStep === totalSteps - 1);
        submitBtn.classList.toggle('hidden', currentStep !== totalSteps - 1);
    }

    function validateCurrentStep() {
        // Basic validation for required fields in current step
        const currentTabContent = document.querySelector('.tab-content.active');
        const requiredFields = currentTabContent.querySelectorAll('[required]');
        
        for (let field of requiredFields) {
            if (!field.value.trim()) {
                field.focus();
                return false;
            }
        }
        
        return true;
    }

    // Auto-generate slug from name
    const nameInput = document.getElementById('name');
    const slugInput = document.getElementById('slug');
    
    nameInput.addEventListener('input', function() {
        if (!slugInput.value || slugInput.dataset.autoGenerated) {
            const slug = this.value
                .toLowerCase()
                .replace(/[^a-z0-9]+/g, '-')
                .replace(/^-+|-+$/g, '');
            slugInput.value = slug;
            slugInput.dataset.autoGenerated = 'true';
        }
    });
    
    slugInput.addEventListener('input', function() {
        if (this.value) {
            delete this.dataset.autoGenerated;
        }
    });

    // Image handling
    const featuredImageInput = document.getElementById('featured_image');
    const featuredPreview = document.getElementById('featured-image-preview');
    const featuredPreviewImg = document.getElementById('featured-preview-img');
    const featuredUploadArea = document.getElementById('featured-upload-area');
    const removeFeaturedBtn = document.getElementById('remove-featured');

    featuredImageInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                featuredPreviewImg.src = e.target.result;
                featuredPreview.classList.remove('hidden');
                featuredUploadArea.classList.add('hidden');
            };
            reader.readAsDataURL(file);
        }
    });

    removeFeaturedBtn.addEventListener('click', function() {
        featuredImageInput.value = '';
        featuredPreview.classList.add('hidden');
        featuredUploadArea.classList.remove('hidden');
    });

    // Gallery images handling
    const galleryInput = document.getElementById('gallery');
    const galleryPreview = document.getElementById('gallery-preview');
    const galleryImages = document.getElementById('gallery-images');
    const galleryUploadArea = document.getElementById('gallery-upload-area');
    const clearGalleryBtn = document.getElementById('clear-gallery');

    galleryInput.addEventListener('change', function(e) {
        const files = Array.from(e.target.files);
        if (files.length > 0) {
            galleryImages.innerHTML = '';
            files.forEach((file, index) => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const div = document.createElement('div');
                    div.className = 'relative';
                    div.innerHTML = `
                        <img src="${e.target.result}" alt="Gallery image ${index + 1}" class="w-full h-24 object-cover rounded-lg">
                        <button type="button" class="absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600" onclick="this.parentElement.remove()">×</button>
                    `;
                    galleryImages.appendChild(div);
                };
                reader.readAsDataURL(file);
            });
            galleryPreview.classList.remove('hidden');
            galleryUploadArea.classList.add('hidden');
        }
    });

    clearGalleryBtn.addEventListener('click', function() {
        galleryInput.value = '';
        galleryImages.innerHTML = '';
        galleryPreview.classList.add('hidden');
        galleryUploadArea.classList.remove('hidden');
    });

    // Inventory tracking toggle
    const trackInventoryCheckbox = document.getElementById('track_inventory');
    const inventoryFields = document.getElementById('inventory-fields');

    function toggleInventoryFields() {
        if (trackInventoryCheckbox.checked) {
            inventoryFields.style.display = 'grid';
            document.getElementById('inventory_quantity').required = true;
        } else {
            inventoryFields.style.display = 'none';
            document.getElementById('inventory_quantity').required = false;
        }
    }

    trackInventoryCheckbox.addEventListener('change', toggleInventoryFields);
    toggleInventoryFields(); // Initial state

    // Variant management
    const addVariantBtn = document.getElementById('add-variant');
    const variantsContainer = document.getElementById('variants-container');
    let variantCount = 0;

    addVariantBtn.addEventListener('click', function() {
        const variantHtml = `
            <div class="variant-item border border-neutral-200 rounded-lg p-4">
                <div class="flex items-center justify-between mb-4">
                    <h5 class="font-medium text-gray-900">Variant ${variantCount + 1}</h5>
                    <button type="button" class="remove-variant text-red-600 hover:text-red-800">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                        </svg>
                    </button>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Variant Name</label>
                        <input type="text" name="variants[${variantCount}][name]" class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent" placeholder="e.g., Red, Large, etc.">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">SKU</label>
                        <input type="text" name="variants[${variantCount}][sku]" class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent" placeholder="Variant SKU">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Price</label>
                        <input type="number" name="variants[${variantCount}][price]" step="0.01" min="0" class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent" placeholder="0.00">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Inventory</label>
                        <input type="number" name="variants[${variantCount}][inventory_quantity]" min="0" class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent" placeholder="0">
                    </div>
                </div>
            </div>
        `;

        variantsContainer.insertAdjacentHTML('beforeend', variantHtml);
        variantCount++;
    });

    // Remove variant
    variantsContainer.addEventListener('click', function(e) {
        if (e.target.closest('.remove-variant')) {
            e.target.closest('.variant-item').remove();
        }
    });

    // Rich text editor is automatically initialized by the dashboard layout
});
</script>

<style>
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}
</style>
@endpush
