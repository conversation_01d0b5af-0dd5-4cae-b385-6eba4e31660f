<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class ContactSubmission extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'uuid',
        'name',
        'email',
        'phone',
        'company',
        'service',
        'subject',
        'message',
        'attachments',
        'ip_address',
        'user_agent',
        'referrer',
        'is_read',
        'is_spam',
        'replied_at',
        'replied_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'attachments' => 'array',
            'is_read' => 'boolean',
            'is_spam' => 'boolean',
            'replied_at' => 'datetime',
            'uuid' => 'string',
        ];
    }

    /**
     * The attributes that should have default values.
     */
    protected $attributes = [
        'is_read' => false,
        'is_spam' => false,
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($submission) {
            if (empty($submission->uuid)) {
                $submission->uuid = Str::uuid();
            }
        });
    }

    /**
     * Get the user who replied to this submission.
     */
    public function repliedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'replied_by');
    }

    /**
     * Scope for unread submissions.
     */
    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }

    /**
     * Scope for non-spam submissions.
     */
    public function scopeNotSpam($query)
    {
        return $query->where('is_spam', false);
    }

    /**
     * Mark as read.
     */
    public function markAsRead(): void
    {
        $this->update(['is_read' => true]);
    }

    /**
     * Mark as spam.
     */
    public function markAsSpam(): void
    {
        $this->update(['is_spam' => true]);
    }

    /**
     * Mark as replied.
     */
    public function markAsReplied(User $user): void
    {
        $this->update([
            'replied_at' => now(),
            'replied_by' => $user->id,
            'is_read' => true,
        ]);
    }
}
