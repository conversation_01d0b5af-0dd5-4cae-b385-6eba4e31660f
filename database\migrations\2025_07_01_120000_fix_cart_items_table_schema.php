<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('cart_items', function (Blueprint $table) {
            // Rename variant_id to product_variant_id to match model
            $table->renameColumn('variant_id', 'product_variant_id');
            
            // Add missing total column
            $table->decimal('total', 10, 2)->after('price');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cart_items', function (Blueprint $table) {
            // Reverse the changes
            $table->renameColumn('product_variant_id', 'variant_id');
            $table->dropColumn('total');
        });
    }
};
